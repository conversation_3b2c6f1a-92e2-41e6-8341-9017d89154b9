# 项目管理系统 API 文档

## 1. API概述

### 1.1 基本信息
- **Base URL**: `http://localhost:8080/api`
- **API版本**: v1
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: JWT Token

### 1.2 通用响应格式

#### 成功响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 具体数据
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

#### 错误响应
```json
{
  "code": 400,
  "message": "请求参数错误",
  "errors": [
    {
      "field": "username",
      "message": "用户名不能为空"
    }
  ],
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 1.3 HTTP状态码
- `200` - 请求成功
- `201` - 创建成功
- `400` - 请求参数错误
- `401` - 未授权
- `403` - 禁止访问
- `404` - 资源不存在
- `500` - 服务器内部错误

## 2. 认证接口

### 2.1 用户注册

**接口地址**: `POST /api/auth/register`

**请求参数**:
```json
{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "password123",
  "confirmPassword": "password123"
}
```

**参数说明**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| username | string | 是 | 用户名，3-20字符 |
| email | string | 是 | 邮箱地址 |
| password | string | 是 | 密码，8-20字符 |
| confirmPassword | string | 是 | 确认密码 |

**响应示例**:
```json
{
  "code": 201,
  "message": "注册成功",
  "data": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "role": "USER",
    "createdAt": "2024-01-01T12:00:00Z"
  }
}
```

### 2.2 用户登录

**接口地址**: `POST /api/auth/login`

**请求参数**:
```json
{
  "username": "testuser",
  "password": "password123"
}
```

**参数说明**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| username | string | 是 | 用户名或邮箱 |
| password | string | 是 | 密码 |

**响应示例**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "tokenType": "Bearer",
    "expiresIn": 86400,
    "user": {
      "id": 1,
      "username": "testuser",
      "email": "<EMAIL>",
      "role": "USER"
    }
  }
}
```

### 2.3 用户登出

**接口地址**: `POST /api/auth/logout`

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "登出成功",
  "data": null
}
```

### 2.4 刷新Token

**接口地址**: `POST /api/auth/refresh`

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "Token刷新成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "tokenType": "Bearer",
    "expiresIn": 86400
  }
}
```

## 3. 用户管理接口

### 3.1 获取当前用户信息

**接口地址**: `GET /api/users/me`

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "role": "USER",
    "avatarUrl": "http://example.com/avatar.jpg",
    "isActive": true,
    "createdAt": "2024-01-01T12:00:00Z",
    "updatedAt": "2024-01-01T12:00:00Z"
  }
}
```

### 3.2 更新用户信息

**接口地址**: `PUT /api/users/me`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:
```json
{
  "email": "<EMAIL>",
  "avatarUrl": "http://example.com/new-avatar.jpg"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "role": "USER",
    "avatarUrl": "http://example.com/new-avatar.jpg",
    "updatedAt": "2024-01-01T13:00:00Z"
  }
}
```

### 3.3 修改密码

**接口地址**: `PUT /api/users/me/password`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:
```json
{
  "oldPassword": "oldpassword123",
  "newPassword": "newpassword123",
  "confirmPassword": "newpassword123"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "密码修改成功",
  "data": null
}
```

## 4. 项目管理接口

### 4.1 获取项目列表

**接口地址**: `GET /api/projects`

**请求头**:
```
Authorization: Bearer {token}
```

**查询参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，从0开始，默认0 |
| size | int | 否 | 每页大小，默认20 |
| status | string | 否 | 项目状态筛选 |
| keyword | string | 否 | 关键词搜索 |
| sort | string | 否 | 排序字段，默认createdAt |
| direction | string | 否 | 排序方向，asc/desc，默认desc |

**响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "content": [
      {
        "id": 1,
        "name": "项目管理系统",
        "description": "一个功能完善的项目管理系统",
        "status": "IN_PROGRESS",
        "priority": "HIGH",
        "creator": {
          "id": 1,
          "username": "admin",
          "email": "<EMAIL>"
        },
        "memberCount": 5,
        "taskCount": 20,
        "completedTaskCount": 8,
        "progress": 40,
        "startDate": "2024-01-01",
        "endDate": "2024-06-01",
        "createdAt": "2024-01-01T12:00:00Z",
        "updatedAt": "2024-01-01T12:00:00Z"
      }
    ],
    "page": 0,
    "size": 20,
    "totalElements": 1,
    "totalPages": 1
  }
}
```

### 4.2 创建项目

**接口地址**: `POST /api/projects`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:
```json
{
  "name": "新项目",
  "description": "项目描述",
  "priority": "MEDIUM",
  "startDate": "2024-01-01",
  "endDate": "2024-06-01"
}
```

**参数说明**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 是 | 项目名称，2-100字符 |
| description | string | 否 | 项目描述 |
| priority | string | 否 | 优先级：LOW/MEDIUM/HIGH/URGENT |
| startDate | string | 否 | 开始日期，格式：YYYY-MM-DD |
| endDate | string | 否 | 结束日期，格式：YYYY-MM-DD |

**响应示例**:
```json
{
  "code": 201,
  "message": "项目创建成功",
  "data": {
    "id": 2,
    "name": "新项目",
    "description": "项目描述",
    "status": "NOT_STARTED",
    "priority": "MEDIUM",
    "creator": {
      "id": 1,
      "username": "testuser",
      "email": "<EMAIL>"
    },
    "startDate": "2024-01-01",
    "endDate": "2024-06-01",
    "createdAt": "2024-01-01T12:00:00Z"
  }
}
```

### 4.3 获取项目详情

**接口地址**: `GET /api/projects/{id}`

**请求头**:
```
Authorization: Bearer {token}
```

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | long | 是 | 项目ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "name": "项目管理系统",
    "description": "一个功能完善的项目管理系统",
    "status": "IN_PROGRESS",
    "priority": "HIGH",
    "creator": {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>"
    },
    "members": [
      {
        "id": 1,
        "username": "admin",
        "email": "<EMAIL>",
        "role": "COORDINATOR",
        "joinedAt": "2024-01-01T12:00:00Z"
      }
    ],
    "tasks": [
      {
        "id": 1,
        "title": "设计数据库",
        "status": "COMPLETED",
        "priority": "HIGH",
        "assignee": {
          "id": 1,
          "username": "admin"
        },
        "dueDate": "2024-01-15T23:59:59Z"
      }
    ],
    "statistics": {
      "totalTasks": 20,
      "completedTasks": 8,
      "inProgressTasks": 10,
      "todoTasks": 2,
      "progress": 40
    },
    "startDate": "2024-01-01",
    "endDate": "2024-06-01",
    "createdAt": "2024-01-01T12:00:00Z",
    "updatedAt": "2024-01-01T12:00:00Z"
  }
}
```

### 4.4 更新项目

**接口地址**: `PUT /api/projects/{id}`

**请求头**:
```
Authorization: Bearer {token}
```

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | long | 是 | 项目ID |

**请求参数**:
```json
{
  "name": "更新后的项目名称",
  "description": "更新后的项目描述",
  "status": "IN_PROGRESS",
  "priority": "HIGH",
  "startDate": "2024-01-01",
  "endDate": "2024-07-01"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "项目更新成功",
  "data": {
    "id": 1,
    "name": "更新后的项目名称",
    "description": "更新后的项目描述",
    "status": "IN_PROGRESS",
    "priority": "HIGH",
    "updatedAt": "2024-01-01T13:00:00Z"
  }
}
```

### 4.5 删除项目

**接口地址**: `DELETE /api/projects/{id}`

**请求头**:
```
Authorization: Bearer {token}
```

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | long | 是 | 项目ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "项目删除成功",
  "data": null
}
```

### 4.6 添加项目成员

**接口地址**: `POST /api/projects/{id}/members`

**请求头**:
```
Authorization: Bearer {token}
```

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | long | 是 | 项目ID |

**请求参数**:
```json
{
  "userId": 2,
  "role": "MEMBER"
}
```

**参数说明**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | long | 是 | 用户ID |
| role | string | 否 | 角色：MEMBER/COORDINATOR，默认MEMBER |

**响应示例**:
```json
{
  "code": 201,
  "message": "成员添加成功",
  "data": {
    "id": 1,
    "user": {
      "id": 2,
      "username": "newmember",
      "email": "<EMAIL>"
    },
    "role": "MEMBER",
    "joinedAt": "2024-01-01T12:00:00Z"
  }
}
```

### 4.7 移除项目成员

**接口地址**: `DELETE /api/projects/{id}/members/{userId}`

**请求头**:
```
Authorization: Bearer {token}
```

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | long | 是 | 项目ID |
| userId | long | 是 | 用户ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "成员移除成功",
  "data": null
}
```

## 5. 任务管理接口

### 5.1 获取任务列表

**接口地址**: `GET /api/tasks`

**请求头**:
```
Authorization: Bearer {token}
```

**查询参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，从0开始，默认0 |
| size | int | 否 | 每页大小，默认20 |
| projectId | long | 否 | 项目ID筛选 |
| status | string | 否 | 任务状态筛选 |
| assigneeId | long | 否 | 指派人ID筛选 |
| priority | string | 否 | 优先级筛选 |
| keyword | string | 否 | 关键词搜索 |

**响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "content": [
      {
        "id": 1,
        "title": "设计数据库",
        "description": "设计项目管理系统的数据库结构",
        "status": "COMPLETED",
        "priority": "HIGH",
        "project": {
          "id": 1,
          "name": "项目管理系统"
        },
        "assignee": {
          "id": 1,
          "username": "admin",
          "email": "<EMAIL>"
        },
        "creator": {
          "id": 1,
          "username": "admin"
        },
        "estimatedHours": 8.0,
        "actualHours": 6.5,
        "dueDate": "2024-01-15T23:59:59Z",
        "completedAt": "2024-01-14T16:30:00Z",
        "createdAt": "2024-01-01T12:00:00Z",
        "updatedAt": "2024-01-14T16:30:00Z"
      }
    ],
    "page": 0,
    "size": 20,
    "totalElements": 1,
    "totalPages": 1
  }
}
```

### 5.2 创建任务

**接口地址**: `POST /api/tasks`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:
```json
{
  "title": "新任务",
  "description": "任务描述",
  "projectId": 1,
  "assigneeId": 2,
  "priority": "MEDIUM",
  "estimatedHours": 4.0,
  "dueDate": "2024-01-20T23:59:59Z"
}
```

**参数说明**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| title | string | 是 | 任务标题，2-200字符 |
| description | string | 否 | 任务描述 |
| projectId | long | 是 | 项目ID |
| assigneeId | long | 否 | 指派人ID |
| priority | string | 否 | 优先级：LOW/MEDIUM/HIGH/URGENT |
| estimatedHours | decimal | 否 | 预估工时 |
| dueDate | string | 否 | 截止时间，ISO 8601格式 |

**响应示例**:
```json
{
  "code": 201,
  "message": "任务创建成功",
  "data": {
    "id": 2,
    "title": "新任务",
    "description": "任务描述",
    "status": "TODO",
    "priority": "MEDIUM",
    "project": {
      "id": 1,
      "name": "项目管理系统"
    },
    "assignee": {
      "id": 2,
      "username": "developer"
    },
    "creator": {
      "id": 1,
      "username": "admin"
    },
    "estimatedHours": 4.0,
    "dueDate": "2024-01-20T23:59:59Z",
    "createdAt": "2024-01-01T12:00:00Z"
  }
}
```

### 5.3 获取任务详情

**接口地址**: `GET /api/tasks/{id}`

**请求头**:
```
Authorization: Bearer {token}
```

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | long | 是 | 任务ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "title": "设计数据库",
    "description": "设计项目管理系统的数据库结构",
    "status": "COMPLETED",
    "priority": "HIGH",
    "project": {
      "id": 1,
      "name": "项目管理系统"
    },
    "assignee": {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>"
    },
    "creator": {
      "id": 1,
      "username": "admin"
    },
    "estimatedHours": 8.0,
    "actualHours": 6.5,
    "dueDate": "2024-01-15T23:59:59Z",
    "completedAt": "2024-01-14T16:30:00Z",
    "createdAt": "2024-01-01T12:00:00Z",
    "updatedAt": "2024-01-14T16:30:00Z"
  }
}
```

### 5.4 更新任务

**接口地址**: `PUT /api/tasks/{id}`

**请求头**:
```
Authorization: Bearer {token}
```

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | long | 是 | 任务ID |

**请求参数**:
```json
{
  "title": "更新后的任务标题",
  "description": "更新后的任务描述",
  "status": "IN_PROGRESS",
  "priority": "HIGH",
  "assigneeId": 3,
  "estimatedHours": 6.0,
  "actualHours": 2.0,
  "dueDate": "2024-01-25T23:59:59Z"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "任务更新成功",
  "data": {
    "id": 1,
    "title": "更新后的任务标题",
    "description": "更新后的任务描述",
    "status": "IN_PROGRESS",
    "priority": "HIGH",
    "assignee": {
      "id": 3,
      "username": "newassignee"
    },
    "estimatedHours": 6.0,
    "actualHours": 2.0,
    "dueDate": "2024-01-25T23:59:59Z",
    "updatedAt": "2024-01-01T13:00:00Z"
  }
}
```

### 5.5 删除任务

**接口地址**: `DELETE /api/tasks/{id}`

**请求头**:
```
Authorization: Bearer {token}
```

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | long | 是 | 任务ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "任务删除成功",
  "data": null
}
```

### 5.6 更新任务状态

**接口地址**: `PATCH /api/tasks/{id}/status`

**请求头**:
```
Authorization: Bearer {token}
```

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | long | 是 | 任务ID |

**请求参数**:
```json
{
  "status": "COMPLETED",
  "actualHours": 5.5
}
```

**参数说明**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| status | string | 是 | 任务状态：TODO/IN_PROGRESS/REVIEW/COMPLETED/CANCELLED |
| actualHours | decimal | 否 | 实际工时（状态为COMPLETED时建议填写） |

**响应示例**:
```json
{
  "code": 200,
  "message": "任务状态更新成功",
  "data": {
    "id": 1,
    "status": "COMPLETED",
    "actualHours": 5.5,
    "completedAt": "2024-01-01T13:00:00Z",
    "updatedAt": "2024-01-01T13:00:00Z"
  }
}
```

## 6. 审核管理接口

### 6.1 获取审核列表

**接口地址**: `GET /api/approvals`

**请求头**:
```
Authorization: Bearer {token}
```

**查询参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，从0开始，默认0 |
| size | int | 否 | 每页大小，默认20 |
| type | string | 否 | 审核类型筛选 |
| status | string | 否 | 审核状态筛选 |
| requesterId | long | 否 | 申请人ID筛选 |

**响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "content": [
      {
        "id": 1,
        "type": "PROJECT_CREATE",
        "targetId": 1,
        "requester": {
          "id": 2,
          "username": "developer",
          "email": "<EMAIL>"
        },
        "approver": {
          "id": 1,
          "username": "admin",
          "email": "<EMAIL>"
        },
        "status": "APPROVED",
        "requestData": {
          "projectName": "新项目",
          "description": "项目描述"
        },
        "approvalComment": "项目方案合理，同意创建",
        "createdAt": "2024-01-01T12:00:00Z",
        "updatedAt": "2024-01-01T14:00:00Z"
      }
    ],
    "page": 0,
    "size": 20,
    "totalElements": 1,
    "totalPages": 1
  }
}
```

### 6.2 提交审核申请

**接口地址**: `POST /api/approvals`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:
```json
{
  "type": "PROJECT_CREATE",
  "targetId": 1,
  "requestData": {
    "projectName": "新项目",
    "description": "项目描述",
    "reason": "申请创建新项目用于团队协作"
  }
}
```

**参数说明**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| type | string | 是 | 审核类型：PROJECT_CREATE/PROJECT_UPDATE/USER_ROLE/PROJECT_JOIN |
| targetId | long | 是 | 目标对象ID |
| requestData | object | 否 | 申请相关数据 |

**响应示例**:
```json
{
  "code": 201,
  "message": "审核申请提交成功",
  "data": {
    "id": 2,
    "type": "PROJECT_CREATE",
    "targetId": 1,
    "requester": {
      "id": 2,
      "username": "developer"
    },
    "status": "PENDING",
    "requestData": {
      "projectName": "新项目",
      "description": "项目描述",
      "reason": "申请创建新项目用于团队协作"
    },
    "createdAt": "2024-01-01T12:00:00Z"
  }
}
```

### 6.3 审核申请

**接口地址**: `PUT /api/approvals/{id}/review`

**请求头**:
```
Authorization: Bearer {token}
```

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | long | 是 | 审核ID |

**请求参数**:
```json
{
  "status": "APPROVED",
  "comment": "项目方案合理，同意创建"
}
```

**参数说明**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| status | string | 是 | 审核结果：APPROVED/REJECTED |
| comment | string | 否 | 审核意见 |

**响应示例**:
```json
{
  "code": 200,
  "message": "审核完成",
  "data": {
    "id": 1,
    "type": "PROJECT_CREATE",
    "status": "APPROVED",
    "approver": {
      "id": 1,
      "username": "admin"
    },
    "approvalComment": "项目方案合理，同意创建",
    "updatedAt": "2024-01-01T14:00:00Z"
  }
}
```

## 7. 统计接口

### 7.1 获取仪表板统计

**接口地址**: `GET /api/dashboard/statistics`

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "projectStatistics": {
      "totalProjects": 10,
      "activeProjects": 6,
      "completedProjects": 3,
      "pausedProjects": 1
    },
    "taskStatistics": {
      "totalTasks": 50,
      "todoTasks": 15,
      "inProgressTasks": 20,
      "completedTasks": 12,
      "overdueTasks": 3
    },
    "userStatistics": {
      "totalUsers": 25,
      "activeUsers": 20,
      "projectManagers": 5
    },
    "recentActivities": [
      {
        "id": 1,
        "type": "TASK_COMPLETED",
        "description": "用户 developer 完成了任务 '设计数据库'",
        "user": {
          "id": 2,
          "username": "developer"
        },
        "createdAt": "2024-01-01T14:00:00Z"
      }
    ]
  }
}
```

### 7.2 获取项目统计

**接口地址**: `GET /api/projects/{id}/statistics`

**请求头**:
```
Authorization: Bearer {token}
```

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | long | 是 | 项目ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "projectInfo": {
      "id": 1,
      "name": "项目管理系统",
      "status": "IN_PROGRESS",
      "progress": 65
    },
    "taskStatistics": {
      "totalTasks": 20,
      "todoTasks": 3,
      "inProgressTasks": 8,
      "reviewTasks": 2,
      "completedTasks": 7
    },
    "memberStatistics": {
      "totalMembers": 5,
      "activeMembers": 4
    },
    "timeStatistics": {
      "totalEstimatedHours": 160.0,
      "totalActualHours": 95.5,
      "remainingHours": 64.5
    },
    "progressChart": [
      {
        "date": "2024-01-01",
        "completedTasks": 2,
        "totalTasks": 20
      },
      {
        "date": "2024-01-02",
        "completedTasks": 3,
        "totalTasks": 20
      }
    ]
  }
}
```

## 8. 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 401 | 未授权，需要登录 |
| 403 | 禁止访问，权限不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突，如用户名已存在 |
| 422 | 请求参数验证失败 |
| 500 | 服务器内部错误 |

## 9. 使用示例

### 9.1 完整的用户注册登录流程

```javascript
// 1. 用户注册
const registerResponse = await fetch('/api/auth/register', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    username: 'testuser',
    email: '<EMAIL>',
    password: 'password123',
    confirmPassword: 'password123'
  })
});

// 2. 用户登录
const loginResponse = await fetch('/api/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    username: 'testuser',
    password: 'password123'
  })
});

const loginData = await loginResponse.json();
const token = loginData.data.token;

// 3. 使用token访问受保护的接口
const projectsResponse = await fetch('/api/projects', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
```

### 9.2 创建项目和任务的流程

```javascript
// 1. 创建项目
const createProjectResponse = await fetch('/api/projects', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    name: '新项目',
    description: '项目描述',
    priority: 'HIGH',
    startDate: '2024-01-01',
    endDate: '2024-06-01'
  })
});

const projectData = await createProjectResponse.json();
const projectId = projectData.data.id;

// 2. 在项目中创建任务
const createTaskResponse = await fetch('/api/tasks', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    title: '新任务',
    description: '任务描述',
    projectId: projectId,
    priority: 'MEDIUM',
    dueDate: '2024-01-20T23:59:59Z'
  })
});
```

## 10. 版本更新记录

### v1.0.0 (2024-01-01)
- 初始版本发布
- 实现用户认证相关接口
- 实现项目管理相关接口
- 实现任务管理相关接口
- 实现审核流程相关接口
- 实现统计数据相关接口