# 项目管理系统开发文档

## 1. 开发环境搭建

### 1.1 环境要求
- **JDK**: 11 或更高版本
- **Maven**: 3.6.0 或更高版本
- **MySQL**: 8.0 或更高版本
- **Node.js**: 16.0 或更高版本（如果使用构建工具）
- **IDE**: IntelliJ IDEA 或 Eclipse
- **现代浏览器**: Chrome 90+, Firefox 88+, Safari 14+
- **Live Server**: 或其他本地服务器工具（开发时）

### 1.2 环境配置

#### 1.2.1 Java环境
```bash
# 检查Java版本
java -version

# 设置JAVA_HOME环境变量
export JAVA_HOME=/path/to/jdk
export PATH=$JAVA_HOME/bin:$PATH
```

#### 1.2.2 Maven配置
```bash
# 检查Maven版本
mvn -version

# 配置Maven镜像（可选）
# 编辑 ~/.m2/settings.xml
```

#### 1.2.3 数据库配置
```sql
-- 创建数据库
CREATE DATABASE project_management DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'pm_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON project_management.* TO 'pm_user'@'localhost';
FLUSH PRIVILEGES;
```

## 2. 项目架构

### 2.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (H5)     │    │  后端 (Spring)   │    │  数据库 (MySQL)  │
│                 │    │                 │    │                 │
│ - HTML5         │◄──►│ - Spring Boot   │◄──►│ - 用户表         │
│ - CSS3          │    │ - Spring Security│    │ - 项目表         │
│ - JavaScript    │    │ - Spring Data   │    │ - 任务表         │
│ - Bootstrap     │    │ - JPA/Hibernate │    │ - 审核表         │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 后端架构

#### 2.2.1 分层架构
```
┌─────────────────────────────────────────┐
│              Controller Layer            │  ← REST API 控制器
├─────────────────────────────────────────┤
│               Service Layer              │  ← 业务逻辑层
├─────────────────────────────────────────┤
│             Repository Layer             │  ← 数据访问层
├─────────────────────────────────────────┤
│               Entity Layer               │  ← 实体模型层
└─────────────────────────────────────────┘
```

#### 2.2.2 包结构
```
com.example
├── ProjectManagementApplication.java    # 启动类
├── config/                             # 配置类
│   ├── SecurityConfig.java             # 安全配置
│   ├── WebConfig.java                  # Web配置
│   └── DatabaseConfig.java             # 数据库配置
├── controller/                         # 控制器
│   ├── AuthController.java             # 认证控制器
│   ├── ProjectController.java          # 项目控制器
│   ├── TaskController.java             # 任务控制器
│   └── ApprovalController.java         # 审核控制器
├── service/                           # 服务层
│   ├── UserService.java               # 用户服务
│   ├── ProjectService.java            # 项目服务
│   ├── TaskService.java               # 任务服务
│   └── ApprovalService.java           # 审核服务
├── repository/                        # 数据访问层
│   ├── UserRepository.java            # 用户仓库
│   ├── ProjectRepository.java         # 项目仓库
│   ├── TaskRepository.java            # 任务仓库
│   └── ApprovalRepository.java        # 审核仓库
├── entity/                           # 实体类
│   ├── User.java                     # 用户实体
│   ├── Project.java                  # 项目实体
│   ├── Task.java                     # 任务实体
│   └── Approval.java                 # 审核实体
├── dto/                              # 数据传输对象
│   ├── request/                      # 请求DTO
│   └── response/                     # 响应DTO
├── security/                         # 安全相关
│   ├── JwtAuthenticationFilter.java  # JWT过滤器
│   ├── JwtTokenProvider.java         # JWT工具类
│   └── UserDetailsServiceImpl.java   # 用户详情服务
└── exception/                        # 异常处理
    ├── GlobalExceptionHandler.java   # 全局异常处理
    └── CustomException.java          # 自定义异常
```

## 3. 数据库设计

### 3.1 数据表设计

#### 3.1.1 用户表 (users)
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    role ENUM('USER', 'PROJECT_MANAGER', 'ADMIN') DEFAULT 'USER',
    avatar_url VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email)
);
```

#### 3.1.2 项目表 (projects)
```sql
CREATE TABLE projects (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    status ENUM('NOT_STARTED', 'IN_PROGRESS', 'COMPLETED', 'PAUSED') DEFAULT 'NOT_STARTED',
    priority ENUM('LOW', 'MEDIUM', 'HIGH', 'URGENT') DEFAULT 'MEDIUM',
    creator_id BIGINT NOT NULL,
    start_date DATE,
    end_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (creator_id) REFERENCES users(id),
    INDEX idx_creator (creator_id),
    INDEX idx_status (status)
);
```

#### 3.1.3 项目成员表 (project_members)
```sql
CREATE TABLE project_members (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    project_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    role ENUM('MEMBER', 'COORDINATOR') DEFAULT 'MEMBER',
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_project_user (project_id, user_id)
);
```

#### 3.1.4 任务表 (tasks)
```sql
CREATE TABLE tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    status ENUM('TODO', 'IN_PROGRESS', 'REVIEW', 'COMPLETED', 'CANCELLED') DEFAULT 'TODO',
    priority ENUM('LOW', 'MEDIUM', 'HIGH', 'URGENT') DEFAULT 'MEDIUM',
    project_id BIGINT NOT NULL,
    assignee_id BIGINT,
    creator_id BIGINT NOT NULL,
    estimated_hours DECIMAL(5,2),
    actual_hours DECIMAL(5,2),
    due_date DATETIME,
    completed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (assignee_id) REFERENCES users(id),
    FOREIGN KEY (creator_id) REFERENCES users(id),
    INDEX idx_project (project_id),
    INDEX idx_assignee (assignee_id),
    INDEX idx_status (status)
);
```

#### 3.1.5 审核表 (approvals)
```sql
CREATE TABLE approvals (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    type ENUM('PROJECT_CREATE', 'PROJECT_UPDATE', 'USER_ROLE', 'PROJECT_JOIN') NOT NULL,
    target_id BIGINT NOT NULL,
    requester_id BIGINT NOT NULL,
    approver_id BIGINT,
    status ENUM('PENDING', 'APPROVED', 'REJECTED') DEFAULT 'PENDING',
    request_data JSON,
    approval_comment TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (requester_id) REFERENCES users(id),
    FOREIGN KEY (approver_id) REFERENCES users(id),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_requester (requester_id)
);
```

### 3.2 数据库索引策略
- **主键索引**：所有表的id字段
- **唯一索引**：用户名、邮箱等唯一字段
- **复合索引**：项目成员表的(project_id, user_id)
- **查询索引**：经常用于查询条件的字段

## 4. API设计规范

### 4.1 RESTful API设计原则
- 使用HTTP动词表示操作：GET(查询)、POST(创建)、PUT(更新)、DELETE(删除)
- 使用名词表示资源：/api/projects、/api/tasks
- 使用HTTP状态码表示结果：200(成功)、201(创建)、400(错误)、401(未授权)、404(未找到)

### 4.2 API响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 具体数据
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 4.3 错误响应格式
```json
{
  "code": 400,
  "message": "请求参数错误",
  "errors": [
    {
      "field": "username",
      "message": "用户名不能为空"
    }
  ],
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 4.4 分页响应格式
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "content": [],
    "page": 0,
    "size": 20,
    "totalElements": 100,
    "totalPages": 5
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## 5. 安全设计

### 5.1 认证机制
- **JWT Token**：使用JSON Web Token进行用户认证
- **Token过期**：设置合理的过期时间（如24小时）
- **刷新机制**：提供Token刷新接口

### 5.2 授权机制
- **基于角色的访问控制(RBAC)**：用户、项目经理、管理员
- **方法级权限控制**：使用@PreAuthorize注解
- **数据级权限控制**：用户只能访问自己有权限的数据

### 5.3 安全配置示例
```java
@Configuration
@EnableWebSecurity
public class SecurityConfig {
    
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.csrf().disable()
            .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            .and()
            .authorizeHttpRequests(authz -> authz
                .requestMatchers("/api/auth/**").permitAll()
                .requestMatchers("/api/admin/**").hasRole("ADMIN")
                .requestMatchers("/api/projects/**").hasAnyRole("USER", "PROJECT_MANAGER", "ADMIN")
                .anyRequest().authenticated()
            )
            .addFilterBefore(jwtAuthenticationFilter(), UsernamePasswordAuthenticationFilter.class);
        
        return http.build();
    }
}
```

## 6. 开发规范

### 6.1 代码规范
- **命名规范**：使用驼峰命名法，类名首字母大写，方法名和变量名首字母小写
- **注释规范**：使用JSDoc格式添加中文注释
- **代码格式**：使用统一的代码格式化工具

### 6.2 前端开发规范

#### 文件命名
- HTML文件使用小写字母和连字符（kebab-case）
- CSS文件使用小写字母和连字符
- JavaScript文件使用驼峰命名（camelCase）
- 图片文件使用小写字母和连字符

#### HTML开发规范
- 使用语义化标签
- 保持良好的文档结构
- 添加必要的meta标签
- 使用合适的ARIA属性提升可访问性

#### CSS开发规范
- 遵循BEM命名规范
- 使用CSS变量管理主题
- 移动端优先的响应式设计
- 合理使用Flexbox和Grid布局

#### JavaScript开发规范
- 使用ES6+现代语法
- 模块化开发
- 避免全局变量污染
- 使用严格模式
- 添加适当的错误处理

### 6.3 Git规范
- **分支策略**：main(主分支)、develop(开发分支)、feature/(功能分支)
- **提交信息**：使用规范的提交信息格式
```
feat: 添加用户登录功能
fix: 修复项目列表分页问题
docs: 更新API文档
style: 代码格式调整
refactor: 重构用户服务代码
test: 添加单元测试
```

### 6.4 测试规范
- **单元测试**：使用JUnit 5编写单元测试
- **集成测试**：使用@SpringBootTest进行集成测试
- **测试覆盖率**：保持80%以上的代码覆盖率

## 7. 部署指南

### 7.1 Docker部署

#### 7.1.1 Dockerfile
```dockerfile
FROM openjdk:11-jre-slim

VOLUME /tmp

COPY target/project-management-*.jar app.jar

EXPOSE 8080

ENTRYPOINT ["java","-jar","/app.jar"]
```

#### 7.1.2 docker-compose.yml
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - SPRING_DATASOURCE_URL=***************************************
      - SPRING_DATASOURCE_USERNAME=pm_user
      - SPRING_DATASOURCE_PASSWORD=your_password
    depends_on:
      - db

  db:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=root_password
      - MYSQL_DATABASE=project_management
      - MYSQL_USER=pm_user
      - MYSQL_PASSWORD=your_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql

volumes:
  mysql_data:
```

### 7.2 生产环境配置

#### 7.2.1 application-prod.properties
```properties
# 数据库配置
spring.datasource.url=*****************************************************************************
spring.datasource.username=${DB_USERNAME}
spring.datasource.password=${DB_PASSWORD}

# JPA配置
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=false

# 日志配置
logging.level.com.example=INFO
logging.file.name=logs/application.log

# JWT配置
jwt.secret=${JWT_SECRET}
jwt.expiration=86400000

# 邮件配置
spring.mail.host=${MAIL_HOST}
spring.mail.port=${MAIL_PORT}
spring.mail.username=${MAIL_USERNAME}
spring.mail.password=${MAIL_PASSWORD}
```

## 8. 监控和日志

### 8.1 应用监控
- **Spring Boot Actuator**：应用健康检查和指标监控
- **Micrometer**：应用指标收集
- **Prometheus + Grafana**：监控数据可视化（可选）

### 8.2 日志管理
- **Logback**：日志框架配置
- **日志级别**：ERROR、WARN、INFO、DEBUG
- **日志轮转**：按大小和时间轮转日志文件

### 8.3 logback-spring.xml配置
```xml
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <springProfile name="!prod">
        <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>
            </encoder>
        </appender>
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
        </root>
    </springProfile>
    
    <springProfile name="prod">
        <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>logs/application.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
                <fileNamePattern>logs/application.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
                <maxFileSize>100MB</maxFileSize>
                <maxHistory>30</maxHistory>
                <totalSizeCap>3GB</totalSizeCap>
            </rollingPolicy>
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>
            </encoder>
        </appender>
        <root level="INFO">
            <appender-ref ref="FILE"/>
        </root>
    </springProfile>
</configuration>
```

## 9. 性能优化

### 9.1 数据库优化
- **连接池配置**：使用HikariCP连接池
- **查询优化**：使用合适的索引，避免N+1查询
- **分页查询**：大数据量时使用分页

### 9.2 缓存策略
- **Spring Cache**：方法级缓存
- **Redis**：分布式缓存（可选）
- **缓存策略**：合理设置缓存过期时间

### 9.3 代码优化
- **异步处理**：使用@Async进行异步操作
- **批量操作**：减少数据库交互次数
- **资源管理**：及时释放资源，避免内存泄漏

## 10. 故障排查

### 10.1 常见问题
- **数据库连接问题**：检查数据库配置和网络连接
- **权限问题**：检查用户角色和权限配置
- **性能问题**：分析慢查询和系统资源使用

### 10.2 调试技巧
- **日志分析**：通过日志定位问题
- **断点调试**：使用IDE进行断点调试
- **性能分析**：使用性能分析工具

### 10.3 应急处理
- **服务降级**：关闭非核心功能
- **数据备份**：定期备份重要数据
- **回滚方案**：准备快速回滚机制