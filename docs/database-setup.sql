-- 项目管理系统数据库初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS project_management DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE project_management;

-- 创建用户并授权
CREATE USER IF NOT EXISTS 'pm_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON project_management.* TO 'pm_user'@'localhost';
FLUSH PRIVILEGES;

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱地址',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    role ENUM('USER', 'PROJECT_MANAGER', 'ADMIN') DEFAULT 'USER' COMMENT '用户角色',
    avatar_url VARCHAR(255) COMMENT '头像URL',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 项目表
CREATE TABLE IF NOT EXISTS projects (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '项目ID',
    name VARCHAR(100) NOT NULL COMMENT '项目名称',
    description TEXT COMMENT '项目描述',
    status ENUM('NOT_STARTED', 'IN_PROGRESS', 'COMPLETED', 'PAUSED') DEFAULT 'NOT_STARTED' COMMENT '项目状态',
    priority ENUM('LOW', 'MEDIUM', 'HIGH', 'URGENT') DEFAULT 'MEDIUM' COMMENT '优先级',
    creator_id BIGINT NOT NULL COMMENT '创建者ID',
    start_date DATE COMMENT '开始日期',
    end_date DATE COMMENT '结束日期',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE RESTRICT,
    INDEX idx_creator (creator_id),
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_dates (start_date, end_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='项目表';

-- 项目成员表
CREATE TABLE IF NOT EXISTS project_members (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role ENUM('MEMBER', 'COORDINATOR') DEFAULT 'MEMBER' COMMENT '项目角色',
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_project_user (project_id, user_id),
    INDEX idx_project (project_id),
    INDEX idx_user (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='项目成员表';

-- 任务表
CREATE TABLE IF NOT EXISTS tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '任务ID',
    title VARCHAR(200) NOT NULL COMMENT '任务标题',
    description TEXT COMMENT '任务描述',
    status ENUM('TODO', 'IN_PROGRESS', 'REVIEW', 'COMPLETED', 'CANCELLED') DEFAULT 'TODO' COMMENT '任务状态',
    priority ENUM('LOW', 'MEDIUM', 'HIGH', 'URGENT') DEFAULT 'MEDIUM' COMMENT '优先级',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    assignee_id BIGINT COMMENT '指派人ID',
    creator_id BIGINT NOT NULL COMMENT '创建者ID',
    estimated_hours DECIMAL(5,2) COMMENT '预估工时',
    actual_hours DECIMAL(5,2) COMMENT '实际工时',
    due_date DATETIME COMMENT '截止时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (assignee_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE RESTRICT,
    INDEX idx_project (project_id),
    INDEX idx_assignee (assignee_id),
    INDEX idx_creator (creator_id),
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_due_date (due_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务表';

-- 审核表
CREATE TABLE IF NOT EXISTS approvals (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '审核ID',
    type ENUM('PROJECT_CREATE', 'PROJECT_UPDATE', 'USER_ROLE', 'PROJECT_JOIN') NOT NULL COMMENT '审核类型',
    target_id BIGINT NOT NULL COMMENT '目标对象ID',
    requester_id BIGINT NOT NULL COMMENT '申请人ID',
    approver_id BIGINT COMMENT '审核人ID',
    status ENUM('PENDING', 'APPROVED', 'REJECTED') DEFAULT 'PENDING' COMMENT '审核状态',
    request_data JSON COMMENT '申请数据',
    approval_comment TEXT COMMENT '审核意见',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (requester_id) REFERENCES users(id) ON DELETE RESTRICT,
    FOREIGN KEY (approver_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_requester (requester_id),
    INDEX idx_approver (approver_id),
    INDEX idx_target (target_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='审核表';

-- 活动日志表
CREATE TABLE IF NOT EXISTS activity_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    action_type VARCHAR(50) NOT NULL COMMENT '操作类型',
    target_type VARCHAR(50) NOT NULL COMMENT '目标类型',
    target_id BIGINT NOT NULL COMMENT '目标ID',
    description TEXT COMMENT '操作描述',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user (user_id),
    INDEX idx_action_type (action_type),
    INDEX idx_target (target_type, target_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='活动日志表';

-- 插入初始数据

-- 插入管理员用户（密码：admin123，需要在应用中使用BCrypt加密）
INSERT INTO users (username, email, password_hash, role) VALUES 
('admin', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDa', 'ADMIN'),
('manager', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDa', 'PROJECT_MANAGER'),
('user1', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDa', 'USER'),
('user2', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDa', 'USER');

-- 插入示例项目
INSERT INTO projects (name, description, status, priority, creator_id, start_date, end_date) VALUES 
('项目管理系统', '开发一个功能完善的项目管理系统', 'IN_PROGRESS', 'HIGH', 1, '2024-01-01', '2024-06-30'),
('移动应用开发', '开发公司移动端应用', 'NOT_STARTED', 'MEDIUM', 2, '2024-02-01', '2024-08-31');

-- 插入项目成员
INSERT INTO project_members (project_id, user_id, role) VALUES 
(1, 1, 'COORDINATOR'),
(1, 2, 'COORDINATOR'),
(1, 3, 'MEMBER'),
(1, 4, 'MEMBER'),
(2, 2, 'COORDINATOR'),
(2, 3, 'MEMBER');

-- 插入示例任务
INSERT INTO tasks (title, description, status, priority, project_id, assignee_id, creator_id, estimated_hours, due_date) VALUES 
('需求分析', '分析项目管理系统的功能需求', 'COMPLETED', 'HIGH', 1, 3, 1, 16.0, '2024-01-15 23:59:59'),
('数据库设计', '设计项目管理系统的数据库结构', 'COMPLETED', 'HIGH', 1, 3, 1, 12.0, '2024-01-20 23:59:59'),
('后端API开发', '开发项目管理系统的后端API', 'IN_PROGRESS', 'HIGH', 1, 4, 1, 40.0, '2024-03-01 23:59:59'),
('前端界面开发', '开发项目管理系统的前端界面', 'TODO', 'MEDIUM', 1, 3, 1, 32.0, '2024-04-01 23:59:59'),
('系统测试', '进行系统功能测试和性能测试', 'TODO', 'MEDIUM', 1, 4, 1, 20.0, '2024-05-01 23:59:59');

-- 更新已完成任务的实际工时和完成时间
UPDATE tasks SET actual_hours = 14.5, completed_at = '2024-01-14 18:30:00' WHERE id = 1;
UPDATE tasks SET actual_hours = 10.0, completed_at = '2024-01-19 16:45:00' WHERE id = 2;

-- 插入示例审核记录
INSERT INTO approvals (type, target_id, requester_id, approver_id, status, request_data, approval_comment) VALUES 
('PROJECT_CREATE', 1, 2, 1, 'APPROVED', '{"projectName": "项目管理系统", "reason": "团队需要一个项目管理工具"}', '项目需求明确，同意创建'),
('USER_ROLE', 3, 3, 1, 'APPROVED', '{"requestedRole": "PROJECT_MANAGER", "reason": "希望承担更多项目管理职责"}', '用户能力符合要求，同意角色升级');

-- 插入示例活动日志
INSERT INTO activity_logs (user_id, action_type, target_type, target_id, description) VALUES 
(1, 'CREATE', 'PROJECT', 1, '创建了项目：项目管理系统'),
(1, 'CREATE', 'TASK', 1, '创建了任务：需求分析'),
(3, 'UPDATE', 'TASK', 1, '完成了任务：需求分析'),
(1, 'CREATE', 'TASK', 2, '创建了任务：数据库设计'),
(3, 'UPDATE', 'TASK', 2, '完成了任务：数据库设计'),
(1, 'CREATE', 'TASK', 3, '创建了任务：后端API开发'),
(4, 'UPDATE', 'TASK', 3, '开始了任务：后端API开发');

-- 创建视图：项目统计视图
CREATE OR REPLACE VIEW project_statistics AS
SELECT 
    p.id,
    p.name,
    p.status,
    COUNT(DISTINCT pm.user_id) as member_count,
    COUNT(DISTINCT t.id) as total_tasks,
    COUNT(DISTINCT CASE WHEN t.status = 'COMPLETED' THEN t.id END) as completed_tasks,
    COUNT(DISTINCT CASE WHEN t.status = 'IN_PROGRESS' THEN t.id END) as in_progress_tasks,
    COUNT(DISTINCT CASE WHEN t.status = 'TODO' THEN t.id END) as todo_tasks,
    COALESCE(ROUND(COUNT(DISTINCT CASE WHEN t.status = 'COMPLETED' THEN t.id END) * 100.0 / NULLIF(COUNT(DISTINCT t.id), 0), 2), 0) as progress_percentage,
    SUM(t.estimated_hours) as total_estimated_hours,
    SUM(t.actual_hours) as total_actual_hours
FROM projects p
LEFT JOIN project_members pm ON p.id = pm.project_id
LEFT JOIN tasks t ON p.id = t.project_id
GROUP BY p.id, p.name, p.status;

-- 创建视图：用户任务统计视图
CREATE OR REPLACE VIEW user_task_statistics AS
SELECT 
    u.id,
    u.username,
    u.email,
    COUNT(DISTINCT t.id) as total_tasks,
    COUNT(DISTINCT CASE WHEN t.status = 'COMPLETED' THEN t.id END) as completed_tasks,
    COUNT(DISTINCT CASE WHEN t.status = 'IN_PROGRESS' THEN t.id END) as in_progress_tasks,
    COUNT(DISTINCT CASE WHEN t.status = 'TODO' THEN t.id END) as todo_tasks,
    COUNT(DISTINCT CASE WHEN t.due_date < NOW() AND t.status != 'COMPLETED' THEN t.id END) as overdue_tasks,
    SUM(t.estimated_hours) as total_estimated_hours,
    SUM(t.actual_hours) as total_actual_hours
FROM users u
LEFT JOIN tasks t ON u.id = t.assignee_id
GROUP BY u.id, u.username, u.email;

-- 创建存储过程：获取项目进度报告
DELIMITER //
CREATE PROCEDURE GetProjectProgressReport(IN project_id BIGINT)
BEGIN
    SELECT 
        p.name as project_name,
        p.status as project_status,
        p.start_date,
        p.end_date,
        ps.member_count,
        ps.total_tasks,
        ps.completed_tasks,
        ps.in_progress_tasks,
        ps.todo_tasks,
        ps.progress_percentage,
        ps.total_estimated_hours,
        ps.total_actual_hours,
        CASE 
            WHEN p.end_date < CURDATE() AND p.status != 'COMPLETED' THEN '已逾期'
            WHEN p.end_date = CURDATE() AND p.status != 'COMPLETED' THEN '今日截止'
            WHEN DATEDIFF(p.end_date, CURDATE()) <= 7 AND p.status != 'COMPLETED' THEN '即将截止'
            ELSE '正常'
        END as deadline_status
    FROM projects p
    JOIN project_statistics ps ON p.id = ps.id
    WHERE p.id = project_id;
END //
DELIMITER ;

-- 创建触发器：记录项目状态变更
DELIMITER //
CREATE TRIGGER project_status_change_log
AFTER UPDATE ON projects
FOR EACH ROW
BEGIN
    IF OLD.status != NEW.status THEN
        INSERT INTO activity_logs (user_id, action_type, target_type, target_id, description)
        VALUES (1, 'UPDATE', 'PROJECT', NEW.id, CONCAT('项目状态从 ', OLD.status, ' 变更为 ', NEW.status));
    END IF;
END //
DELIMITER ;

-- 创建触发器：记录任务状态变更
DELIMITER //
CREATE TRIGGER task_status_change_log
AFTER UPDATE ON tasks
FOR EACH ROW
BEGIN
    IF OLD.status != NEW.status THEN
        INSERT INTO activity_logs (user_id, action_type, target_type, target_id, description)
        VALUES (COALESCE(NEW.assignee_id, NEW.creator_id), 'UPDATE', 'TASK', NEW.id, CONCAT('任务状态从 ', OLD.status, ' 变更为 ', NEW.status));
        
        -- 如果任务完成，记录完成时间
        IF NEW.status = 'COMPLETED' AND OLD.status != 'COMPLETED' THEN
            UPDATE tasks SET completed_at = NOW() WHERE id = NEW.id;
        END IF;
    END IF;
END //
DELIMITER ;

-- 创建索引优化查询性能
CREATE INDEX idx_tasks_status_due_date ON tasks(status, due_date);
CREATE INDEX idx_activity_logs_created_at_desc ON activity_logs(created_at DESC);
CREATE INDEX idx_projects_status_priority ON projects(status, priority);

-- 显示创建结果
SELECT 'Database setup completed successfully!' as message;
SELECT COUNT(*) as user_count FROM users;
SELECT COUNT(*) as project_count FROM projects;
SELECT COUNT(*) as task_count FROM tasks;
SELECT COUNT(*) as approval_count FROM approvals;

-- 显示初始用户信息（用于测试登录）
SELECT 
    username,
    email,
    role,
    '密码: admin123' as password_note
FROM users 
ORDER BY id;

COMMIT;