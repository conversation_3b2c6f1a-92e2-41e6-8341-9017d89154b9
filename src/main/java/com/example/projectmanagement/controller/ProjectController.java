package com.example.projectmanagement.controller;

import com.example.projectmanagement.dto.ApiResponse;
import com.example.projectmanagement.dto.PageResponse;
import com.example.projectmanagement.dto.ProjectDTO;
import com.example.projectmanagement.dto.ProjectMemberDTO;
import com.example.projectmanagement.entity.ProjectRole;
import com.example.projectmanagement.entity.ProjectStatus;
import com.example.projectmanagement.service.ProjectService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;

/**
 * 项目控制器
 * 处理项目相关的HTTP请求
 */
@RestController
@RequestMapping("/api/projects")
@RequiredArgsConstructor
public class ProjectController {

    private final ProjectService projectService;

    /**
     * 创建新项目
     * @param projectDTO 项目DTO
     * @return 创建成功的项目DTO
     */
    @PostMapping
    public ResponseEntity<ApiResponse<ProjectDTO>> createProject(@Valid @RequestBody ProjectDTO projectDTO) {
        ProjectDTO createdProject = projectService.createProject(projectDTO);
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success("项目创建成功", createdProject));
    }

    /**
     * 根据ID获取项目信息
     * @param id 项目ID
     * @return 项目DTO
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<ProjectDTO>> getProjectById(@PathVariable Long id) {
        ProjectDTO project = projectService.getProjectById(id);
        return ResponseEntity.ok(ApiResponse.success(project));
    }

    /**
     * 更新项目信息
     * @param id 项目ID
     * @param projectDTO 项目DTO
     * @return 更新后的项目DTO
     */
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<ProjectDTO>> updateProject(
            @PathVariable Long id, 
            @Valid @RequestBody ProjectDTO projectDTO) {
        ProjectDTO updatedProject = projectService.updateProject(id, projectDTO);
        return ResponseEntity.ok(ApiResponse.success("项目更新成功", updatedProject));
    }

    /**
     * 删除项目（逻辑删除）
     * @param id 项目ID
     * @return 成功响应
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<Void>> deleteProject(@PathVariable Long id) {
        projectService.deleteProject(id);
        return ResponseEntity.ok(ApiResponse.success("项目已删除", (Void)null));
    }

    /**
     * 恢复已删除的项目
     * @param id 项目ID
     * @return 恢复后的项目DTO
     */
    @PutMapping("/{id}/restore")
    public ResponseEntity<ApiResponse<ProjectDTO>> restoreProject(@PathVariable Long id) {
        boolean restored = projectService.restoreProject(id);
        if (restored) {
            ProjectDTO restoredProject = projectService.getProjectById(id);
            return ResponseEntity.ok(ApiResponse.success("项目已恢复", restoredProject));
        } else {
            return ResponseEntity.badRequest().body(ApiResponse.<ProjectDTO>error("项目恢复失败"));
        }
    }

    /**
     * 获取项目列表（分页）
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @param sortBy 排序字段
     * @param direction 排序方向（asc或desc）
     * @param keyword 关键词（用于搜索项目名称）
     * @param status 项目状态（可选）
     * @param archived 是否归档（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 项目DTO分页结果
     */
    @GetMapping
    public ResponseEntity<ApiResponse<PageResponse<ProjectDTO>>> getAllProjects(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String direction,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) ProjectStatus status,
            @RequestParam(required = false) Boolean archived,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        
        Sort.Direction sortDirection = "desc".equalsIgnoreCase(direction) ? Sort.Direction.DESC : Sort.Direction.ASC;
        Pageable pageable = PageRequest.of(page, size, sortDirection, sortBy);
        
        PageResponse<ProjectDTO> projects = projectService.getAllProjects(
                pageable, status, archived, keyword);
        return ResponseEntity.ok(ApiResponse.success(projects));
    }

    /**
     * 获取当前用户创建的项目列表（分页）
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @param status 项目状态（可选）
     * @param archived 是否归档（可选）
     * @return 项目DTO分页结果
     */
    @GetMapping("/my-created")
    public ResponseEntity<ApiResponse<PageResponse<ProjectDTO>>> getMyCreatedProjects(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) ProjectStatus status,
            @RequestParam(required = false) Boolean archived) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.Direction.DESC, "createdAt");
        PageResponse<ProjectDTO> projects = projectService.getCurrentUserCreatedProjects(pageable, status, archived, null);
        return ResponseEntity.ok(ApiResponse.success(projects));
    }

    /**
     * 获取当前用户参与的项目列表（分页）
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @param status 项目状态（可选）
     * @param archived 是否归档（可选）
     * @return 项目DTO分页结果
     */
    @GetMapping("/my-participated")
    public ResponseEntity<ApiResponse<PageResponse<ProjectDTO>>> getMyParticipatedProjects(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) ProjectStatus status,
            @RequestParam(required = false) Boolean archived) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.Direction.DESC, "createdAt");
        PageResponse<ProjectDTO> projects = projectService.getCurrentUserProjects(pageable, status, archived, null);
        return ResponseEntity.ok(ApiResponse.success(projects));
    }

    /**
     * 更新项目状态
     * @param id 项目ID
     * @param status 新状态
     * @return 更新后的项目DTO
     */
    @PutMapping("/{id}/status")
    public ResponseEntity<ApiResponse<ProjectDTO>> updateProjectStatus(
            @PathVariable Long id, 
            @RequestParam ProjectStatus status) {
        ProjectDTO updatedProject = projectService.updateProjectStatus(id, status);
        return ResponseEntity.ok(ApiResponse.success("项目状态已更新", updatedProject));
    }

    /**
     * 归档项目
     * @param id 项目ID
     * @return 更新后的项目DTO
     */
    @PutMapping("/{id}/archive")
    public ResponseEntity<ApiResponse<ProjectDTO>> archiveProject(@PathVariable Long id) {
        boolean archived = projectService.deleteProject(id);
        if (archived) {
            ProjectDTO archivedProject = projectService.getProjectById(id);
            return ResponseEntity.ok(ApiResponse.success("项目已归档", archivedProject));
        } else {
            return ResponseEntity.badRequest().body(ApiResponse.error("项目归档失败"));
        }
    }

    /**
     * 取消归档项目
     * @param id 项目ID
     * @return 更新后的项目DTO
     */
    @PutMapping("/{id}/unarchive")
    public ResponseEntity<ApiResponse<ProjectDTO>> unarchiveProject(@PathVariable Long id) {
        boolean unarchived = projectService.restoreProject(id);
        if (unarchived) {
            ProjectDTO unarchivedProject = projectService.getProjectById(id);
            return ResponseEntity.ok(ApiResponse.success("项目已取消归档", unarchivedProject));
        } else {
            return ResponseEntity.badRequest().body(ApiResponse.error("项目取消归档失败"));
        }
    }

    /**
     * 添加项目成员
     * @param projectId 项目ID
     * @param userId 用户ID
     * @param role 项目角色
     * @return 添加的项目成员DTO
     */
    @PostMapping("/{projectId}/members")
    public ResponseEntity<ApiResponse<ProjectMemberDTO>> addProjectMember(
            @PathVariable Long projectId,
            @RequestParam Long userId,
            @RequestParam(defaultValue = "MEMBER") ProjectRole role) {
        ProjectMemberDTO member = projectService.addProjectMember(projectId, userId, role.name());
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success("成员已添加到项目", member));
    }

    /**
     * 移除项目成员
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 成功响应
     */
    @DeleteMapping("/{projectId}/members/{userId}")
    public ResponseEntity<ApiResponse<Void>> removeProjectMember(
            @PathVariable Long projectId,
            @PathVariable Long userId) {
        projectService.removeProjectMember(projectId, userId);
        return ResponseEntity.ok(ApiResponse.<Void>success("成员已从项目中移除", null));
    }

    /**
     * 更新项目成员角色
     * @param projectId 项目ID
     * @param userId 用户ID
     * @param role 新角色
     * @return 更新后的项目成员DTO
     */
    @PutMapping("/{projectId}/members/{userId}/role")
    public ResponseEntity<ApiResponse<ProjectMemberDTO>> updateProjectMemberRole(
            @PathVariable Long projectId,
            @PathVariable Long userId,
            @RequestParam ProjectRole role) {
        ProjectMemberDTO updatedMember = projectService.updateProjectMemberRole(projectId, userId, role.name());
        return ResponseEntity.ok(ApiResponse.success("成员角色已更新", updatedMember));
    }

    /**
     * 获取项目成员列表
     * @param projectId 项目ID
     * @return 项目成员DTO列表
     */
    @GetMapping("/{projectId}/members")
    public ResponseEntity<ApiResponse<List<ProjectMemberDTO>>> getProjectMembers(@PathVariable Long projectId) {
        List<ProjectMemberDTO> members = projectService.getProjectMembers(projectId);
        return ResponseEntity.ok(ApiResponse.success(members));
    }

    /**
     * 检查用户是否为项目成员
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 是否为项目成员
     */
    @GetMapping("/{projectId}/members/{userId}/check")
    public ResponseEntity<ApiResponse<Boolean>> isProjectMember(
            @PathVariable Long projectId,
            @PathVariable Long userId) {
        boolean isMember = projectService.isProjectMember(projectId, userId);
        return ResponseEntity.ok(ApiResponse.success(isMember));
    }

    /**
     * 检查用户是否为项目管理员
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 是否为项目管理员
     */
    @GetMapping("/{projectId}/admins/{userId}/check")
    public ResponseEntity<ApiResponse<Boolean>> isProjectAdmin(
            @PathVariable Long projectId,
            @PathVariable Long userId) {
        boolean isAdmin = projectService.isProjectAdmin(projectId, userId);
        return ResponseEntity.ok(ApiResponse.success(isAdmin));
    }
}