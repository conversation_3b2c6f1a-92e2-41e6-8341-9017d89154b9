package com.example.projectmanagement.controller;

import com.example.projectmanagement.dto.ApiResponse;
import com.example.projectmanagement.dto.PageResponse;
import com.example.projectmanagement.dto.TaskDTO;
import com.example.projectmanagement.entity.TaskPriority;
import com.example.projectmanagement.entity.TaskStatus;
import com.example.projectmanagement.service.TaskService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;

/**
 * 任务控制器
 * 处理任务相关的HTTP请求
 */
@RestController
@RequestMapping("/api/tasks")
@RequiredArgsConstructor
public class TaskController {

    private final TaskService taskService;

    /**
     * 创建新任务
     * @param taskDTO 任务DTO
     * @return 创建成功的任务DTO
     */
    @PostMapping
    public ResponseEntity<ApiResponse<TaskDTO>> createTask(@Valid @RequestBody TaskDTO taskDTO) {
        TaskDTO createdTask = taskService.createTask(taskDTO);
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success("任务创建成功", createdTask));
    }

    /**
     * 根据ID获取任务信息
     * @param id 任务ID
     * @return 任务DTO
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<TaskDTO>> getTaskById(@PathVariable Long id) {
        TaskDTO task = taskService.getTaskById(id);
        return ResponseEntity.ok(ApiResponse.success(task));
    }

    /**
     * 更新任务信息
     * @param id 任务ID
     * @param taskDTO 任务DTO
     * @return 更新后的任务DTO
     */
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<TaskDTO>> updateTask(
            @PathVariable Long id, 
            @Valid @RequestBody TaskDTO taskDTO) {
        TaskDTO updatedTask = taskService.updateTask(id, taskDTO);
        return ResponseEntity.ok(ApiResponse.success("任务更新成功", updatedTask));
    }

    /**
     * 删除任务（逻辑删除）
     * @param id 任务ID
     * @return 成功响应
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<Void>> deleteTask(@PathVariable Long id) {
        taskService.deleteTask(id);
        return ResponseEntity.ok(ApiResponse.<Void>success("任务已删除", null));
    }

    /**
     * 恢复已删除的任务
     * @param id 任务ID
     * @return 恢复后的任务DTO
     */
    @PutMapping("/{id}/restore")
    public ResponseEntity<ApiResponse<Void>> restoreTask(@PathVariable Long id) {
        taskService.restoreTask(id);
        return ResponseEntity.ok(ApiResponse.<Void>success("任务已恢复", null));
    }

    /**
     * 获取任务列表（分页）
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @param sortBy 排序字段
     * @param direction 排序方向（asc或desc）
     * @param keyword 关键词（用于搜索任务名称）
     * @param status 任务状态（可选）
     * @param priority 任务优先级（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 任务DTO分页结果
     */
    @GetMapping
    public ResponseEntity<ApiResponse<PageResponse<TaskDTO>>> getAllTasks(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String direction,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) TaskStatus status,
            @RequestParam(required = false) TaskPriority priority,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        
        Sort.Direction sortDirection = "desc".equalsIgnoreCase(direction) ? Sort.Direction.DESC : Sort.Direction.ASC;
        Pageable pageable = PageRequest.of(page, size, sortDirection, sortBy);
        
        PageResponse<TaskDTO> tasks = taskService.getAllTasks(
                pageable, null, status, priority, null, null, keyword);
        return ResponseEntity.ok(ApiResponse.success(tasks));
    }

    /**
     * 获取项目任务列表（分页）
     * @param projectId 项目ID
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @param status 任务状态（可选）
     * @param priority 任务优先级（可选）
     * @return 任务DTO分页结果
     */
    @GetMapping("/project/{projectId}")
    public ResponseEntity<ApiResponse<PageResponse<TaskDTO>>> getProjectTasks(
            @PathVariable Long projectId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) TaskStatus status,
            @RequestParam(required = false) TaskPriority priority) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.Direction.DESC, "createdAt");
        PageResponse<TaskDTO> tasks = taskService.getProjectTasks(projectId, pageable, status, priority, null, null, null);
        return ResponseEntity.ok(ApiResponse.success(tasks));
    }

    /**
     * 获取当前用户创建的任务列表（分页）
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @param status 任务状态（可选）
     * @param priority 任务优先级（可选）
     * @return 任务DTO分页结果
     */
    @GetMapping("/my-created")
    public ResponseEntity<ApiResponse<PageResponse<TaskDTO>>> getMyCreatedTasks(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) TaskStatus status,
            @RequestParam(required = false) TaskPriority priority) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.Direction.DESC, "createdAt");
        PageResponse<TaskDTO> tasks = taskService.getCurrentUserCreatedTasks(pageable, null, status, priority, null, null);
        return ResponseEntity.ok(ApiResponse.success(tasks));
    }

    /**
     * 获取当前用户负责的任务列表（分页）
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @param status 任务状态（可选）
     * @param priority 任务优先级（可选）
     * @return 任务DTO分页结果
     */
    @GetMapping("/my-assigned")
    public ResponseEntity<ApiResponse<PageResponse<TaskDTO>>> getMyAssignedTasks(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) TaskStatus status,
            @RequestParam(required = false) TaskPriority priority) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.Direction.DESC, "createdAt");
        PageResponse<TaskDTO> tasks = taskService.getCurrentUserAssignedTasks(pageable, null, status, priority, null, null);
        return ResponseEntity.ok(ApiResponse.success(tasks));
    }

    /**
     * 更新任务状态
     * @param id 任务ID
     * @param status 新状态
     * @return 更新后的任务DTO
     */
    @PutMapping("/{id}/status")
    public ResponseEntity<ApiResponse<TaskDTO>> updateTaskStatus(
            @PathVariable Long id, 
            @RequestParam TaskStatus status) {
        TaskDTO updatedTask = taskService.updateTaskStatus(id, status);
        return ResponseEntity.ok(ApiResponse.success("任务状态已更新", updatedTask));
    }

    /**
     * 更新任务优先级
     * @param id 任务ID
     * @param priority 新优先级
     * @return 更新后的任务DTO
     */
    @PutMapping("/{id}/priority")
    public ResponseEntity<ApiResponse<TaskDTO>> updateTaskPriority(
            @PathVariable Long id, 
            @RequestParam TaskPriority priority) {
        TaskDTO updatedTask = taskService.updateTaskPriority(id, priority);
        return ResponseEntity.ok(ApiResponse.success("任务优先级已更新", updatedTask));
    }

    /**
     * 分配任务负责人
     * @param id 任务ID
     * @param assigneeId 负责人ID
     * @return 更新后的任务DTO
     */
    @PutMapping("/{id}/assignee")
    public ResponseEntity<ApiResponse<TaskDTO>> assignTask(
            @PathVariable Long id, 
            @RequestParam Long assigneeId) {
        TaskDTO updatedTask = taskService.assignTask(id, assigneeId);
        return ResponseEntity.ok(ApiResponse.success("任务已分配", updatedTask));
    }

    /**
     * 取消任务负责人分配
     * @param id 任务ID
     * @return 更新后的任务DTO
     */
    @PutMapping("/{id}/unassign")
    public ResponseEntity<ApiResponse<TaskDTO>> unassignTask(@PathVariable Long id) {
        TaskDTO updatedTask = taskService.unassignTask(id);
        return ResponseEntity.ok(ApiResponse.success("任务负责人已取消", updatedTask));
    }

    /**
     * 获取即将到期的任务列表
     * @param days 天数（默认为7天内）
     * @return 任务DTO列表
     */
    @GetMapping("/upcoming")
    public ResponseEntity<ApiResponse<List<TaskDTO>>> getUpcomingTasks(
            @RequestParam(defaultValue = "7") int days) {
        List<TaskDTO> tasks = taskService.getUpcomingTasks(null);
        return ResponseEntity.ok(ApiResponse.success(tasks));
    }

    /**
     * 获取逾期的任务列表
     * @return 任务DTO列表
     */
    @GetMapping("/overdue")
    public ResponseEntity<ApiResponse<List<TaskDTO>>> getOverdueTasks() {
        List<TaskDTO> tasks = taskService.getOverdueTasks(null);
        return ResponseEntity.ok(ApiResponse.success(tasks));
    }

    /**
     * 记录任务实际工时
     * @param id 任务ID
     * @param hours 实际工时（小时）
     * @return 更新后的任务DTO
     */
    @PutMapping("/{id}/actual-hours")
    public ResponseEntity<ApiResponse<TaskDTO>> recordActualHours(
            @PathVariable Long id, 
            @RequestParam Double hours) {
        TaskDTO updatedTask = taskService.recordTaskActualHours(id, hours);
        return ResponseEntity.ok(ApiResponse.success("实际工时已记录", updatedTask));
    }

    /**
     * 按日期范围获取任务列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 任务DTO列表
     */
    @GetMapping("/date-range")
    public ResponseEntity<ApiResponse<List<TaskDTO>>> getTasksByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        List<TaskDTO> tasks = taskService.getTasksByDateRange(startDate, endDate);
        return ResponseEntity.ok(ApiResponse.success(tasks));
    }
}