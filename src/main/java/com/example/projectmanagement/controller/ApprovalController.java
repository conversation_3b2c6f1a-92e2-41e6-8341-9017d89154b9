package com.example.projectmanagement.controller;

import com.example.projectmanagement.dto.ApiResponse;
import com.example.projectmanagement.dto.ApprovalDTO;
import com.example.projectmanagement.dto.PageResponse;
import com.example.projectmanagement.entity.ApprovalStatus;
import com.example.projectmanagement.entity.ApprovalType;
import com.example.projectmanagement.service.ApprovalService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 审核控制器
 * 处理审核相关的HTTP请求
 */
@RestController
@RequestMapping("/api/approvals")
@RequiredArgsConstructor
public class ApprovalController {

    private final ApprovalService approvalService;

    /**
     * 创建新审核
     * @param approvalDTO 审核DTO
     * @return 创建成功的审核DTO
     */
    @PostMapping
    public ResponseEntity<ApiResponse<ApprovalDTO>> createApproval(@Valid @RequestBody ApprovalDTO approvalDTO) {
        ApprovalDTO createdApproval = approvalService.createApproval(approvalDTO);
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success("审核创建成功", createdApproval));
    }

    /**
     * 根据ID获取审核信息
     * @param id 审核ID
     * @return 审核DTO
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<ApprovalDTO>> getApprovalById(@PathVariable Long id) {
        ApprovalDTO approval = approvalService.getApprovalById(id);
        return ResponseEntity.ok(ApiResponse.success(approval));
    }

    /**
     * 更新审核信息
     * @param id 审核ID
     * @param approvalDTO 审核DTO
     * @return 更新后的审核DTO
     */
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<ApprovalDTO>> updateApproval(
            @PathVariable Long id, 
            @Valid @RequestBody ApprovalDTO approvalDTO) {
        ApprovalDTO updatedApproval = approvalService.updateApproval(id, approvalDTO);
        return ResponseEntity.ok(ApiResponse.success("审核更新成功", updatedApproval));
    }

    /**
     * 批准审核
     * @param id 审核ID
     * @param comment 批准意见（可选）
     * @return 更新后的审核DTO
     */
    @PutMapping("/{id}/approve")
    public ResponseEntity<ApiResponse<ApprovalDTO>> approveApproval(
            @PathVariable Long id,
            @RequestParam(required = false) String comment) {
        ApprovalDTO approvedApproval = approvalService.processApproval(id, ApprovalStatus.APPROVED, comment);
        return ResponseEntity.ok(ApiResponse.success("审核已批准", approvedApproval));
    }

    /**
     * 拒绝审核
     * @param id 审核ID
     * @param comment 拒绝理由
     * @return 更新后的审核DTO
     */
    @PutMapping("/{id}/reject")
    public ResponseEntity<ApiResponse<ApprovalDTO>> rejectApproval(
            @PathVariable Long id,
            @RequestParam String comment) {
        ApprovalDTO rejectedApproval = approvalService.processApproval(id, ApprovalStatus.REJECTED, comment);
        return ResponseEntity.ok(ApiResponse.success("审核已拒绝", rejectedApproval));
    }

    /**
     * 取消审核
     * @param id 审核ID
     * @return 更新后的审核DTO
     */
    @PutMapping("/{id}/cancel")
    public ResponseEntity<ApiResponse<ApprovalDTO>> cancelApproval(@PathVariable Long id) {
        boolean cancelled = approvalService.cancelApproval(id);
        if (cancelled) {
            return ResponseEntity.ok(ApiResponse.success("审核已取消", (ApprovalDTO)null));
        } else {
            return ResponseEntity.badRequest().body(ApiResponse.<ApprovalDTO>error("审核取消失败"));
        }
    }

    /**
     * 获取审核列表（分页）
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @param sortBy 排序字段
     * @param direction 排序方向（asc或desc）
     * @param keyword 关键词（用于搜索审核标题）
     * @param status 审核状态（可选）
     * @param type 审核类型（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 审核DTO分页结果
     */
    @GetMapping
    public ResponseEntity<ApiResponse<PageResponse<ApprovalDTO>>> getAllApprovals(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String direction,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) ApprovalStatus status,
            @RequestParam(required = false) ApprovalType type,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        
        Sort.Direction sortDirection = "desc".equalsIgnoreCase(direction) ? Sort.Direction.DESC : Sort.Direction.ASC;
        Pageable pageable = PageRequest.of(page, size, sortDirection, sortBy);
        
        LocalDateTime startDateTime = startDate != null ? startDate.atStartOfDay() : null;
        LocalDateTime endDateTime = endDate != null ? endDate.atTime(23, 59, 59) : null;
        PageResponse<ApprovalDTO> approvals = approvalService.getAllApprovals(
                pageable, null, null, null, null, status, type, startDateTime, endDateTime, keyword);
        return ResponseEntity.ok(ApiResponse.success(approvals));
    }

    /**
     * 获取当前用户发起的审核列表（分页）
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @param status 审核状态（可选）
     * @return 审核DTO分页结果
     */
    @GetMapping("/my-created")
    public ResponseEntity<ApiResponse<PageResponse<ApprovalDTO>>> getMyCreatedApprovals(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) ApprovalStatus status) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.Direction.DESC, "createdAt");
        PageResponse<ApprovalDTO> approvals = approvalService.getCurrentUserRequestedApprovals(pageable, status, null, null);
        return ResponseEntity.ok(ApiResponse.success(approvals));
    }

    /**
     * 获取当前用户待审批的审核列表（分页）
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @return 审核DTO分页结果
     */
    @GetMapping("/my-pending")
    public ResponseEntity<ApiResponse<PageResponse<ApprovalDTO>>> getMyPendingApprovals(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.Direction.DESC, "createdAt");
        PageResponse<ApprovalDTO> approvals = approvalService.getCurrentUserPendingApprovals(pageable, null, null);
        return ResponseEntity.ok(ApiResponse.success(approvals));
    }

    /**
     * 获取项目相关的审核列表（分页）
     * @param projectId 项目ID
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @param status 审核状态（可选）
     * @return 审核DTO分页结果
     */
    @GetMapping("/project/{projectId}")
    public ResponseEntity<ApiResponse<PageResponse<ApprovalDTO>>> getProjectApprovals(
            @PathVariable Long projectId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) ApprovalStatus status) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.Direction.DESC, "createdAt");
        PageResponse<ApprovalDTO> approvals = approvalService.getProjectApprovals(projectId, pageable, status, null, null);
        return ResponseEntity.ok(ApiResponse.success(approvals));
    }

    /**
     * 获取任务相关的审核列表（分页）
     * @param taskId 任务ID
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @param status 审核状态（可选）
     * @return 审核DTO分页结果
     */
    @GetMapping("/task/{taskId}")
    public ResponseEntity<ApiResponse<PageResponse<ApprovalDTO>>> getTaskApprovals(
            @PathVariable Long taskId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) ApprovalStatus status) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.Direction.DESC, "createdAt");
        List<ApprovalDTO> approvals = approvalService.getTaskApprovals(taskId);
         PageResponse<ApprovalDTO> pageResponse = PageResponse.<ApprovalDTO>builder()
                 .page(1)
                 .size(approvals.size())
                 .totalPages(1)
                 .totalElements(approvals.size())
                 .first(true)
                 .last(true)
                 .hasPrevious(false)
                 .hasNext(false)
                 .content(approvals)
                 .build();
         return ResponseEntity.ok(ApiResponse.success(pageResponse));
    }

    /**
     * 检查用户是否有权限审批
     * @param id 审核ID
     * @param userId 用户ID
     * @return 是否有权限审批
     */
    @GetMapping("/{id}/can-approve/{userId}")
    public ResponseEntity<ApiResponse<Boolean>> canUserApprove(
            @PathVariable Long id,
            @PathVariable Long userId) {
        boolean canApprove = approvalService.canApprove(id, userId);
        return ResponseEntity.ok(ApiResponse.success(canApprove));
    }
}