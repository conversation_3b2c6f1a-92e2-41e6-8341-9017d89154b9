package com.example.projectmanagement.controller;

import com.example.projectmanagement.dto.ActivityLogDTO;
import com.example.projectmanagement.dto.ApiResponse;
import com.example.projectmanagement.dto.DashboardStatsDTO;
import com.example.projectmanagement.service.DashboardService;
import com.example.projectmanagement.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 仪表板控制器
 * 处理仪表板统计相关的HTTP请求
 */
@RestController
@RequestMapping("/api/dashboard")
@RequiredArgsConstructor
public class DashboardController {

    private final DashboardService dashboardService;

    /**
     * 获取系统统计信息
     * @return 系统统计信息DTO
     */
    @GetMapping("/system-stats")
    public ResponseEntity<ApiResponse<DashboardStatsDTO>> getSystemStats() {
        DashboardStatsDTO stats = dashboardService.getSystemStats();
        return ResponseEntity.ok(ApiResponse.success(stats));
    }

    /**
     * 获取当前用户统计信息
     * @return 用户统计信息DTO
     */
    @GetMapping("/user-stats")
    public ResponseEntity<ApiResponse<DashboardStatsDTO>> getCurrentUserStats() {
        DashboardStatsDTO stats = dashboardService.getCurrentUserStats();
        return ResponseEntity.ok(ApiResponse.success(stats));
    }

    /**
     * 获取项目统计信息
     * @param projectId 项目ID
     * @return 项目统计信息DTO
     */
    @GetMapping("/project/{projectId}/stats")
    public ResponseEntity<ApiResponse<DashboardStatsDTO>> getProjectStats(@PathVariable Long projectId) {
        DashboardStatsDTO stats = dashboardService.getProjectStats(projectId);
        return ResponseEntity.ok(ApiResponse.success(stats));
    }

    /**
     * 获取最近活动日志
     * @param limit 限制数量（默认为10）
     * @return 活动日志DTO列表
     */
    @GetMapping("/recent-activities")
    public ResponseEntity<ApiResponse<List<ActivityLogDTO>>> getRecentActivities(
            @RequestParam(defaultValue = "10") int limit) {
        List<ActivityLogDTO> activities = dashboardService.getRecentActivities(limit);
        return ResponseEntity.ok(ApiResponse.success(activities));
    }

    /**
     * 获取项目进度统计分布
     * @return 项目进度统计Map
     */
    @GetMapping("/project-progress-stats")
    public ResponseEntity<ApiResponse<Map<String, Long>>> getProjectProgressStats() {
        Map<String, Long> stats = dashboardService.getProjectProgressStats();
        return ResponseEntity.ok(ApiResponse.success(stats));
    }

    /**
     * 获取任务状态统计分布
     * @return 任务状态统计Map
     */
    @GetMapping("/task-status-stats")
    public ResponseEntity<ApiResponse<Map<String, Long>>> getTaskStatusStats() {
        Map<String, Long> stats = dashboardService.getTaskStatusDistribution();
        return ResponseEntity.ok(ApiResponse.success(stats));
    }

    /**
     * 获取审核类型统计分布
     * @return 审核类型统计Map
     */
    @GetMapping("/approval-type-stats")
    public ResponseEntity<ApiResponse<Map<String, Long>>> getApprovalTypeStats() {
        Map<String, Long> stats = dashboardService.getApprovalTypeDistribution();
        return ResponseEntity.ok(ApiResponse.success(stats));
    }

    /**
     * 获取项目任务状态统计分布
     * @param projectId 项目ID
     * @return 项目任务状态统计Map
     */
    @GetMapping("/project/{projectId}/task-status-stats")
    public ResponseEntity<ApiResponse<Map<String, Long>>> getProjectTaskStatusStats(@PathVariable Long projectId) {
        Map<String, Long> stats = dashboardService.getProjectTaskStatusDistribution(projectId);
        return ResponseEntity.ok(ApiResponse.success(stats));
    }

    /**
     * 获取项目成员工作量统计分布
     * @param projectId 项目ID
     * @return 项目成员工作量统计Map
     */
    @GetMapping("/project/{projectId}/member-workload-stats")
    public ResponseEntity<ApiResponse<Map<String, Long>>> getProjectMemberWorkloadStats(@PathVariable Long projectId) {
        Map<String, Long> stats = dashboardService.getProjectMemberWorkloadStats(projectId);
        return ResponseEntity.ok(ApiResponse.success(stats));
    }

    /**
     * 获取当前用户参与项目统计分布
     * @return 用户参与项目统计Map
     */
    @GetMapping("/user-project-stats")
    public ResponseEntity<ApiResponse<Map<String, Long>>> getUserProjectStats() {
        Map<String, Long> stats = dashboardService.getCurrentUserProjectStats();
        return ResponseEntity.ok(ApiResponse.success(stats));
    }

    /**
     * 获取当前用户任务统计分布
     * @return 用户任务统计Map
     */
    @GetMapping("/user-task-stats")
    public ResponseEntity<ApiResponse<Map<String, Long>>> getUserTaskStats() {
        Map<String, Long> stats = dashboardService.getCurrentUserTaskStats();
        return ResponseEntity.ok(ApiResponse.success(stats));
    }
}