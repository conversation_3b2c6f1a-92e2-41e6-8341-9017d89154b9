package com.example.projectmanagement.controller;

import com.example.projectmanagement.dto.ActivityLogDTO;
import com.example.projectmanagement.dto.ApiResponse;
import com.example.projectmanagement.dto.PageResponse;
import com.example.projectmanagement.entity.ActivityType;
import com.example.projectmanagement.service.ActivityLogService;
import com.example.projectmanagement.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 活动日志控制器
 * 处理活动日志相关的HTTP请求
 */
@RestController
@RequestMapping("/api/activity-logs")
@RequiredArgsConstructor
public class ActivityLogController {

    private final ActivityLogService activityLogService;

    /**
     * 根据ID获取活动日志信息
     * @param id 活动日志ID
     * @return 活动日志DTO
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<ActivityLogDTO>> getActivityLogById(@PathVariable Long id) {
        ActivityLogDTO activityLog = activityLogService.getActivityLogById(id);
        return ResponseEntity.ok(ApiResponse.success(activityLog));
    }

    /**
     * 获取活动日志列表（分页）
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @param sortBy 排序字段
     * @param direction 排序方向（asc或desc）
     * @param userId 用户ID（可选）
     * @param projectId 项目ID（可选）
     * @param taskId 任务ID（可选）
     * @param approvalId 审核ID（可选）
     * @param activityType 活动类型（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 活动日志DTO分页结果
     */
    @GetMapping
    public ResponseEntity<ApiResponse<PageResponse<ActivityLogDTO>>> getAllActivityLogs(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String direction,
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) Long projectId,
            @RequestParam(required = false) Long taskId,
            @RequestParam(required = false) Long approvalId,
            @RequestParam(required = false) ActivityType activityType,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        
        Sort.Direction sortDirection = "desc".equalsIgnoreCase(direction) ? Sort.Direction.DESC : Sort.Direction.ASC;
        Pageable pageable = PageRequest.of(page, size, sortDirection, sortBy);
        
        LocalDateTime startDateTime = startDate != null ? startDate.atStartOfDay() : null;
        LocalDateTime endDateTime = endDate != null ? endDate.atTime(23, 59, 59) : null;
        PageResponse<ActivityLogDTO> activityLogs = activityLogService.getAllActivityLogs(
                pageable, userId, projectId, taskId, approvalId, activityType, startDateTime, endDateTime);
        return ResponseEntity.ok(ApiResponse.success(activityLogs));
    }

    /**
     * 获取当前用户的活动日志列表（分页）
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @param activityType 活动类型（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 活动日志DTO分页结果
     */
    @GetMapping("/my-logs")
    public ResponseEntity<ApiResponse<PageResponse<ActivityLogDTO>>> getCurrentUserActivityLogs(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) ActivityType activityType,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        
        Long currentUserId = SecurityUtils.getCurrentUserId();
        Pageable pageable = PageRequest.of(page, size, Sort.Direction.DESC, "createdAt");
        LocalDateTime startDateTime = startDate != null ? startDate.atStartOfDay() : null;
        LocalDateTime endDateTime = endDate != null ? endDate.atTime(23, 59, 59) : null;
        PageResponse<ActivityLogDTO> activityLogs = activityLogService.getAllActivityLogs(
                pageable, currentUserId, null, null, null, activityType, startDateTime, endDateTime);
        return ResponseEntity.ok(ApiResponse.success(activityLogs));
    }

    /**
     * 获取项目相关的活动日志列表（分页）
     * @param projectId 项目ID
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @param activityType 活动类型（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 活动日志DTO分页结果
     */
    @GetMapping("/project/{projectId}")
    public ResponseEntity<ApiResponse<PageResponse<ActivityLogDTO>>> getProjectActivityLogs(
            @PathVariable Long projectId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) ActivityType activityType,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.Direction.DESC, "createdAt");
        PageResponse<ActivityLogDTO> activityLogs = activityLogService.getProjectActivityLogs(projectId, pageable);
        return ResponseEntity.ok(ApiResponse.success(activityLogs));
    }

    /**
     * 获取任务相关的活动日志列表（分页）
     * @param taskId 任务ID
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @param activityType 活动类型（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 活动日志DTO分页结果
     */
    @GetMapping("/task/{taskId}")
    public ResponseEntity<ApiResponse<PageResponse<ActivityLogDTO>>> getTaskActivityLogs(
            @PathVariable Long taskId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) ActivityType activityType,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        
        List<ActivityLogDTO> activityLogList = activityLogService.getTaskActivityLogs(taskId);
        PageResponse<ActivityLogDTO> activityLogs = PageResponse.<ActivityLogDTO>builder()
                .page(page + 1)
                .size(size)
                .totalPages(1)
                .totalElements(activityLogList.size())
                .first(true)
                .last(true)
                .hasPrevious(false)
                .hasNext(false)
                .content(activityLogList)
                .build();
        return ResponseEntity.ok(ApiResponse.success(activityLogs));
    }

    /**
     * 获取审核相关的活动日志列表（分页）
     * @param approvalId 审核ID
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @param activityType 活动类型（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 活动日志DTO分页结果
     */
    @GetMapping("/approval/{approvalId}")
    public ResponseEntity<ApiResponse<PageResponse<ActivityLogDTO>>> getApprovalActivityLogs(
            @PathVariable Long approvalId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) ActivityType activityType,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.Direction.DESC, "createdAt");
        List<ActivityLogDTO> activityLogList = activityLogService.getApprovalActivityLogs(approvalId);
        PageResponse<ActivityLogDTO> activityLogs = PageResponse.<ActivityLogDTO>builder()
                .page(page + 1)
                .size(size)
                .totalPages(1)
                .totalElements(activityLogList.size())
                .first(true)
                .last(true)
                .hasPrevious(false)
                .hasNext(false)
                .content(activityLogList)
                .build();
        return ResponseEntity.ok(ApiResponse.success(activityLogs));
    }

    /**
     * 获取最近的活动日志列表
     * @param limit 限制数量（默认为10）
     * @return 活动日志DTO列表
     */
    @GetMapping("/recent")
    public ResponseEntity<ApiResponse<List<ActivityLogDTO>>> getRecentActivityLogs(
            @RequestParam(defaultValue = "10") int limit) {
        List<ActivityLogDTO> activityLogs = activityLogService.getRecentActivityLogs(limit);
        return ResponseEntity.ok(ApiResponse.success(activityLogs));
    }

    /**
     * 获取当前用户最近的活动日志列表
     * @param limit 限制数量（默认为10）
     * @return 活动日志DTO列表
     */
    @GetMapping("/my-recent")
    public ResponseEntity<ApiResponse<List<ActivityLogDTO>>> getCurrentUserRecentActivityLogs(
            @RequestParam(defaultValue = "10") int limit) {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        Pageable pageable = PageRequest.of(0, limit, Sort.Direction.DESC, "createdAt");
        PageResponse<ActivityLogDTO> pageResponse = activityLogService.getAllActivityLogs(
                pageable, currentUserId, null, null, null, null, null, null);
        return ResponseEntity.ok(ApiResponse.success(pageResponse.getContent()));
    }

    /**
     * 获取项目最近的活动日志列表
     * @param projectId 项目ID
     * @param limit 限制数量（默认为10）
     * @return 活动日志DTO列表
     */
    @GetMapping("/project/{projectId}/recent")
    public ResponseEntity<ApiResponse<List<ActivityLogDTO>>> getProjectRecentActivityLogs(
            @PathVariable Long projectId,
            @RequestParam(defaultValue = "10") int limit) {
        List<ActivityLogDTO> activityLogs = activityLogService.getProjectRecentActivityLogs(projectId, limit);
        return ResponseEntity.ok(ApiResponse.success(activityLogs));
    }

    /**
     * 获取任务最近的活动日志列表
     * @param taskId 任务ID
     * @param limit 限制数量（默认为10）
     * @return 活动日志DTO列表
     */
    @GetMapping("/task/{taskId}/recent")
    public ResponseEntity<ApiResponse<List<ActivityLogDTO>>> getTaskRecentActivityLogs(
            @PathVariable Long taskId,
            @RequestParam(defaultValue = "10") int limit) {
        List<ActivityLogDTO> activityLogs = activityLogService.getTaskActivityLogs(taskId);
        return ResponseEntity.ok(ApiResponse.success(activityLogs));
    }

    /**
     * 获取审核最近的活动日志列表
     * @param approvalId 审核ID
     * @param limit 限制数量（默认为10）
     * @return 活动日志DTO列表
     */
    @GetMapping("/approval/{approvalId}/recent")
    public ResponseEntity<ApiResponse<List<ActivityLogDTO>>> getApprovalRecentActivityLogs(
            @PathVariable Long approvalId,
            @RequestParam(defaultValue = "10") int limit) {
        List<ActivityLogDTO> activityLogs = activityLogService.getApprovalActivityLogs(approvalId);
        return ResponseEntity.ok(ApiResponse.success(activityLogs));
    }
}