package com.example.projectmanagement.controller;

import com.example.projectmanagement.dto.ApiResponse;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器
 * 用于测试前后端连接
 */
@RestController
@RequestMapping("/api/test")
public class TestController {

    /**
     * 简单的ping测试
     * @return 测试响应
     */
    @GetMapping("/ping")
    public ResponseEntity<ApiResponse<String>> ping() {
        return ResponseEntity.ok(ApiResponse.success("pong"));
    }

    /**
     * 获取服务器状态
     * @return 服务器状态信息
     */
    @GetMapping("/status")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("server", "项目管理系统后端");
        status.put("version", "1.0.0");
        status.put("timestamp", LocalDateTime.now());
        status.put("status", "running");
        
        return ResponseEntity.ok(ApiResponse.success("服务器状态正常", status));
    }
}
