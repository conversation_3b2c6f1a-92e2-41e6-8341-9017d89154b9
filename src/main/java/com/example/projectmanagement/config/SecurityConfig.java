package com.example.projectmanagement.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.web.cors.CorsConfigurationSource;

/**
 * 安全配置类
 * 配置Spring Security相关设置
 */
@Configuration
@EnableWebSecurity
public class SecurityConfig {

    @Autowired
    private CorsConfigurationSource corsConfigurationSource;

    /**
     * 密码编码器Bean
     * @return BCrypt密码编码器
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    /**
     * 安全过滤器链配置
     * @param http HttpSecurity对象
     * @return SecurityFilterChain
     * @throws Exception 配置异常
     */
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            // 启用CORS
            .cors().configurationSource(corsConfigurationSource)
            .and()

            // 禁用CSRF保护（因为使用JWT）
            .csrf().disable()

            // 配置会话管理为无状态
            .sessionManagement()
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            .and()

            // 配置授权规则
            .authorizeHttpRequests()
                // 允许所有人访问认证相关端点
                .antMatchers("/api/auth/**").permitAll()
                // 允许访问测试端点
                .antMatchers("/api/test/**").permitAll()
                // 允许访问H2控制台（仅开发环境）
                .antMatchers("/h2-console/**").permitAll()
                // 允许访问静态资源
                .antMatchers("/", "/index.html", "/assets/**", "/pages/**").permitAll()
                // 允许访问健康检查端点
                .antMatchers("/actuator/health").permitAll()
                // 其他所有请求都需要认证
                .anyRequest().authenticated()
            .and()

            // 允许H2控制台的frame显示
            .headers().frameOptions().disable();

        return http.build();
    }
}
