package com.example.projectmanagement.exception;

/**
 * 数据验证异常
 * 当数据验证失败时抛出此异常
 */
public class ValidationException extends RuntimeException {
    
    /**
     * 构造函数
     * @param message 异常消息
     */
    public ValidationException(String message) {
        super(message);
    }
    
    /**
     * 构造函数
     * @param message 异常消息
     * @param cause 异常原因
     */
    public ValidationException(String message, Throwable cause) {
        super(message, cause);
    }
}