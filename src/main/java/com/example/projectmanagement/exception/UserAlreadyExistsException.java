package com.example.projectmanagement.exception;

/**
 * 用户已存在异常
 * 当尝试创建已存在的用户时抛出此异常
 */
public class UserAlreadyExistsException extends RuntimeException {
    
    /**
     * 构造函数
     * @param message 异常消息
     */
    public UserAlreadyExistsException(String message) {
        super(message);
    }
    
    /**
     * 构造函数
     * @param message 异常消息
     * @param cause 异常原因
     */
    public UserAlreadyExistsException(String message, Throwable cause) {
        super(message, cause);
    }
}