package com.example.projectmanagement.dto;

import com.example.projectmanagement.entity.ProjectStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 仪表板统计数据DTO
 * 用于返回系统概览统计信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DashboardStatsDTO {
    
    /**
     * 项目总数
     */
    private long totalProjects;
    
    /**
     * 活跃项目数
     */
    private long activeProjects;
    
    /**
     * 已完成项目数
     */
    private long completedProjects;
    
    /**
     * 任务总数
     */
    private long totalTasks;
    
    /**
     * 待办任务数
     */
    private long todoTasks;
    
    /**
     * 进行中任务数
     */
    private long inProgressTasks;
    
    /**
     * 审核中任务数
     */
    private long reviewTasks;
    
    /**
     * 已完成任务数
     */
    private long doneTasks;
    
    /**
     * 审核总数
     */
    private long totalApprovals;
    
    /**
     * 待审核数
     */
    private long pendingApprovals;
    
    /**
     * 已批准审核数
     */
    private long approvedApprovals;
    
    /**
     * 已拒绝审核数
     */
    private long rejectedApprovals;
    
    /**
     * 用户总数
     */
    private long totalUsers;
    
    /**
     * 最近活动
     */
    private List<ActivityLogDTO> recentActivities;
    
    /**
     * 项目进度统计
     * 键为项目ID，值为项目进度百分比
     */
    private Map<Long, Integer> projectProgress;
    
    /**
     * 任务状态分布
     * 键为状态名称，值为任务数量
     */
    private Map<String, Long> taskStatusDistribution;
    
    /**
     * 审核类型分布
     * 键为审核类型名称，值为审核数量
     */
    private Map<String, Long> approvalTypeDistribution;
    
    /**
     * 项目进度统计
     * 键为项目状态名称，值为项目数量
     */
    private Map<String, Long> projectProgressStats;
    
    /**
     * 已完成任务数（用于设置方法）
     */
    private long completedTasks;
    
    /**
     * 项目ID（用于设置方法）
     */
    private Long projectId;
    
    /**
     * 设置已完成任务数
     * @param completedTasks 已完成任务数
     */
    public void setCompletedTasks(long completedTasks) {
        this.completedTasks = completedTasks;
    }
    
    /**
     * 设置项目ID
     * @param projectId 项目ID
     */
    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }
    
    /**
     * 成员总数
     */
    private long totalMembers;
    
    /**
     * 成员工作量统计
     */
    private Map<String, Long> memberWorkloadStats;
    
    /**
     * 设置成员总数
     * @param totalMembers 成员总数
     */
    public void setTotalMembers(long totalMembers) {
        this.totalMembers = totalMembers;
    }
    
    /**
     * 设置成员工作量统计
     * @param memberWorkloadStats 成员工作量统计
     */
    public void setMemberWorkloadStats(Map<String, Long> memberWorkloadStats) {
        this.memberWorkloadStats = memberWorkloadStats;
    }
    
    // 审核中任务数
    private long inReviewTasks;
    
    // 项目名称
    private String projectName;
    
    // 项目状态
    private ProjectStatus projectStatus;
    
    public long getInReviewTasks() {
        return inReviewTasks;
    }
    
    public void setInReviewTasks(long inReviewTasks) {
        this.inReviewTasks = inReviewTasks;
    }
    
    public String getProjectName() {
        return projectName;
    }
    
    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }
    
    public ProjectStatus getProjectStatus() {
        return projectStatus;
    }
    
    public void setProjectStatus(ProjectStatus projectStatus) {
        this.projectStatus = projectStatus;
    }
}