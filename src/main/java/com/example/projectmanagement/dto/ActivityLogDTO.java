package com.example.projectmanagement.dto;

import com.example.projectmanagement.entity.ActivityType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 活动日志DTO
 * 用于在控制器和服务层之间传递活动日志数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActivityLogDTO {
    
    /**
     * 日志ID
     */
    private Long id;
    
    /**
     * 活动类型
     */
    private ActivityType activityType;
    
    /**
     * 活动描述
     */
    private String description;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 用户头像
     */
    private String userAvatar;
    
    /**
     * 项目ID
     */
    private Long projectId;
    
    /**
     * 项目名称
     */
    private String projectName;
    
    /**
     * 任务ID
     */
    private Long taskId;
    
    /**
     * 任务标题
     */
    private String taskTitle;
    
    /**
     * 审核ID
     */
    private Long approvalId;
    
    /**
     * 审核标题
     */
    private String approvalTitle;
    
    /**
     * IP地址
     */
    private String ipAddress;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
}