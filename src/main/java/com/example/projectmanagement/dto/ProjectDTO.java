package com.example.projectmanagement.dto;

import com.example.projectmanagement.entity.ProjectStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 项目DTO
 * 用于在控制器和服务层之间传递项目数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectDTO {
    
    /**
     * 项目ID
     */
    private Long id;
    
    /**
     * 项目名称
     */
    private String name;
    
    /**
     * 项目描述
     */
    private String description;
    
    /**
     * 项目状态
     */
    private ProjectStatus status;
    
    /**
     * 开始日期
     */
    private LocalDate startDate;
    
    /**
     * 结束日期
     */
    private LocalDate endDate;
    
    /**
     * 创建者ID
     */
    private Long creatorId;
    
    /**
     * 创建者用户名
     */
    private String creatorName;
    
    /**
     * 是否已归档
     */
    private boolean archived;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 项目成员列表
     */
    private List<ProjectMemberDTO> members;
    
    /**
     * 任务总数
     */
    private Integer totalTasks;
    
    /**
     * 已完成任务数
     */
    private Integer completedTasks;
    
    /**
     * 成员数量
     */
    private Integer memberCount;
}