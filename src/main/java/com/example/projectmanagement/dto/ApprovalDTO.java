package com.example.projectmanagement.dto;

import com.example.projectmanagement.entity.ApprovalStatus;
import com.example.projectmanagement.entity.ApprovalType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 审核DTO
 * 用于在控制器和服务层之间传递审核数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApprovalDTO {
    
    /**
     * 审核ID
     */
    private Long id;
    
    /**
     * 审核标题
     */
    private String title;
    
    /**
     * 审核描述
     */
    private String description;
    
    /**
     * 审核类型
     */
    private ApprovalType type;
    
    /**
     * 审核状态
     */
    private ApprovalStatus status;
    
    /**
     * 项目ID
     */
    private Long projectId;
    
    /**
     * 项目名称
     */
    private String projectName;
    
    /**
     * 任务ID
     */
    private Long taskId;
    
    /**
     * 任务标题
     */
    private String taskTitle;
    
    /**
     * 申请人ID
     */
    private Long requesterId;
    
    /**
     * 申请人用户名
     */
    private String requesterName;
    
    /**
     * 审核人ID
     */
    private Long reviewerId;

    /**
     * 审核人用户名
     */
    private String reviewerName;

    /**
     * 批准人ID
     */
    private Long approverId;

    /**
     * 批准人用户名
     */
    private String approverName;
    
    /**
     * 审核时间
     */
    private LocalDateTime reviewedAt;
    
    /**
     * 审核意见
     */
    private String comments;
    
    /**
     * 审核意见
     */
    private String comment;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 审批时间（别名方法）
     * @param approvalTime 审批时间
     */
    public void setApprovalTime(LocalDateTime approvalTime) {
        this.reviewedAt = approvalTime;
    }
    
    /**
     * 获取审批时间（别名方法）
     * @return 审批时间
     */
    public LocalDateTime getApprovalTime() {
        return this.reviewedAt;
    }
    
    /**
     * 设置申请人用户名
     * @param requesterUsername 申请人用户名
     */
    public void setRequesterUsername(String requesterUsername) {
        this.requesterName = requesterUsername;
    }
    
    /**
     * 设置申请人全名
     * @param requesterFullName 申请人全名
     */
    public void setRequesterFullName(String requesterFullName) {
        this.requesterName = requesterFullName;
    }
    
    /**
     * 设置审核人用户名
     * @param approverUsername 审核人用户名
     */
    public void setApproverUsername(String approverUsername) {
        this.approverName = approverUsername;
    }
    
    /**
     * 设置审核人全名
     * @param approverFullName 审核人全名
     */
    public void setApproverFullName(String approverFullName) {
        this.approverName = approverFullName;
    }
}