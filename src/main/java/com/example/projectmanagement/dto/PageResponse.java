package com.example.projectmanagement.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 分页响应DTO
 * 用于封装分页查询结果
 * @param <T> 数据类型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PageResponse<T> {
    
    /**
     * 当前页码
     */
    private int page;
    
    /**
     * 每页大小
     */
    private int size;
    
    /**
     * 总页数
     */
    private int totalPages;
    
    /**
     * 总记录数
     */
    private long totalElements;
    
    /**
     * 是否为第一页
     */
    private boolean first;
    
    /**
     * 是否为最后一页
     */
    private boolean last;
    
    /**
     * 是否有上一页
     */
    private boolean hasPrevious;
    
    /**
     * 是否有下一页
     */
    private boolean hasNext;
    
    /**
     * 数据列表
     */
    private List<T> content;
    
    /**
     * 从Spring Data的Page对象创建PageResponse
     * @param page Spring Data的Page对象
     * @param <T> 数据类型
     * @return PageResponse对象
     */
    public static <T> PageResponse<T> from(Page<T> page) {
        return PageResponse.<T>builder()
                .page(page.getNumber() + 1) // Page从0开始，转换为从1开始
                .size(page.getSize())
                .totalPages(page.getTotalPages())
                .totalElements(page.getTotalElements())
                .first(page.isFirst())
                .last(page.isLast())
                .hasPrevious(page.hasPrevious())
                .hasNext(page.hasNext())
                .content(page.getContent())
                .build();
    }
    
    /**
     * 从Spring Data的Page对象创建PageResponse（别名方法）
     * @param page Spring Data的Page对象
     * @param <T> 数据类型
     * @return PageResponse对象
     */
    public static <T> PageResponse<T> fromPage(Page<T> page) {
        return from(page);
    }
    
    /**
     * 从Spring Data的Page对象创建PageResponse（of方法）
     * @param page Spring Data的Page对象
     * @param <T> 数据类型
     * @return PageResponse对象
     */
    public static <T> PageResponse<T> of(Page<T> page) {
        return from(page);
    }
}