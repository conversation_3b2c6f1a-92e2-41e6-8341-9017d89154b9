package com.example.projectmanagement.dto;

import com.example.projectmanagement.entity.ProjectRole;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 项目成员DTO
 * 用于在控制器和服务层之间传递项目成员数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectMemberDTO {
    
    /**
     * 成员ID
     */
    private Long id;
    
    /**
     * 项目ID
     */
    private Long projectId;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 用户全名
     */
    private String fullName;
    
    /**
     * 用户头像
     */
    private String avatar;
    
    /**
     * 项目角色
     */
    private ProjectRole role;
    
    /**
     * 是否活跃
     */
    private boolean active;
    
    /**
     * 加入时间
     */
    private LocalDateTime joinedAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}