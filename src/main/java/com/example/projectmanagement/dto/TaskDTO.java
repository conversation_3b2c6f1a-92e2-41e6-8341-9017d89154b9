package com.example.projectmanagement.dto;

import com.example.projectmanagement.entity.TaskPriority;
import com.example.projectmanagement.entity.TaskStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 任务DTO
 * 用于在控制器和服务层之间传递任务数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskDTO {
    
    /**
     * 任务ID
     */
    private Long id;
    
    /**
     * 任务标题
     */
    private String title;
    
    /**
     * 任务描述
     */
    private String description;
    
    /**
     * 任务状态
     */
    private TaskStatus status;
    
    /**
     * 任务优先级
     */
    private TaskPriority priority;
    
    /**
     * 截止日期
     */
    private LocalDate dueDate;
    
    /**
     * 预计工时
     */
    private Integer estimatedHours;
    
    /**
     * 实际工时
     */
    private Integer actualHours;
    
    /**
     * 项目ID
     */
    private Long projectId;
    
    /**
     * 项目名称
     */
    private String projectName;
    
    /**
     * 创建者ID
     */
    private Long creatorId;
    
    /**
     * 创建者用户名
     */
    private String creatorName;
    
    /**
     * 负责人ID
     */
    private Long assigneeId;
    
    /**
     * 负责人用户名
     */
    private String assigneeName;
    
    /**
     * 是否已归档
     */
    private boolean archived;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}