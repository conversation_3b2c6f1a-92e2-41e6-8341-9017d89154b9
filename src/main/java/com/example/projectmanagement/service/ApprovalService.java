package com.example.projectmanagement.service;

import com.example.projectmanagement.dto.ApprovalDTO;
import com.example.projectmanagement.dto.PageResponse;
import com.example.projectmanagement.entity.Approval;
import com.example.projectmanagement.entity.ApprovalStatus;
import com.example.projectmanagement.entity.ApprovalType;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 审核服务接口
 * 定义审核相关的业务逻辑方法
 */
public interface ApprovalService {

    /**
     * 创建新审核
     * @param approvalDTO 审核DTO
     * @return 创建成功的审核DTO
     */
    ApprovalDTO createApproval(ApprovalDTO approvalDTO);

    /**
     * 根据ID查找审核
     * @param id 审核ID
     * @return 审核DTO
     */
    ApprovalDTO getApprovalById(Long id);

    /**
     * 更新审核信息
     * @param id 审核ID
     * @param approvalDTO 审核DTO
     * @return 更新后的审核DTO
     */
    ApprovalDTO updateApproval(Long id, ApprovalDTO approvalDTO);

    /**
     * 审批审核
     * @param id 审核ID
     * @param status 审核状态（APPROVED或REJECTED）
     * @param comment 审批意见
     * @return 审批后的审核DTO
     */
    ApprovalDTO processApproval(Long id, ApprovalStatus status, String comment);

    /**
     * 取消审核
     * @param id 审核ID
     * @return 是否取消成功
     */
    boolean cancelApproval(Long id);

    /**
     * 获取审核列表（分页）
     * @param pageable 分页参数
     * @param projectId 项目ID（可选）
     * @param taskId 任务ID（可选）
     * @param requesterId 申请人ID（可选）
     * @param approverId 审核人ID（可选）
     * @param status 审核状态（可选）
     * @param type 审核类型（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @param keyword 关键词（可选，用于搜索审核标题）
     * @return 审核DTO分页结果
     */
    PageResponse<ApprovalDTO> getAllApprovals(Pageable pageable, Long projectId, Long taskId, 
                                            Long requesterId, Long approverId, ApprovalStatus status, 
                                            ApprovalType type, LocalDateTime startDate, 
                                            LocalDateTime endDate, String keyword);

    /**
     * 获取当前用户发起的审核列表（分页）
     * @param pageable 分页参数
     * @param status 审核状态（可选）
     * @param type 审核类型（可选）
     * @param keyword 关键词（可选，用于搜索审核标题）
     * @return 审核DTO分页结果
     */
    PageResponse<ApprovalDTO> getCurrentUserRequestedApprovals(Pageable pageable, ApprovalStatus status, 
                                                           ApprovalType type, String keyword);

    /**
     * 获取当前用户待审批的审核列表（分页）
     * @param pageable 分页参数
     * @param type 审核类型（可选）
     * @param keyword 关键词（可选，用于搜索审核标题）
     * @return 审核DTO分页结果
     */
    PageResponse<ApprovalDTO> getCurrentUserPendingApprovals(Pageable pageable, ApprovalType type, String keyword);

    /**
     * 获取项目审核列表（分页）
     * @param projectId 项目ID
     * @param pageable 分页参数
     * @param status 审核状态（可选）
     * @param type 审核类型（可选）
     * @param keyword 关键词（可选，用于搜索审核标题）
     * @return 审核DTO分页结果
     */
    PageResponse<ApprovalDTO> getProjectApprovals(Long projectId, Pageable pageable, ApprovalStatus status, 
                                                ApprovalType type, String keyword);

    /**
     * 获取任务审核列表
     * @param taskId 任务ID
     * @return 审核DTO列表
     */
    List<ApprovalDTO> getTaskApprovals(Long taskId);

    /**
     * 检查用户是否有权限审批
     * @param approvalId 审核ID
     * @param userId 用户ID
     * @return 是否有权限审批
     */
    boolean canApprove(Long approvalId, Long userId);

    /**
     * 检查当前用户是否有权限审批
     * @param approvalId 审核ID
     * @return 是否有权限审批
     */
    boolean canCurrentUserApprove(Long approvalId);

    /**
     * 将审核实体转换为DTO
     * @param approval 审核实体
     * @return 审核DTO
     */
    ApprovalDTO convertToDTO(Approval approval);
}