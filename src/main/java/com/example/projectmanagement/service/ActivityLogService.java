package com.example.projectmanagement.service;

import com.example.projectmanagement.dto.ActivityLogDTO;
import com.example.projectmanagement.dto.PageResponse;
import com.example.projectmanagement.entity.ActivityLog;
import com.example.projectmanagement.entity.ActivityType;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 活动日志服务接口
 * 定义活动日志相关的业务逻辑方法
 */
public interface ActivityLogService {

    /**
     * 记录活动日志
     * @param type 活动类型
     * @param description 活动描述
     * @param userId 用户ID
     * @param projectId 项目ID（可选）
     * @param taskId 任务ID（可选）
     * @param approvalId 审核ID（可选）
     * @return 创建的活动日志DTO
     */
    ActivityLogDTO logActivity(ActivityType type, String description, Long userId, 
                             Long projectId, Long taskId, Long approvalId);

    /**
     * 记录当前用户的活动日志
     * @param type 活动类型
     * @param description 活动描述
     * @param projectId 项目ID（可选）
     * @param taskId 任务ID（可选）
     * @param approvalId 审核ID（可选）
     * @return 创建的活动日志DTO
     */
    ActivityLogDTO logCurrentUserActivity(ActivityType type, String description, 
                                       Long projectId, Long taskId, Long approvalId);

    /**
     * 根据ID获取活动日志
     * @param id 活动日志ID
     * @return 活动日志DTO
     */
    ActivityLogDTO getActivityLogById(Long id);

    /**
     * 获取所有活动日志（分页）
     * @param pageable 分页参数
     * @param userId 用户ID（可选）
     * @param projectId 项目ID（可选）
     * @param taskId 任务ID（可选）
     * @param approvalId 审核ID（可选）
     * @param type 活动类型（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 活动日志DTO分页结果
     */
    PageResponse<ActivityLogDTO> getAllActivityLogs(Pageable pageable, Long userId, Long projectId, 
                                                 Long taskId, Long approvalId, ActivityType type, 
                                                 LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 获取当前用户的活动日志（分页）
     * @param pageable 分页参数
     * @return 活动日志DTO分页结果
     */
    PageResponse<ActivityLogDTO> getCurrentUserActivityLogs(Pageable pageable);

    /**
     * 获取项目活动日志（分页）
     * @param projectId 项目ID
     * @param pageable 分页参数
     * @return 活动日志DTO分页结果
     */
    PageResponse<ActivityLogDTO> getProjectActivityLogs(Long projectId, Pageable pageable);

    /**
     * 获取任务活动日志
     * @param taskId 任务ID
     * @return 活动日志DTO列表
     */
    List<ActivityLogDTO> getTaskActivityLogs(Long taskId);

    /**
     * 获取审核活动日志
     * @param approvalId 审核ID
     * @return 活动日志DTO列表
     */
    List<ActivityLogDTO> getApprovalActivityLogs(Long approvalId);

    /**
     * 获取最近的活动日志
     * @param limit 限制数量
     * @return 活动日志DTO列表
     */
    List<ActivityLogDTO> getRecentActivityLogs(int limit);

    /**
     * 获取用户最近的活动日志
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 活动日志DTO列表
     */
    List<ActivityLogDTO> getUserRecentActivityLogs(Long userId, int limit);

    /**
     * 获取项目最近的活动日志
     * @param projectId 项目ID
     * @param limit 限制数量
     * @return 活动日志DTO列表
     */
    List<ActivityLogDTO> getProjectRecentActivityLogs(Long projectId, int limit);

    /**
     * 将活动日志实体转换为DTO
     * @param activityLog 活动日志实体
     * @return 活动日志DTO
     */
    ActivityLogDTO convertToDTO(ActivityLog activityLog);
}