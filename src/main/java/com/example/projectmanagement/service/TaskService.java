package com.example.projectmanagement.service;

import com.example.projectmanagement.dto.PageResponse;
import com.example.projectmanagement.dto.TaskDTO;
import com.example.projectmanagement.entity.Task;
import com.example.projectmanagement.entity.TaskPriority;
import com.example.projectmanagement.entity.TaskStatus;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.List;

/**
 * 任务服务接口
 * 定义任务相关的业务逻辑方法
 */
public interface TaskService {

    /**
     * 创建新任务
     * @param taskDTO 任务DTO
     * @return 创建成功的任务DTO
     */
    TaskDTO createTask(TaskDTO taskDTO);

    /**
     * 根据ID查找任务
     * @param id 任务ID
     * @return 任务DTO
     */
    TaskDTO getTaskById(Long id);

    /**
     * 更新任务信息
     * @param id 任务ID
     * @param taskDTO 任务DTO
     * @return 更新后的任务DTO
     */
    TaskDTO updateTask(Long id, TaskDTO taskDTO);

    /**
     * 删除任务（逻辑删除，设置为归档状态）
     * @param id 任务ID
     * @return 是否删除成功
     */
    boolean deleteTask(Long id);

    /**
     * 恢复已归档的任务
     * @param id 任务ID
     * @return 是否恢复成功
     */
    boolean restoreTask(Long id);

    /**
     * 获取任务列表（分页）
     * @param pageable 分页参数
     * @param projectId 项目ID（可选）
     * @param status 任务状态（可选）
     * @param priority 任务优先级（可选）
     * @param assigneeId 负责人ID（可选）
     * @param archived 是否归档（可选）
     * @param keyword 关键词（可选，用于搜索任务标题）
     * @return 任务DTO分页结果
     */
    PageResponse<TaskDTO> getAllTasks(Pageable pageable, Long projectId, TaskStatus status, 
                                     TaskPriority priority, Long assigneeId, Boolean archived, String keyword);

    /**
     * 获取当前用户创建的任务列表（分页）
     * @param pageable 分页参数
     * @param projectId 项目ID（可选）
     * @param status 任务状态（可选）
     * @param priority 任务优先级（可选）
     * @param archived 是否归档（可选）
     * @param keyword 关键词（可选，用于搜索任务标题）
     * @return 任务DTO分页结果
     */
    PageResponse<TaskDTO> getCurrentUserCreatedTasks(Pageable pageable, Long projectId, TaskStatus status, 
                                                   TaskPriority priority, Boolean archived, String keyword);

    /**
     * 获取当前用户负责的任务列表（分页）
     * @param pageable 分页参数
     * @param projectId 项目ID（可选）
     * @param status 任务状态（可选）
     * @param priority 任务优先级（可选）
     * @param archived 是否归档（可选）
     * @param keyword 关键词（可选，用于搜索任务标题）
     * @return 任务DTO分页结果
     */
    PageResponse<TaskDTO> getCurrentUserAssignedTasks(Pageable pageable, Long projectId, TaskStatus status, 
                                                    TaskPriority priority, Boolean archived, String keyword);

    /**
     * 获取项目任务列表（分页）
     * @param projectId 项目ID
     * @param pageable 分页参数
     * @param status 任务状态（可选）
     * @param priority 任务优先级（可选）
     * @param assigneeId 负责人ID（可选）
     * @param archived 是否归档（可选）
     * @param keyword 关键词（可选，用于搜索任务标题）
     * @return 任务DTO分页结果
     */
    PageResponse<TaskDTO> getProjectTasks(Long projectId, Pageable pageable, TaskStatus status, 
                                         TaskPriority priority, Long assigneeId, Boolean archived, String keyword);

    /**
     * 更新任务状态
     * @param id 任务ID
     * @param status 新状态
     * @return 更新后的任务DTO
     */
    TaskDTO updateTaskStatus(Long id, TaskStatus status);

    /**
     * 更新任务优先级
     * @param id 任务ID
     * @param priority 新优先级
     * @return 更新后的任务DTO
     */
    TaskDTO updateTaskPriority(Long id, TaskPriority priority);

    /**
     * 分配任务负责人
     * @param id 任务ID
     * @param assigneeId 负责人ID
     * @return 更新后的任务DTO
     */
    TaskDTO assignTask(Long id, Long assigneeId);
    
    /**
     * 取消任务负责人分配
     * @param id 任务ID
     * @return 更新后的任务DTO
     */
    TaskDTO unassignTask(Long id);

    /**
     * 获取即将到期的任务列表（截止日期在未来7天内）
     * @param projectId 项目ID（可选）
     * @return 任务DTO列表
     */
    List<TaskDTO> getUpcomingTasks(Long projectId);

    /**
     * 获取逾期任务列表（截止日期已过但未完成）
     * @param projectId 项目ID（可选）
     * @return 任务DTO列表
     */
    List<TaskDTO> getOverdueTasks(Long projectId);

    /**
     * 获取当前用户即将到期的任务列表（截止日期在未来7天内）
     * @return 任务DTO列表
     */
    List<TaskDTO> getCurrentUserUpcomingTasks();

    /**
     * 获取当前用户逾期任务列表（截止日期已过但未完成）
     * @return 任务DTO列表
     */
    List<TaskDTO> getCurrentUserOverdueTasks();

    /**
     * 记录任务实际工时
     * @param id 任务ID
     * @param actualHours 实际工时
     * @return 更新后的任务DTO
     */
    TaskDTO recordTaskActualHours(Long id, Double actualHours);

    /**
     * 获取指定日期范围内的任务列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param projectId 项目ID（可选）
     * @return 任务DTO列表
     */
    List<TaskDTO> getTasksByDateRange(LocalDate startDate, LocalDate endDate, Long projectId);
    
    /**
     * 获取指定日期范围内的任务列表（当前用户相关）
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 任务DTO列表
     */
    List<TaskDTO> getTasksByDateRange(LocalDate startDate, LocalDate endDate);

    /**
     * 将任务实体转换为DTO
     * @param task 任务实体
     * @return 任务DTO
     */
    TaskDTO convertToDTO(Task task);
}