package com.example.projectmanagement.service.impl;

import com.example.projectmanagement.dto.ActivityLogDTO;
import com.example.projectmanagement.dto.DashboardStatsDTO;
import com.example.projectmanagement.entity.*;
import com.example.projectmanagement.exception.ResourceNotFoundException;
import com.example.projectmanagement.exception.UnauthorizedException;
import com.example.projectmanagement.repository.*;
import com.example.projectmanagement.service.ActivityLogService;
import com.example.projectmanagement.service.DashboardService;
import com.example.projectmanagement.service.ProjectService;
import com.example.projectmanagement.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 仪表板服务实现类
 */
@Service
@RequiredArgsConstructor
public class DashboardServiceImpl implements DashboardService {

    private final UserRepository userRepository;
    private final ProjectRepository projectRepository;
    private final TaskRepository taskRepository;
    private final ApprovalRepository approvalRepository;
    private final ProjectMemberRepository projectMemberRepository;
    private final ActivityLogService activityLogService;
    private final ProjectService projectService;
    private final SecurityUtils securityUtils;

    @Override
    public DashboardStatsDTO getSystemStats() {
        // 验证当前用户是否为管理员
        if (!securityUtils.isCurrentUserAdmin()) {
            throw new UnauthorizedException("Only administrators can view system statistics");
        }
        
        DashboardStatsDTO stats = new DashboardStatsDTO();
        
        // 项目统计
        stats.setTotalProjects(projectRepository.count());
        stats.setActiveProjects(projectRepository.countByStatus(ProjectStatus.IN_PROGRESS));
        stats.setCompletedProjects(projectRepository.countByStatus(ProjectStatus.COMPLETED));
        
        // 任务统计
        stats.setTotalTasks(taskRepository.count());
        stats.setTodoTasks(taskRepository.countByStatus(TaskStatus.TODO));
        stats.setInProgressTasks(taskRepository.countByStatus(TaskStatus.IN_PROGRESS));
        stats.setInReviewTasks(taskRepository.countByStatus(TaskStatus.IN_REVIEW));
        stats.setCompletedTasks(taskRepository.countByStatus(TaskStatus.COMPLETED));
        
        // 审核统计
        stats.setTotalApprovals(approvalRepository.count());
        stats.setPendingApprovals(approvalRepository.countByStatus(ApprovalStatus.PENDING));
        stats.setApprovedApprovals(approvalRepository.countByStatus(ApprovalStatus.APPROVED));
        stats.setRejectedApprovals(approvalRepository.countByStatus(ApprovalStatus.REJECTED));
        
        // 用户统计
        stats.setTotalUsers(userRepository.count());
        
        // 最近活动
        stats.setRecentActivities(activityLogService.getRecentActivityLogs(10));
        
        // 项目进度统计
        stats.setProjectProgressStats(getProjectProgressStats());
        
        // 任务状态分布
        stats.setTaskStatusDistribution(getTaskStatusDistribution());
        
        // 审核类型分布
        stats.setApprovalTypeDistribution(getApprovalTypeDistribution());
        
        return stats;
    }

    @Override
    public DashboardStatsDTO getCurrentUserStats() {
        Long currentUserId = securityUtils.getCurrentUserId();
        
        DashboardStatsDTO stats = new DashboardStatsDTO();
        
        // 项目统计
        stats.setTotalProjects(projectRepository.countByUserId(currentUserId));
        stats.setActiveProjects(projectRepository.countByUserIdAndStatus(currentUserId, ProjectStatus.IN_PROGRESS));
        stats.setCompletedProjects(projectRepository.countByUserIdAndStatus(currentUserId, ProjectStatus.COMPLETED));
        
        // 任务统计
        stats.setTotalTasks(taskRepository.countByAssigneeIdOrCreatorId(currentUserId, currentUserId));
        stats.setTodoTasks(taskRepository.countByAssigneeIdAndStatus(currentUserId, TaskStatus.TODO));
        stats.setInProgressTasks(taskRepository.countByAssigneeIdAndStatus(currentUserId, TaskStatus.IN_PROGRESS));
        stats.setInReviewTasks(taskRepository.countByAssigneeIdAndStatus(currentUserId, TaskStatus.IN_REVIEW));
        stats.setCompletedTasks(taskRepository.countByAssigneeIdAndStatus(currentUserId, TaskStatus.COMPLETED));
        
        // 审核统计
        stats.setTotalApprovals(approvalRepository.countByRequesterIdOrApproverId(currentUserId, currentUserId));
        stats.setPendingApprovals(approvalRepository.countByApproverIdAndStatus(currentUserId, ApprovalStatus.PENDING));
        stats.setApprovedApprovals(approvalRepository.countByRequesterIdAndStatus(currentUserId, ApprovalStatus.APPROVED));
        stats.setRejectedApprovals(approvalRepository.countByRequesterIdAndStatus(currentUserId, ApprovalStatus.REJECTED));
        
        // 最近活动
        stats.setRecentActivities(activityLogService.getUserRecentActivityLogs(currentUserId, 10));
        
        // 用户项目统计
        stats.setProjectProgressStats(getCurrentUserProjectStats());
        
        // 用户任务统计
        stats.setTaskStatusDistribution(getCurrentUserTaskStats());
        
        return stats;
    }

    @Override
    public DashboardStatsDTO getProjectStats(Long projectId) {
        // 验证项目存在
        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new ResourceNotFoundException("Project not found with id: " + projectId));
        
        // 验证用户是项目成员
        Long currentUserId = securityUtils.getCurrentUserId();
        if (!projectService.isProjectMember(projectId, currentUserId)) {
            throw new UnauthorizedException("User is not a member of the project");
        }
        
        DashboardStatsDTO stats = new DashboardStatsDTO();
        
        // 项目信息
        stats.setProjectId(project.getId());
        stats.setProjectName(project.getName());
        stats.setProjectStatus(project.getStatus());
        
        // 任务统计
        stats.setTotalTasks(taskRepository.countByProjectId(projectId));
        stats.setTodoTasks(taskRepository.countByProjectIdAndStatus(projectId, TaskStatus.TODO));
        stats.setInProgressTasks(taskRepository.countByProjectIdAndStatus(projectId, TaskStatus.IN_PROGRESS));
        stats.setInReviewTasks(taskRepository.countByProjectIdAndStatus(projectId, TaskStatus.IN_REVIEW));
        stats.setCompletedTasks(taskRepository.countByProjectIdAndStatus(projectId, TaskStatus.COMPLETED));
        
        // 审核统计
        stats.setTotalApprovals(approvalRepository.countByProjectId(projectId));
        stats.setPendingApprovals(approvalRepository.countByProjectIdAndStatus(projectId, ApprovalStatus.PENDING));
        stats.setApprovedApprovals(approvalRepository.countByProjectIdAndStatus(projectId, ApprovalStatus.APPROVED));
        stats.setRejectedApprovals(approvalRepository.countByProjectIdAndStatus(projectId, ApprovalStatus.REJECTED));
        
        // 项目成员数量
        stats.setTotalMembers(projectMemberRepository.countByProjectIdAndActiveTrue(projectId));
        
        // 最近活动
        stats.setRecentActivities(activityLogService.getProjectRecentActivityLogs(projectId, 10));
        
        // 项目任务状态分布
        stats.setTaskStatusDistribution(getProjectTaskStatusDistribution(projectId));
        
        // 项目成员工作量统计
        stats.setMemberWorkloadStats(getProjectMemberWorkloadStats(projectId));
        
        return stats;
    }

    @Override
    public List<ActivityLogDTO> getRecentActivities(int limit) {
        // 验证当前用户是否为管理员
        if (!securityUtils.isCurrentUserAdmin()) {
            throw new UnauthorizedException("Only administrators can view all recent activities");
        }
        
        return activityLogService.getRecentActivityLogs(limit);
    }

    @Override
    public Map<String, Long> getProjectProgressStats() {
        Map<String, Long> stats = new HashMap<>();
        
        // 获取各状态项目数量
        for (ProjectStatus status : ProjectStatus.values()) {
            stats.put(status.name(), projectRepository.countByStatus(status));
        }
        
        return stats;
    }

    @Override
    public Map<String, Long> getTaskStatusDistribution() {
        Map<String, Long> stats = new HashMap<>();
        
        // 获取各状态任务数量
        for (TaskStatus status : TaskStatus.values()) {
            stats.put(status.name(), taskRepository.countByStatus(status));
        }
        
        return stats;
    }

    @Override
    public Map<String, Long> getApprovalTypeDistribution() {
        Map<String, Long> stats = new HashMap<>();
        
        // 获取各类型审核数量
        for (ApprovalType type : ApprovalType.values()) {
            stats.put(type.name(), approvalRepository.countByType(type));
        }
        
        return stats;
    }

    @Override
    public Map<String, Long> getProjectTaskStatusDistribution(Long projectId) {
        Map<String, Long> stats = new HashMap<>();
        
        // 验证项目存在
        projectRepository.findById(projectId)
                .orElseThrow(() -> new ResourceNotFoundException("Project not found with id: " + projectId));
        
        // 验证用户是项目成员
        Long currentUserId = securityUtils.getCurrentUserId();
        if (!projectService.isProjectMember(projectId, currentUserId)) {
            throw new UnauthorizedException("User is not a member of the project");
        }
        
        // 获取项目各状态任务数量
        for (TaskStatus status : TaskStatus.values()) {
            stats.put(status.name(), taskRepository.countByProjectIdAndStatus(projectId, status));
        }
        
        return stats;
    }

    @Override
    public Map<String, Long> getProjectMemberWorkloadStats(Long projectId) {
        // 验证项目存在
        projectRepository.findById(projectId)
                .orElseThrow(() -> new ResourceNotFoundException("Project not found with id: " + projectId));
        
        // 验证用户是项目成员
        Long currentUserId = securityUtils.getCurrentUserId();
        if (!projectService.isProjectMember(projectId, currentUserId)) {
            throw new UnauthorizedException("User is not a member of the project");
        }
        
        // 获取项目成员列表
        List<ProjectMember> members = projectMemberRepository.findByProjectIdAndActiveTrue(projectId);
        
        Map<String, Long> stats = new HashMap<>();
        
        // 统计每个成员的任务数量
        for (ProjectMember member : members) {
            String memberName = member.getUser().getUsername();
            Long taskCount = taskRepository.countByProjectIdAndAssigneeId(projectId, member.getUser().getId());
            stats.put(memberName, taskCount);
        }
        
        return stats;
    }

    @Override
    public Map<String, Long> getUserProjectStats(Long userId) {
        // 验证用户存在
        userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));
        
        // 验证当前用户是否为管理员或查询自己的统计
        Long currentUserId = securityUtils.getCurrentUserId();
        if (!securityUtils.isCurrentUserAdmin() && !currentUserId.equals(userId)) {
            throw new UnauthorizedException("User does not have permission to view other user's statistics");
        }
        
        Map<String, Long> stats = new HashMap<>();
        
        // 获取用户参与的各状态项目数量
        for (ProjectStatus status : ProjectStatus.values()) {
            stats.put(status.name(), projectRepository.countByUserIdAndStatus(userId, status));
        }
        
        return stats;
    }

    @Override
    public Map<String, Long> getUserTaskStats(Long userId) {
        // 验证用户存在
        userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));
        
        // 验证当前用户是否为管理员或查询自己的统计
        Long currentUserId = securityUtils.getCurrentUserId();
        if (!securityUtils.isCurrentUserAdmin() && !currentUserId.equals(userId)) {
            throw new UnauthorizedException("User does not have permission to view other user's statistics");
        }
        
        Map<String, Long> stats = new HashMap<>();
        
        // 获取用户负责的各状态任务数量
        for (TaskStatus status : TaskStatus.values()) {
            stats.put(status.name(), taskRepository.countByAssigneeIdAndStatus(userId, status));
        }
        
        return stats;
    }

    @Override
    public Map<String, Long> getCurrentUserProjectStats() {
        Long currentUserId = securityUtils.getCurrentUserId();
        return getUserProjectStats(currentUserId);
    }

    @Override
    public Map<String, Long> getCurrentUserTaskStats() {
        Long currentUserId = securityUtils.getCurrentUserId();
        return getUserTaskStats(currentUserId);
    }
}