package com.example.projectmanagement.service.impl;

import com.example.projectmanagement.dto.PageResponse;
import com.example.projectmanagement.dto.TaskDTO;
import com.example.projectmanagement.entity.*;
import com.example.projectmanagement.exception.ResourceNotFoundException;
import com.example.projectmanagement.exception.ValidationException;
import com.example.projectmanagement.repository.ActivityLogRepository;
import com.example.projectmanagement.repository.ProjectRepository;
import com.example.projectmanagement.repository.TaskRepository;
import com.example.projectmanagement.service.ProjectService;
import com.example.projectmanagement.service.TaskService;
import com.example.projectmanagement.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 任务服务实现类
 * 实现任务相关的业务逻辑
 */
@Service
@RequiredArgsConstructor
public class TaskServiceImpl implements TaskService {

    private final TaskRepository taskRepository;
    private final ProjectRepository projectRepository;
    private final ActivityLogRepository activityLogRepository;
    private final ProjectService projectService;
    private final SecurityUtils securityUtils;

    /**
     * 创建新任务
     * @param taskDTO 任务DTO
     * @return 创建成功的任务DTO
     */
    @Override
    @Transactional
    public TaskDTO createTask(TaskDTO taskDTO) {
        // 检查项目是否存在
        Project project = projectRepository.findById(taskDTO.getProjectId())
                .orElseThrow(() -> new ResourceNotFoundException("项目不存在"));
        
        // 检查当前用户是否为项目成员
        User currentUser = securityUtils.getCurrentUser();
        if (!projectService.isProjectMember(project.getId(), currentUser.getId()) && 
                currentUser.getRole() != UserRole.ADMIN) {
            throw new ValidationException("没有权限在该项目中创建任务");
        }
        
        // 创建任务
        Task task = Task.builder()
                .title(taskDTO.getTitle())
                .description(taskDTO.getDescription())
                .status(TaskStatus.TODO) // 默认为待办状态
                .priority(taskDTO.getPriority() != null ? taskDTO.getPriority() : TaskPriority.MEDIUM) // 默认为中等优先级
                .dueDate(taskDTO.getDueDate())
                .estimatedHours(taskDTO.getEstimatedHours())
                .actualHours(0) // 初始实际工时为0
                .project(project)
                .creator(currentUser)
                .assignee(null) // 初始无负责人
                .archived(false)
                .build();
        
        // 如果指定了负责人，设置负责人
        if (taskDTO.getAssigneeId() != null) {
            // 检查负责人是否为项目成员
            if (!projectService.isProjectMember(project.getId(), taskDTO.getAssigneeId())) {
                throw new ValidationException("指定的负责人不是项目成员");
            }
            User assignee = securityUtils.getUserById(taskDTO.getAssigneeId());
            task.setAssignee(assignee);
        }
        
        Task savedTask = taskRepository.save(task);
        
        // 记录活动日志
        ActivityLog activityLog = ActivityLog.builder()
                .activityType(ActivityType.TASK_CREATE)
                .description(String.format("任务 '%s' 已创建", task.getTitle()))
                .user(currentUser)
                .project(project)
                .task(savedTask)
                .ipAddress(securityUtils.getCurrentIpAddress())
                .build();
        activityLogRepository.save(activityLog);
        
        return convertToDTO(savedTask);
    }

    /**
     * 根据ID查找任务
     * @param id 任务ID
     * @return 任务DTO
     */
    @Override
    public TaskDTO getTaskById(Long id) {
        Task task = taskRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("任务不存在"));
        
        // 检查当前用户是否有权限查看该任务
        User currentUser = securityUtils.getCurrentUser();
        if (!projectService.isProjectMember(task.getProject().getId(), currentUser.getId()) && 
                currentUser.getRole() != UserRole.ADMIN) {
            throw new ValidationException("没有权限查看该任务");
        }
        
        return convertToDTO(task);
    }

    /**
     * 更新任务信息
     * @param id 任务ID
     * @param taskDTO 任务DTO
     * @return 更新后的任务DTO
     */
    @Override
    @Transactional
    public TaskDTO updateTask(Long id, TaskDTO taskDTO) {
        Task task = taskRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("任务不存在"));
        
        // 检查当前用户是否有权限更新该任务
        User currentUser = securityUtils.getCurrentUser();
        if (!isTaskEditor(task, currentUser) && currentUser.getRole() != UserRole.ADMIN) {
            throw new ValidationException("没有权限更新该任务");
        }
        
        // 更新任务信息
        task.setTitle(taskDTO.getTitle());
        task.setDescription(taskDTO.getDescription());
        task.setPriority(taskDTO.getPriority());
        task.setDueDate(taskDTO.getDueDate());
        task.setEstimatedHours(taskDTO.getEstimatedHours());
        
        // 如果指定了负责人，更新负责人
        if (taskDTO.getAssigneeId() != null) {
            // 检查负责人是否为项目成员
            if (!projectService.isProjectMember(task.getProject().getId(), taskDTO.getAssigneeId())) {
                throw new ValidationException("指定的负责人不是项目成员");
            }
            User assignee = securityUtils.getUserById(taskDTO.getAssigneeId());
            task.setAssignee(assignee);
        } else {
            task.setAssignee(null); // 清除负责人
        }
        
        Task updatedTask = taskRepository.save(task);
        
        // 记录活动日志
        ActivityLog activityLog = ActivityLog.builder()
                .activityType(ActivityType.TASK_UPDATE)
                .description(String.format("任务 '%s' 信息已更新", task.getTitle()))
                .user(currentUser)
                .project(task.getProject())
                .task(updatedTask)
                .ipAddress(securityUtils.getCurrentIpAddress())
                .build();
        activityLogRepository.save(activityLog);
        
        return convertToDTO(updatedTask);
    }

    /**
     * 删除任务（逻辑删除，设置为归档状态）
     * @param id 任务ID
     * @return 是否删除成功
     */
    @Override
    @Transactional
    public boolean deleteTask(Long id) {
        Task task = taskRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("任务不存在"));
        
        // 检查当前用户是否有权限删除该任务
        User currentUser = securityUtils.getCurrentUser();
        if (!isTaskEditor(task, currentUser) && currentUser.getRole() != UserRole.ADMIN) {
            throw new ValidationException("没有权限删除该任务");
        }
        
        // 逻辑删除（归档）任务
        task.setArchived(true);
        taskRepository.save(task);
        
        // 记录活动日志
        ActivityLog activityLog = ActivityLog.builder()
                .activityType(ActivityType.TASK_ARCHIVE)
                .description(String.format("任务 '%s' 已归档", task.getTitle()))
                .user(currentUser)
                .project(task.getProject())
                .task(task)
                .ipAddress(securityUtils.getCurrentIpAddress())
                .build();
        activityLogRepository.save(activityLog);
        
        return true;
    }

    /**
     * 恢复已归档的任务
     * @param id 任务ID
     * @return 是否恢复成功
     */
    @Override
    @Transactional
    public boolean restoreTask(Long id) {
        Task task = taskRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("任务不存在"));
        
        // 检查当前用户是否有权限恢复该任务
        User currentUser = securityUtils.getCurrentUser();
        if (!isTaskEditor(task, currentUser) && currentUser.getRole() != UserRole.ADMIN) {
            throw new ValidationException("没有权限恢复该任务");
        }
        
        // 恢复任务
        task.setArchived(false);
        taskRepository.save(task);
        
        // 记录活动日志
        ActivityLog activityLog = ActivityLog.builder()
                .activityType(ActivityType.TASK_RESTORE)
                .description(String.format("任务 '%s' 已恢复", task.getTitle()))
                .user(currentUser)
                .project(task.getProject())
                .task(task)
                .ipAddress(securityUtils.getCurrentIpAddress())
                .build();
        activityLogRepository.save(activityLog);
        
        return true;
    }

    /**
     * 获取任务列表（分页）
     * @param pageable 分页参数
     * @param projectId 项目ID（可选）
     * @param status 任务状态（可选）
     * @param priority 任务优先级（可选）
     * @param assigneeId 负责人ID（可选）
     * @param archived 是否归档（可选）
     * @param keyword 关键词（可选，用于搜索任务标题）
     * @return 任务DTO分页结果
     */
    @Override
    public PageResponse<TaskDTO> getAllTasks(Pageable pageable, Long projectId, TaskStatus status, 
                                           TaskPriority priority, Long assigneeId, Boolean archived, String keyword) {
        Page<Task> taskPage;
        
        // 根据条件查询任务
        if (projectId != null) {
            // 检查当前用户是否有权限查看该项目的任务
            User currentUser = securityUtils.getCurrentUser();
            if (!projectService.isProjectMember(projectId, currentUser.getId()) && 
                    currentUser.getRole() != UserRole.ADMIN) {
                throw new ValidationException("没有权限查看该项目的任务");
            }
            
            taskPage = taskRepository.findByProjectIdAndFilters(
                    projectId, status, priority, assigneeId, archived, keyword, pageable);
        } else {
            // 系统管理员可以查看所有任务，普通用户只能查看自己参与的项目的任务
            User currentUser = securityUtils.getCurrentUser();
            if (currentUser.getRole() == UserRole.ADMIN) {
                taskPage = taskRepository.findByFilters(status, priority, assigneeId, archived, keyword, pageable);
            } else {
                taskPage = taskRepository.findByUserProjectsAndFilters(
                        currentUser.getId(), status, priority, assigneeId, archived, keyword, pageable);
            }
        }
        
        // 转换为DTO并返回分页结果
        Page<TaskDTO> taskDTOPage = taskPage.map(this::convertToDTO);
        return PageResponse.fromPage(taskDTOPage);
    }

    /**
     * 获取当前用户创建的任务列表（分页）
     * @param pageable 分页参数
     * @param projectId 项目ID（可选）
     * @param status 任务状态（可选）
     * @param priority 任务优先级（可选）
     * @param archived 是否归档（可选）
     * @param keyword 关键词（可选，用于搜索任务标题）
     * @return 任务DTO分页结果
     */
    @Override
    public PageResponse<TaskDTO> getCurrentUserCreatedTasks(Pageable pageable, Long projectId, TaskStatus status, 
                                                         TaskPriority priority, Boolean archived, String keyword) {
        User currentUser = securityUtils.getCurrentUser();
        Page<Task> taskPage;
        
        // 根据条件查询当前用户创建的任务
        if (projectId != null) {
            // 检查当前用户是否有权限查看该项目的任务
            if (!projectService.isProjectMember(projectId, currentUser.getId()) && 
                    currentUser.getRole() != UserRole.ADMIN) {
                throw new ValidationException("没有权限查看该项目的任务");
            }
            
            taskPage = taskRepository.findByProjectIdAndCreatorIdAndFilters(
                    projectId, currentUser.getId(), status, priority, archived, keyword, pageable);
        } else {
            taskPage = taskRepository.findByCreatorIdAndFilters(
                    currentUser.getId(), status, priority, archived, keyword, pageable);
        }
        
        // 转换为DTO并返回分页结果
        Page<TaskDTO> taskDTOPage = taskPage.map(this::convertToDTO);
        return PageResponse.fromPage(taskDTOPage);
    }

    /**
     * 获取当前用户负责的任务列表（分页）
     * @param pageable 分页参数
     * @param projectId 项目ID（可选）
     * @param status 任务状态（可选）
     * @param priority 任务优先级（可选）
     * @param archived 是否归档（可选）
     * @param keyword 关键词（可选，用于搜索任务标题）
     * @return 任务DTO分页结果
     */
    @Override
    public PageResponse<TaskDTO> getCurrentUserAssignedTasks(Pageable pageable, Long projectId, TaskStatus status, 
                                                          TaskPriority priority, Boolean archived, String keyword) {
        User currentUser = securityUtils.getCurrentUser();
        Page<Task> taskPage;
        
        // 根据条件查询当前用户负责的任务
        if (projectId != null) {
            // 检查当前用户是否有权限查看该项目的任务
            if (!projectService.isProjectMember(projectId, currentUser.getId()) && 
                    currentUser.getRole() != UserRole.ADMIN) {
                throw new ValidationException("没有权限查看该项目的任务");
            }
            
            taskPage = taskRepository.findByProjectIdAndAssigneeIdAndFilters(
                    projectId, currentUser.getId(), status, priority, archived, keyword, pageable);
        } else {
            taskPage = taskRepository.findByAssigneeIdAndFilters(
                    currentUser.getId(), status, priority, archived, keyword, pageable);
        }
        
        // 转换为DTO并返回分页结果
        Page<TaskDTO> taskDTOPage = taskPage.map(this::convertToDTO);
        return PageResponse.fromPage(taskDTOPage);
    }

    /**
     * 获取项目任务列表（分页）
     * @param projectId 项目ID
     * @param pageable 分页参数
     * @param status 任务状态（可选）
     * @param priority 任务优先级（可选）
     * @param assigneeId 负责人ID（可选）
     * @param archived 是否归档（可选）
     * @param keyword 关键词（可选，用于搜索任务标题）
     * @return 任务DTO分页结果
     */
    @Override
    public PageResponse<TaskDTO> getProjectTasks(Long projectId, Pageable pageable, TaskStatus status, 
                                               TaskPriority priority, Long assigneeId, Boolean archived, String keyword) {
        // 检查项目是否存在
        if (!projectRepository.existsById(projectId)) {
            throw new ResourceNotFoundException("项目不存在");
        }
        
        // 检查当前用户是否有权限查看该项目的任务
        User currentUser = securityUtils.getCurrentUser();
        if (!projectService.isProjectMember(projectId, currentUser.getId()) && 
                currentUser.getRole() != UserRole.ADMIN) {
            throw new ValidationException("没有权限查看该项目的任务");
        }
        
        // 根据条件查询项目任务
        Page<Task> taskPage = taskRepository.findByProjectIdAndFilters(
                projectId, status, priority, assigneeId, archived, keyword, pageable);
        
        // 转换为DTO并返回分页结果
        Page<TaskDTO> taskDTOPage = taskPage.map(this::convertToDTO);
        return PageResponse.fromPage(taskDTOPage);
    }

    /**
     * 更新任务状态
     * @param id 任务ID
     * @param status 新状态
     * @return 更新后的任务DTO
     */
    @Override
    @Transactional
    public TaskDTO updateTaskStatus(Long id, TaskStatus status) {
        Task task = taskRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("任务不存在"));
        
        // 检查当前用户是否有权限更新任务状态
        User currentUser = securityUtils.getCurrentUser();
        if (!isTaskEditor(task, currentUser) && currentUser.getRole() != UserRole.ADMIN) {
            throw new ValidationException("没有权限更新任务状态");
        }
        
        // 更新任务状态
        TaskStatus oldStatus = task.getStatus();
        task.setStatus(status);
        Task updatedTask = taskRepository.save(task);
        
        // 记录活动日志
        ActivityLog activityLog = ActivityLog.builder()
                .activityType(ActivityType.TASK_STATUS_CHANGE)
                .description(String.format("任务 '%s' 状态已从 %s 更新为 %s", 
                        task.getTitle(), oldStatus, status))
                .user(currentUser)
                .project(task.getProject())
                .task(updatedTask)
                .ipAddress(securityUtils.getCurrentIpAddress())
                .build();
        activityLogRepository.save(activityLog);
        
        return convertToDTO(updatedTask);
    }

    /**
     * 更新任务优先级
     * @param id 任务ID
     * @param priority 新优先级
     * @return 更新后的任务DTO
     */
    @Override
    @Transactional
    public TaskDTO updateTaskPriority(Long id, TaskPriority priority) {
        Task task = taskRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("任务不存在"));
        
        // 检查当前用户是否有权限更新任务优先级
        User currentUser = securityUtils.getCurrentUser();
        if (!isTaskEditor(task, currentUser) && currentUser.getRole() != UserRole.ADMIN) {
            throw new ValidationException("没有权限更新任务优先级");
        }
        
        // 更新任务优先级
        TaskPriority oldPriority = task.getPriority();
        task.setPriority(priority);
        Task updatedTask = taskRepository.save(task);
        
        // 记录活动日志
        ActivityLog activityLog = ActivityLog.builder()
                .activityType(ActivityType.TASK_PRIORITY_CHANGE)
                .description(String.format("任务 '%s' 优先级已从 %s 更新为 %s", 
                        task.getTitle(), oldPriority, priority))
                .user(currentUser)
                .project(task.getProject())
                .task(updatedTask)
                .ipAddress(securityUtils.getCurrentIpAddress())
                .build();
        activityLogRepository.save(activityLog);
        
        return convertToDTO(updatedTask);
    }

    /**
     * 分配任务负责人
     * @param id 任务ID
     * @param assigneeId 负责人ID
     * @return 更新后的任务DTO
     */
    @Override
    @Transactional
    public TaskDTO assignTask(Long id, Long assigneeId) {
        Task task = taskRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("任务不存在"));
        
        // 检查当前用户是否有权限分配任务
        User currentUser = securityUtils.getCurrentUser();
        if (!isTaskEditor(task, currentUser) && currentUser.getRole() != UserRole.ADMIN) {
            throw new ValidationException("没有权限分配任务");
        }
        
        // 检查负责人是否为项目成员
        if (!projectService.isProjectMember(task.getProject().getId(), assigneeId)) {
            throw new ValidationException("指定的负责人不是项目成员");
        }
        
        // 获取负责人
        User assignee = securityUtils.getUserById(assigneeId);
        
        // 更新任务负责人
        User oldAssignee = task.getAssignee();
        task.setAssignee(assignee);
        Task updatedTask = taskRepository.save(task);
        
        // 记录活动日志
        String description;
        if (oldAssignee == null) {
            description = String.format("任务 '%s' 已分配给 %s", 
                    task.getTitle(), assignee.getUsername());
        } else {
            description = String.format("任务 '%s' 负责人已从 %s 更改为 %s", 
                    task.getTitle(), oldAssignee.getUsername(), assignee.getUsername());
        }
        
        ActivityLog activityLog = ActivityLog.builder()
                .activityType(ActivityType.TASK_ASSIGNEE_CHANGE)
                .description(description)
                .user(currentUser)
                .project(task.getProject())
                .task(updatedTask)
                .ipAddress(securityUtils.getCurrentIpAddress())
                .build();
        activityLogRepository.save(activityLog);
        
        return convertToDTO(updatedTask);
    }
    
    @Override
    @Transactional
    public TaskDTO unassignTask(Long id) {
        Task task = taskRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("任务不存在"));
        
        // 检查当前用户是否有权限取消分配任务
        User currentUser = securityUtils.getCurrentUser();
        if (!isTaskEditor(task, currentUser) && currentUser.getRole() != UserRole.ADMIN) {
            throw new ValidationException("没有权限取消分配任务");
        }
        
        // 取消分配
        task.setAssignee(null);
        Task updatedTask = taskRepository.save(task);
        
        // 记录活动日志
        ActivityLog activityLog = ActivityLog.builder()
                .activityType(ActivityType.TASK_UPDATE)
                .description(String.format("任务 '%s' 的负责人分配已取消", task.getTitle()))
                .user(currentUser)
                .project(task.getProject())
                .task(updatedTask)
                .ipAddress(securityUtils.getCurrentIpAddress())
                .build();
        activityLogRepository.save(activityLog);
        
        return convertToDTO(updatedTask);
    }

    /**
     * 获取即将到期的任务列表（截止日期在未来7天内）
     * @param projectId 项目ID（可选）
     * @return 任务DTO列表
     */
    @Override
    public List<TaskDTO> getUpcomingTasks(Long projectId) {
        User currentUser = securityUtils.getCurrentUser();
        LocalDate today = LocalDate.now();
        LocalDate nextWeek = today.plusDays(7);
        List<Task> tasks;
        
        if (projectId != null) {
            // 检查当前用户是否有权限查看该项目的任务
            if (!projectService.isProjectMember(projectId, currentUser.getId()) && 
                    currentUser.getRole() != UserRole.ADMIN) {
                throw new ValidationException("没有权限查看该项目的任务");
            }
            
            tasks = taskRepository.findByProjectIdAndDueDateBetweenAndStatusNotAndArchivedFalse(
                    projectId, today, nextWeek, TaskStatus.COMPLETED);
        } else if (currentUser.getRole() == UserRole.ADMIN) {
            tasks = taskRepository.findByDueDateBetweenAndStatusNotAndArchivedFalse(
                    today, nextWeek, TaskStatus.COMPLETED);
        } else {
            tasks = taskRepository.findByUserProjectsAndDueDateBetweenAndStatusNotAndArchivedFalse(
                    currentUser.getId(), today, nextWeek, TaskStatus.COMPLETED);
        }
        
        return tasks.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 获取逾期任务列表（截止日期已过但未完成）
     * @param projectId 项目ID（可选）
     * @return 任务DTO列表
     */
    @Override
    public List<TaskDTO> getOverdueTasks(Long projectId) {
        User currentUser = securityUtils.getCurrentUser();
        LocalDate today = LocalDate.now();
        List<Task> tasks;
        
        if (projectId != null) {
            // 检查当前用户是否有权限查看该项目的任务
            if (!projectService.isProjectMember(projectId, currentUser.getId()) && 
                    currentUser.getRole() != UserRole.ADMIN) {
                throw new ValidationException("没有权限查看该项目的任务");
            }
            
            tasks = taskRepository.findByProjectIdAndDueDateBeforeAndStatusNotAndArchivedFalse(
                    projectId, today, TaskStatus.COMPLETED);
        } else if (currentUser.getRole() == UserRole.ADMIN) {
            tasks = taskRepository.findByDueDateBeforeAndStatusNotAndArchivedFalse(
                    today, TaskStatus.COMPLETED, Pageable.unpaged()).getContent();
        } else {
            tasks = taskRepository.findByUserProjectsAndDueDateBeforeAndStatusNotAndArchivedFalse(
                    currentUser.getId(), today, TaskStatus.COMPLETED);
        }
        
        return tasks.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 获取当前用户即将到期的任务列表（截止日期在未来7天内）
     * @return 任务DTO列表
     */
    @Override
    public List<TaskDTO> getCurrentUserUpcomingTasks() {
        User currentUser = securityUtils.getCurrentUser();
        LocalDate today = LocalDate.now();
        LocalDate nextWeek = today.plusDays(7);
        
        List<Task> tasks = taskRepository.findByAssigneeIdAndDueDateBetweenAndStatusNotAndArchivedFalse(
                currentUser.getId(), today, nextWeek, TaskStatus.COMPLETED);
        
        return tasks.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 获取当前用户逾期任务列表（截止日期已过但未完成）
     * @return 任务DTO列表
     */
    @Override
    public List<TaskDTO> getCurrentUserOverdueTasks() {
        User currentUser = securityUtils.getCurrentUser();
        LocalDate today = LocalDate.now();
        
        List<Task> tasks = taskRepository.findByAssigneeIdAndDueDateBeforeAndStatusNotAndArchivedFalse(
                currentUser.getId(), today, TaskStatus.COMPLETED);
        
        return tasks.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 记录任务实际工时
     * @param id 任务ID
     * @param actualHours 实际工时
     * @return 更新后的任务DTO
     */
    @Override
    @Transactional
    public TaskDTO recordTaskActualHours(Long id, Double actualHours) {
        Task task = taskRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("任务不存在"));
        
        // 检查当前用户是否有权限记录实际工时
        User currentUser = securityUtils.getCurrentUser();
        if (!isTaskEditor(task, currentUser) && currentUser.getRole() != UserRole.ADMIN) {
            throw new ValidationException("没有权限记录实际工时");
        }
        
        // 更新实际工时
        task.setActualHours(actualHours.intValue());
        Task updatedTask = taskRepository.save(task);
        
        // 记录活动日志
        ActivityLog activityLog = ActivityLog.builder()
                .activityType(ActivityType.TASK_UPDATE)
                .description(String.format("任务 '%s' 实际工时已更新为 %.1f 小时", 
                        task.getTitle(), actualHours))
                .user(currentUser)
                .project(task.getProject())
                .task(updatedTask)
                .ipAddress(securityUtils.getCurrentIpAddress())
                .build();
        activityLogRepository.save(activityLog);
        
        return convertToDTO(updatedTask);
    }

    /**
     * 获取指定日期范围内的任务列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param projectId 项目ID（可选）
     * @return 任务DTO列表
     */
    @Override
    public List<TaskDTO> getTasksByDateRange(LocalDate startDate, LocalDate endDate, Long projectId) {
        User currentUser = securityUtils.getCurrentUser();
        List<Task> tasks;
        
        if (projectId != null) {
            // 检查当前用户是否有权限查看该项目的任务
            if (!projectService.isProjectMember(projectId, currentUser.getId()) && 
                    currentUser.getRole() != UserRole.ADMIN) {
                throw new ValidationException("没有权限查看该项目的任务");
            }
            
            tasks = taskRepository.findByProjectIdAndDueDateBetweenAndArchivedFalse(
                    projectId, startDate, endDate);
        } else if (currentUser.getRole() == UserRole.ADMIN) {
            tasks = taskRepository.findByDueDateBetweenAndArchivedFalse(startDate, endDate);
        } else {
            tasks = taskRepository.findByUserProjectsAndDueDateBetweenAndArchivedFalse(
                    currentUser.getId(), startDate, endDate);
        }
        
        return tasks.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取指定日期范围内的任务列表（当前用户相关）
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 任务DTO列表
     */
    @Override
    public List<TaskDTO> getTasksByDateRange(LocalDate startDate, LocalDate endDate) {
        User currentUser = securityUtils.getCurrentUser();
        List<Task> tasks = taskRepository.findByDueDateRange(startDate, endDate, currentUser.getId());
        
        return tasks.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 将任务实体转换为DTO
     * @param task 任务实体
     * @return 任务DTO
     */
    @Override
    public TaskDTO convertToDTO(Task task) {
        return TaskDTO.builder()
                .id(task.getId())
                .title(task.getTitle())
                .description(task.getDescription())
                .status(task.getStatus())
                .priority(task.getPriority())
                .dueDate(task.getDueDate())
                .estimatedHours(task.getEstimatedHours())
                .actualHours(task.getActualHours())
                .projectId(task.getProject().getId())
                .projectName(task.getProject().getName())
                .creatorId(task.getCreator().getId())
                .creatorName(task.getCreator().getUsername())
                .assigneeId(task.getAssignee() != null ? task.getAssignee().getId() : null)
                .assigneeName(task.getAssignee() != null ? task.getAssignee().getUsername() : null)
                .archived(task.isArchived())
                .createdAt(task.getCreatedAt())
                .updatedAt(task.getUpdatedAt())
                .build();
    }

    /**
     * 检查用户是否有权限编辑任务
     * 任务创建者、任务负责人、项目管理员可以编辑任务
     * @param task 任务
     * @param user 用户
     * @return 是否有权限编辑
     */
    private boolean isTaskEditor(Task task, User user) {
        // 任务创建者可以编辑
        if (task.getCreator().getId().equals(user.getId())) {
            return true;
        }
        
        // 任务负责人可以编辑
        if (task.getAssignee() != null && task.getAssignee().getId().equals(user.getId())) {
            return true;
        }
        
        // 项目管理员可以编辑
        return projectService.isProjectAdmin(task.getProject().getId(), user.getId());
    }
}