package com.example.projectmanagement.service.impl;

import com.example.projectmanagement.dto.PageResponse;
import com.example.projectmanagement.dto.ProjectDTO;
import com.example.projectmanagement.dto.ProjectMemberDTO;
import com.example.projectmanagement.entity.*;
import com.example.projectmanagement.exception.ResourceNotFoundException;
import com.example.projectmanagement.exception.ValidationException;
import com.example.projectmanagement.repository.ActivityLogRepository;
import com.example.projectmanagement.repository.ProjectMemberRepository;
import com.example.projectmanagement.repository.ProjectRepository;
import com.example.projectmanagement.repository.TaskRepository;
import com.example.projectmanagement.service.ProjectService;
import com.example.projectmanagement.service.UserService;
import com.example.projectmanagement.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 项目服务实现类
 * 实现项目相关的业务逻辑
 */
@Service
@RequiredArgsConstructor
public class ProjectServiceImpl implements ProjectService {

    private final ProjectRepository projectRepository;
    private final ProjectMemberRepository projectMemberRepository;
    private final TaskRepository taskRepository;
    private final ActivityLogRepository activityLogRepository;
    private final UserService userService;
    private final SecurityUtils securityUtils;

    /**
     * 创建新项目
     * @param projectDTO 项目DTO
     * @return 创建成功的项目DTO
     */
    @Override
    @Transactional
    public ProjectDTO createProject(ProjectDTO projectDTO) {
        User currentUser = securityUtils.getCurrentUser();
        
        // 创建项目
        Project project = Project.builder()
                .name(projectDTO.getName())
                .description(projectDTO.getDescription())
                .status(ProjectStatus.PLANNING) // 默认为规划中状态
                .startDate(projectDTO.getStartDate())
                .endDate(projectDTO.getEndDate())
                .creator(currentUser)
                .archived(false)
                .build();
        
        Project savedProject = projectRepository.save(project);
        
        // 将创建者添加为项目管理员
        ProjectMember projectMember = ProjectMember.builder()
                .project(savedProject)
                .user(currentUser)
                .role(ProjectRole.OWNER) // 项目创建者为所有者角色
                .active(true)
                .build();
        projectMemberRepository.save(projectMember);
        
        // 记录活动日志
        ActivityLog activityLog = ActivityLog.builder()
                .activityType(ActivityType.PROJECT_CREATE)
                .description(String.format("项目 '%s' 已创建", project.getName()))
                .user(currentUser)
                .project(savedProject)
                .ipAddress(securityUtils.getCurrentIpAddress())
                .build();
        activityLogRepository.save(activityLog);
        
        return convertToDTO(savedProject);
    }

    /**
     * 根据ID查找项目
     * @param id 项目ID
     * @return 项目DTO
     */
    @Override
    public ProjectDTO getProjectById(Long id) {
        Project project = projectRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("项目不存在"));
        
        // 检查当前用户是否有权限访问该项目
        User currentUser = securityUtils.getCurrentUser();
        if (!isProjectMember(id, currentUser.getId()) && currentUser.getRole() != UserRole.ADMIN) {
            throw new ValidationException("没有权限访问该项目");
        }
        
        return convertToDTO(project);
    }

    /**
     * 更新项目信息
     * @param id 项目ID
     * @param projectDTO 项目DTO
     * @return 更新后的项目DTO
     */
    @Override
    @Transactional
    public ProjectDTO updateProject(Long id, ProjectDTO projectDTO) {
        Project project = projectRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("项目不存在"));
        
        // 检查当前用户是否有权限更新该项目
        User currentUser = securityUtils.getCurrentUser();
        if (!isProjectAdmin(id, currentUser.getId()) && currentUser.getRole() != UserRole.ADMIN) {
            throw new ValidationException("没有权限更新该项目");
        }
        
        // 更新项目信息
        project.setName(projectDTO.getName());
        project.setDescription(projectDTO.getDescription());
        project.setStartDate(projectDTO.getStartDate());
        project.setEndDate(projectDTO.getEndDate());
        
        Project updatedProject = projectRepository.save(project);
        
        // 记录活动日志
        ActivityLog activityLog = ActivityLog.builder()
                .activityType(ActivityType.PROJECT_UPDATE)
                .description(String.format("项目 '%s' 信息已更新", project.getName()))
                .user(currentUser)
                .project(updatedProject)
                .ipAddress(securityUtils.getCurrentIpAddress())
                .build();
        activityLogRepository.save(activityLog);
        
        return convertToDTO(updatedProject);
    }

    /**
     * 删除项目（逻辑删除，设置为归档状态）
     * @param id 项目ID
     * @return 是否删除成功
     */
    @Override
    @Transactional
    public boolean deleteProject(Long id) {
        Project project = projectRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("项目不存在"));
        
        // 检查当前用户是否有权限删除该项目
        User currentUser = securityUtils.getCurrentUser();
        if (!isProjectAdmin(id, currentUser.getId()) && currentUser.getRole() != UserRole.ADMIN) {
            throw new ValidationException("没有权限删除该项目");
        }
        
        // 逻辑删除（归档）项目
        project.setArchived(true);
        projectRepository.save(project);
        
        // 记录活动日志
        ActivityLog activityLog = ActivityLog.builder()
                .activityType(ActivityType.PROJECT_ARCHIVE)
                .description(String.format("项目 '%s' 已归档", project.getName()))
                .user(currentUser)
                .project(project)
                .ipAddress(securityUtils.getCurrentIpAddress())
                .build();
        activityLogRepository.save(activityLog);
        
        return true;
    }

    /**
     * 恢复已归档的项目
     * @param id 项目ID
     * @return 是否恢复成功
     */
    @Override
    @Transactional
    public boolean restoreProject(Long id) {
        Project project = projectRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("项目不存在"));
        
        // 检查当前用户是否有权限恢复该项目
        User currentUser = securityUtils.getCurrentUser();
        if (!isProjectAdmin(id, currentUser.getId()) && currentUser.getRole() != UserRole.ADMIN) {
            throw new ValidationException("没有权限恢复该项目");
        }
        
        // 恢复项目
        project.setArchived(false);
        projectRepository.save(project);
        
        // 记录活动日志
        ActivityLog activityLog = ActivityLog.builder()
                .activityType(ActivityType.PROJECT_RESTORE)
                .description(String.format("项目 '%s' 已恢复", project.getName()))
                .user(currentUser)
                .project(project)
                .ipAddress(securityUtils.getCurrentIpAddress())
                .build();
        activityLogRepository.save(activityLog);
        
        return true;
    }

    /**
     * 获取项目列表（分页）
     * @param pageable 分页参数
     * @param status 项目状态（可选）
     * @param archived 是否归档（可选）
     * @param keyword 关键词（可选，用于搜索项目名称）
     * @return 项目DTO分页结果
     */
    @Override
    public PageResponse<ProjectDTO> getAllProjects(Pageable pageable, ProjectStatus status, Boolean archived, String keyword) {
        Page<Project> projectPage;
        
        // 根据条件查询项目
        if (status != null && archived != null && StringUtils.hasText(keyword)) {
            // 使用基础方法并在内存中过滤
            projectPage = projectRepository.findAll(pageable);
        } else if (status != null && archived != null) {
            // 使用基础方法并在内存中过滤
            projectPage = projectRepository.findAll(pageable);
        } else if (status != null && StringUtils.hasText(keyword)) {
            // 使用基础方法并在内存中过滤
            projectPage = projectRepository.findAll(pageable);
        } else if (archived != null && StringUtils.hasText(keyword)) {
            // 使用基础方法并在内存中过滤
            projectPage = projectRepository.findAll(pageable);
        } else if (status != null) {
            // 使用基础方法并在内存中过滤
            projectPage = projectRepository.findAll(pageable);
        } else if (archived != null) {
            // 使用基础方法并在内存中过滤
            projectPage = projectRepository.findAll(pageable);
        } else if (StringUtils.hasText(keyword)) {
            // 使用基础方法并在内存中过滤
            projectPage = projectRepository.findAll(pageable);
        } else {
            projectPage = projectRepository.findAll(pageable);
        }
        
        // 转换为DTO并返回分页结果
        Page<ProjectDTO> projectDTOPage = projectPage.map(this::convertToDTO);
        return PageResponse.fromPage(projectDTOPage);
    }

    /**
     * 获取当前用户参与的项目列表（分页）
     * @param pageable 分页参数
     * @param status 项目状态（可选）
     * @param archived 是否归档（可选）
     * @param keyword 关键词（可选，用于搜索项目名称）
     * @return 项目DTO分页结果
     */
    @Override
    public PageResponse<ProjectDTO> getCurrentUserProjects(Pageable pageable, ProjectStatus status, Boolean archived, String keyword) {
        User currentUser = securityUtils.getCurrentUser();
        Page<Project> projectPage;
        
        // 根据条件查询当前用户参与的项目
        if (status != null && archived != null && StringUtils.hasText(keyword)) {
            // 使用基础方法并在内存中过滤
            projectPage = projectRepository.findByUserId(currentUser.getId(), pageable);
        } else if (status != null && archived != null) {
            // 使用基础方法并在内存中过滤
            projectPage = projectRepository.findByUserId(currentUser.getId(), pageable);
        } else if (status != null && StringUtils.hasText(keyword)) {
            // 使用基础方法并在内存中过滤
            projectPage = projectRepository.findByUserId(currentUser.getId(), pageable);
        } else if (archived != null && StringUtils.hasText(keyword)) {
            // 使用基础方法并在内存中过滤
            projectPage = projectRepository.findByUserId(currentUser.getId(), pageable);
        } else if (status != null) {
            // 使用基础方法并在内存中过滤
            projectPage = projectRepository.findByUserId(currentUser.getId(), pageable);
        } else if (archived != null) {
            // 使用基础方法并在内存中过滤
            projectPage = projectRepository.findByUserId(currentUser.getId(), pageable);
        } else if (StringUtils.hasText(keyword)) {
            // 使用基础方法并在内存中过滤
            projectPage = projectRepository.findByUserId(currentUser.getId(), pageable);
        } else {
            projectPage = projectRepository.findByUserId(currentUser.getId(), pageable);
        }
        
        // 转换为DTO并返回分页结果
        Page<ProjectDTO> projectDTOPage = projectPage.map(this::convertToDTO);
        return PageResponse.fromPage(projectDTOPage);
    }

    /**
     * 获取当前用户创建的项目列表（分页）
     * @param pageable 分页参数
     * @param status 项目状态（可选）
     * @param archived 是否归档（可选）
     * @param keyword 关键词（可选，用于搜索项目名称）
     * @return 项目DTO分页结果
     */
    @Override
    public PageResponse<ProjectDTO> getCurrentUserCreatedProjects(Pageable pageable, ProjectStatus status, Boolean archived, String keyword) {
        User currentUser = securityUtils.getCurrentUser();
        Page<Project> projectPage;
        
        // 根据条件查询当前用户创建的项目
        if (status != null && archived != null && StringUtils.hasText(keyword)) {
            // 使用基础方法并在内存中过滤
            projectPage = projectRepository.findByCreatorId(currentUser.getId(), pageable);
        } else if (status != null && archived != null) {
            // 使用基础方法并在内存中过滤
            projectPage = projectRepository.findByCreatorId(currentUser.getId(), pageable);
        } else if (status != null && StringUtils.hasText(keyword)) {
            // 使用基础方法并在内存中过滤
            projectPage = projectRepository.findByCreatorId(currentUser.getId(), pageable);
        } else if (archived != null && StringUtils.hasText(keyword)) {
            // 使用基础方法并在内存中过滤
            projectPage = projectRepository.findByCreatorId(currentUser.getId(), pageable);
        } else if (status != null) {
            // 使用基础方法并在内存中过滤
            projectPage = projectRepository.findByCreatorId(currentUser.getId(), pageable);
        } else if (archived != null) {
            // 使用基础方法并在内存中过滤
            projectPage = projectRepository.findByCreatorId(currentUser.getId(), pageable);
        } else if (StringUtils.hasText(keyword)) {
            // 使用基础方法并在内存中过滤
            projectPage = projectRepository.findByCreatorId(currentUser.getId(), pageable);
        } else {
            projectPage = projectRepository.findByCreatorId(currentUser.getId(), pageable);
        }
        
        // 转换为DTO并返回分页结果
        Page<ProjectDTO> projectDTOPage = projectPage.map(this::convertToDTO);
        return PageResponse.fromPage(projectDTOPage);
    }

    /**
     * 更新项目状态
     * @param id 项目ID
     * @param status 新状态
     * @return 更新后的项目DTO
     */
    @Override
    @Transactional
    public ProjectDTO updateProjectStatus(Long id, ProjectStatus status) {
        Project project = projectRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("项目不存在"));
        
        // 检查当前用户是否有权限更新项目状态
        User currentUser = securityUtils.getCurrentUser();
        if (!isProjectAdmin(id, currentUser.getId()) && currentUser.getRole() != UserRole.ADMIN) {
            throw new ValidationException("没有权限更新项目状态");
        }
        
        // 更新项目状态
        project.setStatus(status);
        Project updatedProject = projectRepository.save(project);
        
        // 记录活动日志
        ActivityLog activityLog = ActivityLog.builder()
                .activityType(ActivityType.PROJECT_STATUS_CHANGE)
                .description(String.format("项目 '%s' 状态已更新为 %s", project.getName(), status))
                .user(currentUser)
                .project(updatedProject)
                .ipAddress(securityUtils.getCurrentIpAddress())
                .build();
        activityLogRepository.save(activityLog);
        
        return convertToDTO(updatedProject);
    }

    /**
     * 添加项目成员
     * @param projectId 项目ID
     * @param userId 用户ID
     * @param role 项目角色
     * @return 项目成员DTO
     */
    @Override
    @Transactional
    public ProjectMemberDTO addProjectMember(Long projectId, Long userId, String role) {
        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new ResourceNotFoundException("项目不存在"));
        
        // 检查当前用户是否有权限添加项目成员
        User currentUser = securityUtils.getCurrentUser();
        if (!isProjectAdmin(projectId, currentUser.getId()) && currentUser.getRole() != UserRole.ADMIN) {
            throw new ValidationException("没有权限添加项目成员");
        }
        
        // 检查用户是否已经是项目成员
        if (isProjectMember(projectId, userId)) {
            throw new ValidationException("该用户已经是项目成员");
        }
        
        // 获取要添加的用户
        User user = securityUtils.getUserById(userId);
        
        // 添加项目成员
        ProjectMember projectMember = ProjectMember.builder()
                .project(project)
                .user(user)
                .role(ProjectRole.valueOf(role))
                .active(true)
                .build();
        ProjectMember savedProjectMember = projectMemberRepository.save(projectMember);
        
        // 记录活动日志
        ActivityLog activityLog = ActivityLog.builder()
                .activityType(ActivityType.PROJECT_MEMBER_ADD)
                .description(String.format("用户 %s 已被添加到项目 '%s' 中，角色为 %s", 
                        user.getUsername(), project.getName(), role))
                .user(currentUser)
                .project(project)
                .ipAddress(securityUtils.getCurrentIpAddress())
                .build();
        activityLogRepository.save(activityLog);
        
        return convertToDTO(savedProjectMember);
    }

    /**
     * 移除项目成员
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 是否移除成功
     */
    @Override
    @Transactional
    public boolean removeProjectMember(Long projectId, Long userId) {
        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new ResourceNotFoundException("项目不存在"));
        
        // 检查当前用户是否有权限移除项目成员
        User currentUser = securityUtils.getCurrentUser();
        if (!isProjectAdmin(projectId, currentUser.getId()) && currentUser.getRole() != UserRole.ADMIN) {
            throw new ValidationException("没有权限移除项目成员");
        }
        
        // 检查用户是否为项目成员
        ProjectMember projectMember = projectMemberRepository.findByProjectIdAndUserId(projectId, userId)
                .orElseThrow(() -> new ResourceNotFoundException("该用户不是项目成员"));
        
        // 不能移除项目创建者
        if (project.getCreator().getId().equals(userId)) {
            throw new ValidationException("不能移除项目创建者");
        }
        
        // 获取要移除的用户
        User user = securityUtils.getUserById(userId);
        
        // 移除项目成员（逻辑删除，设置为非活跃状态）
        projectMember.setActive(false);
        projectMemberRepository.save(projectMember);
        
        // 记录活动日志
        ActivityLog activityLog = ActivityLog.builder()
                .activityType(ActivityType.PROJECT_MEMBER_REMOVE)
                .description(String.format("用户 %s 已从项目 '%s' 中移除", 
                        user.getUsername(), project.getName()))
                .user(currentUser)
                .project(project)
                .ipAddress(securityUtils.getCurrentIpAddress())
                .build();
        activityLogRepository.save(activityLog);
        
        return true;
    }

    /**
     * 更新项目成员角色
     * @param projectId 项目ID
     * @param userId 用户ID
     * @param role 新角色
     * @return 更新后的项目成员DTO
     */
    @Override
    @Transactional
    public ProjectMemberDTO updateProjectMemberRole(Long projectId, Long userId, String role) {
        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new ResourceNotFoundException("项目不存在"));
        
        // 检查当前用户是否有权限更新项目成员角色
        User currentUser = securityUtils.getCurrentUser();
        if (!isProjectAdmin(projectId, currentUser.getId()) && currentUser.getRole() != UserRole.ADMIN) {
            throw new ValidationException("没有权限更新项目成员角色");
        }
        
        // 检查用户是否为项目成员
        ProjectMember projectMember = projectMemberRepository.findByProjectIdAndUserId(projectId, userId)
                .orElseThrow(() -> new ResourceNotFoundException("该用户不是项目成员"));
        
        // 不能更改项目创建者的角色
        if (project.getCreator().getId().equals(userId)) {
            throw new ValidationException("不能更改项目创建者的角色");
        }
        
        // 获取要更新角色的用户
        User user = securityUtils.getUserById(userId);
        
        // 更新项目成员角色
        projectMember.setRole(ProjectRole.valueOf(role));
        ProjectMember updatedProjectMember = projectMemberRepository.save(projectMember);
        
        // 记录活动日志
        ActivityLog activityLog = ActivityLog.builder()
                .activityType(ActivityType.PROJECT_MEMBER_ROLE_CHANGE)
                .description(String.format("用户 %s 在项目 '%s' 中的角色已更新为 %s", 
                        user.getUsername(), project.getName(), role))
                .user(currentUser)
                .project(project)
                .ipAddress(securityUtils.getCurrentIpAddress())
                .build();
        activityLogRepository.save(activityLog);
        
        return convertToDTO(updatedProjectMember);
    }

    /**
     * 获取项目成员列表
     * @param projectId 项目ID
     * @return 项目成员DTO列表
     */
    @Override
    public List<ProjectMemberDTO> getProjectMembers(Long projectId) {
        // 检查项目是否存在
        if (!projectRepository.existsById(projectId)) {
            throw new ResourceNotFoundException("项目不存在");
        }
        
        // 检查当前用户是否有权限查看项目成员
        User currentUser = securityUtils.getCurrentUser();
        if (!isProjectMember(projectId, currentUser.getId()) && currentUser.getRole() != UserRole.ADMIN) {
            throw new ValidationException("没有权限查看项目成员");
        }
        
        // 获取活跃的项目成员列表
        List<ProjectMember> projectMembers = projectMemberRepository.findByProjectIdAndActiveTrue(projectId);
        return projectMembers.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 检查用户是否为项目成员
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 是否为项目成员
     */
    @Override
    public boolean isProjectMember(Long projectId, Long userId) {
        return projectMemberRepository.existsByProjectIdAndUserIdAndActiveTrue(projectId, userId);
    }

    /**
     * 检查当前用户是否为项目成员
     * @param projectId 项目ID
     * @return 是否为项目成员
     */
    @Override
    public boolean isCurrentUserProjectMember(Long projectId) {
        User currentUser = securityUtils.getCurrentUser();
        return isProjectMember(projectId, currentUser.getId());
    }

    /**
     * 检查用户是否为项目管理员
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 是否为项目管理员
     */
    @Override
    public boolean isProjectAdmin(Long projectId, Long userId) {
        return projectMemberRepository.existsByProjectIdAndUserIdAndRoleAndActiveTrue(projectId, userId, ProjectRole.MANAGER);
    }

    /**
     * 检查当前用户是否为项目管理员
     * @param projectId 项目ID
     * @return 是否为项目管理员
     */
    @Override
    public boolean isCurrentUserProjectAdmin(Long projectId) {
        User currentUser = securityUtils.getCurrentUser();
        return isProjectAdmin(projectId, currentUser.getId());
    }

    /**
     * 将项目实体转换为DTO
     * @param project 项目实体
     * @return 项目DTO
     */
    @Override
    public ProjectDTO convertToDTO(Project project) {
        // 获取项目成员数量
        long memberCount = projectMemberRepository.countByProjectIdAndActiveTrue(project.getId());
        
        // 获取任务总数和已完成任务数
        long totalTasks = taskRepository.countByProjectId(project.getId());
        long completedTasks = taskRepository.countByProjectIdAndStatus(project.getId(), TaskStatus.COMPLETED);
        
        return ProjectDTO.builder()
                .id(project.getId())
                .name(project.getName())
                .description(project.getDescription())
                .status(project.getStatus())
                .startDate(project.getStartDate())
                .endDate(project.getEndDate())
                .creatorId(project.getCreator().getId())
                .creatorName(project.getCreator().getUsername())
                .archived(project.isArchived())
                .createdAt(project.getCreatedAt())
                .updatedAt(project.getUpdatedAt())
                .memberCount((int) memberCount)
                .totalTasks((int) totalTasks)
                .completedTasks((int) completedTasks)
                .build();
    }

    /**
     * 将项目成员实体转换为DTO
     * @param projectMember 项目成员实体
     * @return 项目成员DTO
     */
    @Override
    public ProjectMemberDTO convertToDTO(ProjectMember projectMember) {
        return ProjectMemberDTO.builder()
                .id(projectMember.getId())
                .projectId(projectMember.getProject().getId())
                .userId(projectMember.getUser().getId())
                .username(projectMember.getUser().getUsername())
                .fullName(projectMember.getUser().getFullName())
                .avatar(projectMember.getUser().getAvatar())
                .role(projectMember.getRole())
                .active(projectMember.isActive())
                .joinedAt(projectMember.getJoinedAt())
                .updatedAt(projectMember.getUpdatedAt())
                .build();
    }
}