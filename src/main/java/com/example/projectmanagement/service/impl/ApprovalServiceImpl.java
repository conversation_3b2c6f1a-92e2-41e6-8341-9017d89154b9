package com.example.projectmanagement.service.impl;

import com.example.projectmanagement.dto.ApprovalDTO;
import com.example.projectmanagement.dto.PageResponse;
import com.example.projectmanagement.entity.*;
import com.example.projectmanagement.exception.ResourceNotFoundException;
import com.example.projectmanagement.exception.UnauthorizedException;
import com.example.projectmanagement.repository.ApprovalRepository;
import com.example.projectmanagement.repository.ProjectRepository;
import com.example.projectmanagement.repository.TaskRepository;
import com.example.projectmanagement.repository.UserRepository;
import com.example.projectmanagement.service.ActivityLogService;
import com.example.projectmanagement.service.ApprovalService;
import com.example.projectmanagement.service.ProjectService;
import com.example.projectmanagement.service.TaskService;
import com.example.projectmanagement.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 审核服务实现类
 */
@Service
@RequiredArgsConstructor
public class ApprovalServiceImpl implements ApprovalService {

    private final ApprovalRepository approvalRepository;
    private final UserRepository userRepository;
    private final ProjectRepository projectRepository;
    private final TaskRepository taskRepository;
    private final ProjectService projectService;
    private final TaskService taskService;
    private final ActivityLogService activityLogService;
    private final SecurityUtils securityUtils;

    @Override
    @Transactional
    public ApprovalDTO createApproval(ApprovalDTO approvalDTO) {
        // 获取当前用户
        Long currentUserId = securityUtils.getCurrentUserId();
        User requester = userRepository.findById(currentUserId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + currentUserId));
        
        // 验证项目存在
        Project project = null;
        if (approvalDTO.getProjectId() != null) {
            project = projectRepository.findById(approvalDTO.getProjectId())
                    .orElseThrow(() -> new ResourceNotFoundException("Project not found with id: " + approvalDTO.getProjectId()));
            
            // 验证用户是项目成员
            if (!projectService.isProjectMember(approvalDTO.getProjectId(), currentUserId)) {
                throw new UnauthorizedException("User is not a member of the project");
            }
        }
        
        // 验证任务存在（如果有）
        Task task = null;
        if (approvalDTO.getTaskId() != null) {
            task = taskRepository.findById(approvalDTO.getTaskId())
                    .orElseThrow(() -> new ResourceNotFoundException("Task not found with id: " + approvalDTO.getTaskId()));
            
            // 如果项目ID为空，则从任务中获取项目ID
            if (approvalDTO.getProjectId() == null) {
                project = task.getProject();
                approvalDTO.setProjectId(project.getId());
            }
        }
        
        // 验证审核人存在
        User approver = null;
        if (approvalDTO.getApproverId() != null) {
            approver = userRepository.findById(approvalDTO.getApproverId())
                    .orElseThrow(() -> new ResourceNotFoundException("Approver not found with id: " + approvalDTO.getApproverId()));
            
            // 验证审核人是项目成员
            if (project != null && !projectService.isProjectMember(project.getId(), approvalDTO.getApproverId())) {
                throw new UnauthorizedException("Approver is not a member of the project");
            }
        }
        
        // 创建审核实体
        Approval approval = new Approval();
        approval.setTitle(approvalDTO.getTitle());
        approval.setDescription(approvalDTO.getDescription());
        approval.setType(approvalDTO.getType());
        approval.setStatus(ApprovalStatus.PENDING); // 新创建的审核状态为待审批
        approval.setProject(project);
        approval.setTask(task);
        approval.setRequester(requester);
        approval.setApprover(approver);
        approval.setCreatedAt(LocalDateTime.now());
        approval.setUpdatedAt(LocalDateTime.now());
        
        // 保存审核
        Approval savedApproval = approvalRepository.save(approval);
        
        // 记录活动日志
        activityLogService.logActivity(
                ActivityType.APPROVAL_CREATED,
                "创建了审核: " + savedApproval.getTitle(),
                currentUserId,
                project != null ? project.getId() : null,
                task != null ? task.getId() : null,
                savedApproval.getId()
        );
        
        return convertToDTO(savedApproval);
    }

    @Override
    public ApprovalDTO getApprovalById(Long id) {
        Approval approval = approvalRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Approval not found with id: " + id));
        
        // 验证用户有权限查看该审核
        Long currentUserId = securityUtils.getCurrentUserId();
        if (approval.getProject() != null && !projectService.isProjectMember(approval.getProject().getId(), currentUserId)) {
            throw new UnauthorizedException("User does not have permission to view this approval");
        }
        
        return convertToDTO(approval);
    }

    @Override
    @Transactional
    public ApprovalDTO updateApproval(Long id, ApprovalDTO approvalDTO) {
        Approval approval = approvalRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Approval not found with id: " + id));
        
        // 验证用户有权限更新该审核（只有申请人可以更新）
        Long currentUserId = securityUtils.getCurrentUserId();
        if (!approval.getRequester().getId().equals(currentUserId)) {
            throw new UnauthorizedException("Only the requester can update the approval");
        }
        
        // 验证审核状态（只有待审批的审核可以更新）
        if (approval.getStatus() != ApprovalStatus.PENDING) {
            throw new UnauthorizedException("Only pending approvals can be updated");
        }
        
        // 更新审核信息
        approval.setTitle(approvalDTO.getTitle());
        approval.setDescription(approvalDTO.getDescription());
        approval.setUpdatedAt(LocalDateTime.now());
        
        // 如果更新了审核人
        if (approvalDTO.getApproverId() != null && 
                (approval.getApprover() == null || !approval.getApprover().getId().equals(approvalDTO.getApproverId()))) {
            User approver = userRepository.findById(approvalDTO.getApproverId())
                    .orElseThrow(() -> new ResourceNotFoundException("Approver not found with id: " + approvalDTO.getApproverId()));
            
            // 验证审核人是项目成员
            if (approval.getProject() != null && 
                    !projectService.isProjectMember(approval.getProject().getId(), approvalDTO.getApproverId())) {
                throw new UnauthorizedException("Approver is not a member of the project");
            }
            
            approval.setApprover(approver);
        }
        
        // 保存更新
        Approval updatedApproval = approvalRepository.save(approval);
        
        // 记录活动日志
        activityLogService.logActivity(
                ActivityType.APPROVAL_UPDATED,
                "更新了审核: " + updatedApproval.getTitle(),
                currentUserId,
                approval.getProject() != null ? approval.getProject().getId() : null,
                approval.getTask() != null ? approval.getTask().getId() : null,
                updatedApproval.getId()
        );
        
        return convertToDTO(updatedApproval);
    }

    @Override
    @Transactional
    public ApprovalDTO processApproval(Long id, ApprovalStatus status, String comment) {
        Approval approval = approvalRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Approval not found with id: " + id));
        
        // 验证用户有权限审批
        Long currentUserId = securityUtils.getCurrentUserId();
        if (!canApprove(id, currentUserId)) {
            throw new UnauthorizedException("User does not have permission to process this approval");
        }
        
        // 验证审核状态（只有待审批的审核可以处理）
        if (approval.getStatus() != ApprovalStatus.PENDING) {
            throw new UnauthorizedException("Only pending approvals can be processed");
        }
        
        // 验证状态参数
        if (status != ApprovalStatus.APPROVED && status != ApprovalStatus.REJECTED) {
            throw new IllegalArgumentException("Status must be either APPROVED or REJECTED");
        }
        
        // 更新审核状态
        approval.setStatus(status);
        approval.setComment(comment);
        approval.setApprovalTime(LocalDateTime.now());
        approval.setUpdatedAt(LocalDateTime.now());
        
        // 如果审核人为空（可能是项目管理员直接审批），则设置为当前用户
        if (approval.getApprover() == null) {
            User approver = userRepository.findById(currentUserId)
                    .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + currentUserId));
            approval.setApprover(approver);
        }
        
        // 保存更新
        Approval processedApproval = approvalRepository.save(approval);
        
        // 根据审核类型和结果执行相应操作
        if (status == ApprovalStatus.APPROVED) {
            handleApprovedApproval(processedApproval);
        }
        
        // 记录活动日志
        String action = status == ApprovalStatus.APPROVED ? "批准" : "拒绝";
        activityLogService.logActivity(
                status == ApprovalStatus.APPROVED ? ActivityType.APPROVAL_APPROVED : ActivityType.APPROVAL_REJECTED,
                action + "了审核: " + processedApproval.getTitle(),
                currentUserId,
                approval.getProject() != null ? approval.getProject().getId() : null,
                approval.getTask() != null ? approval.getTask().getId() : null,
                processedApproval.getId()
        );
        
        return convertToDTO(processedApproval);
    }

    /**
     * 处理已批准的审核
     * @param approval 已批准的审核
     */
    private void handleApprovedApproval(Approval approval) {
        // 根据审核类型执行相应操作
        switch (approval.getType()) {
            case TASK_STATUS_CHANGE:
                if (approval.getTask() != null) {
                    // 更新任务状态为已完成
                    taskService.updateTaskStatus(approval.getTask().getId(), TaskStatus.COMPLETED);
                }
                break;
            case PROJECT_COMPLETION:
                if (approval.getProject() != null) {
                    // 更新项目状态为已完成
                    projectService.updateProjectStatus(approval.getProject().getId(), ProjectStatus.COMPLETED);
                }
                break;
            // 可以根据需要添加其他审核类型的处理逻辑
            default:
                // 对于其他类型的审核，可能不需要额外操作
                break;
        }
    }

    @Override
    @Transactional
    public boolean cancelApproval(Long id) {
        Approval approval = approvalRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Approval not found with id: " + id));
        
        // 验证用户有权限取消审核（只有申请人可以取消）
        Long currentUserId = securityUtils.getCurrentUserId();
        if (!approval.getRequester().getId().equals(currentUserId)) {
            throw new UnauthorizedException("Only the requester can cancel the approval");
        }
        
        // 验证审核状态（只有待审批的审核可以取消）
        if (approval.getStatus() != ApprovalStatus.PENDING) {
            throw new UnauthorizedException("Only pending approvals can be canceled");
        }
        
        // 删除审核
        approvalRepository.delete(approval);
        
        // 记录活动日志
        activityLogService.logActivity(
                ActivityType.APPROVAL_CANCELED,
                "取消了审核: " + approval.getTitle(),
                currentUserId,
                approval.getProject() != null ? approval.getProject().getId() : null,
                approval.getTask() != null ? approval.getTask().getId() : null,
                approval.getId()
        );
        
        return true;
    }

    @Override
    public PageResponse<ApprovalDTO> getAllApprovals(Pageable pageable, Long projectId, Long taskId, 
                                                  Long requesterId, Long approverId, ApprovalStatus status, 
                                                  ApprovalType type, LocalDateTime startDate, 
                                                  LocalDateTime endDate, String keyword) {
        // 构建查询条件
        Page<Approval> approvals = approvalRepository.findByFilters(
                projectId, taskId, requesterId, approverId, status, type, 
                startDate, endDate, keyword, pageable);
        
        // 转换为DTO并返回分页结果
        return PageResponse.of(approvals.map(this::convertToDTO));
    }

    @Override
    public PageResponse<ApprovalDTO> getCurrentUserRequestedApprovals(Pageable pageable, ApprovalStatus status, 
                                                                 ApprovalType type, String keyword) {
        Long currentUserId = securityUtils.getCurrentUserId();
        
        // 查询当前用户发起的审核
        Page<Approval> approvals = approvalRepository.findByFilters(
                null, null, currentUserId, null, status, type, 
                null, null, keyword, pageable);
        
        // 转换为DTO并返回分页结果
        return PageResponse.of(approvals.map(this::convertToDTO));
    }

    @Override
    public PageResponse<ApprovalDTO> getCurrentUserPendingApprovals(Pageable pageable, ApprovalType type, String keyword) {
        Long currentUserId = securityUtils.getCurrentUserId();
        
        // 查询当前用户待审批的审核
        Page<Approval> approvals = approvalRepository.findByFilters(
                null, null, null, currentUserId, ApprovalStatus.PENDING, type, 
                null, null, keyword, pageable);
        
        // 转换为DTO并返回分页结果
        return PageResponse.of(approvals.map(this::convertToDTO));
    }

    @Override
    public PageResponse<ApprovalDTO> getProjectApprovals(Long projectId, Pageable pageable, ApprovalStatus status, 
                                                      ApprovalType type, String keyword) {
        // 验证项目存在
        projectRepository.findById(projectId)
                .orElseThrow(() -> new ResourceNotFoundException("Project not found with id: " + projectId));
        
        // 验证用户是项目成员
        Long currentUserId = securityUtils.getCurrentUserId();
        if (!projectService.isProjectMember(projectId, currentUserId)) {
            throw new UnauthorizedException("User is not a member of the project");
        }
        
        // 查询项目审核
        Page<Approval> approvals = approvalRepository.findByFilters(
                projectId, null, null, null, status, type, 
                null, null, keyword, pageable);
        
        // 转换为DTO并返回分页结果
        return PageResponse.of(approvals.map(this::convertToDTO));
    }

    @Override
    public List<ApprovalDTO> getTaskApprovals(Long taskId) {
        // 验证任务存在
        Task task = taskRepository.findById(taskId)
                .orElseThrow(() -> new ResourceNotFoundException("Task not found with id: " + taskId));
        
        // 验证用户是项目成员
        Long currentUserId = securityUtils.getCurrentUserId();
        if (!projectService.isProjectMember(task.getProject().getId(), currentUserId)) {
            throw new UnauthorizedException("User is not a member of the project");
        }
        
        // 查询任务相关的审核
        List<Approval> approvals = approvalRepository.findByTaskId(taskId);
        
        // 转换为DTO并返回
        return approvals.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public boolean canApprove(Long approvalId, Long userId) {
        Approval approval = approvalRepository.findById(approvalId)
                .orElseThrow(() -> new ResourceNotFoundException("Approval not found with id: " + approvalId));
        
        // 如果用户是指定的审核人，则可以审批
        if (approval.getApprover() != null && approval.getApprover().getId().equals(userId)) {
            return true;
        }
        
        // 如果用户是项目管理员，也可以审批
        if (approval.getProject() != null) {
            return projectService.isProjectAdmin(approval.getProject().getId(), userId);
        }
        
        return false;
    }

    @Override
    public boolean canCurrentUserApprove(Long approvalId) {
        Long currentUserId = securityUtils.getCurrentUserId();
        return canApprove(approvalId, currentUserId);
    }

    @Override
    public ApprovalDTO convertToDTO(Approval approval) {
        if (approval == null) {
            return null;
        }
        
        ApprovalDTO dto = new ApprovalDTO();
        dto.setId(approval.getId());
        dto.setTitle(approval.getTitle());
        dto.setDescription(approval.getDescription());
        dto.setType(approval.getType());
        dto.setStatus(approval.getStatus());
        
        // 设置项目信息
        if (approval.getProject() != null) {
            dto.setProjectId(approval.getProject().getId());
            dto.setProjectName(approval.getProject().getName());
        }
        
        // 设置任务信息
        if (approval.getTask() != null) {
            dto.setTaskId(approval.getTask().getId());
            dto.setTaskTitle(approval.getTask().getTitle());
        }
        
        // 设置申请人信息
        if (approval.getRequester() != null) {
            dto.setRequesterId(approval.getRequester().getId());
            dto.setRequesterUsername(approval.getRequester().getUsername());
            dto.setRequesterFullName(approval.getRequester().getFullName());
        }
        
        // 设置审核人信息
        if (approval.getApprover() != null) {
            dto.setApproverId(approval.getApprover().getId());
            dto.setApproverUsername(approval.getApprover().getUsername());
            dto.setApproverFullName(approval.getApprover().getFullName());
        }
        
        dto.setApprovalTime(approval.getApprovalTime());
        dto.setComment(approval.getComment());
        dto.setCreatedAt(approval.getCreatedAt());
        dto.setUpdatedAt(approval.getUpdatedAt());
        
        return dto;
    }
}