package com.example.projectmanagement.service.impl;

import com.example.projectmanagement.dto.ActivityLogDTO;
import com.example.projectmanagement.dto.PageResponse;
import com.example.projectmanagement.entity.*;
import com.example.projectmanagement.exception.ResourceNotFoundException;
import com.example.projectmanagement.exception.UnauthorizedException;
import com.example.projectmanagement.repository.*;
import com.example.projectmanagement.service.ActivityLogService;
import com.example.projectmanagement.service.ProjectService;
import com.example.projectmanagement.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 活动日志服务实现类
 */
@Service
@RequiredArgsConstructor
public class ActivityLogServiceImpl implements ActivityLogService {

    private final ActivityLogRepository activityLogRepository;
    private final UserRepository userRepository;
    private final ProjectRepository projectRepository;
    private final TaskRepository taskRepository;
    private final ApprovalRepository approvalRepository;
    private final ProjectService projectService;
    private final SecurityUtils securityUtils;

    @Override
    @Transactional
    public ActivityLogDTO logActivity(ActivityType type, String description, Long userId, 
                                    Long projectId, Long taskId, Long approvalId) {
        // 验证用户存在
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));
        
        // 验证项目存在（如果有）
        Project project = null;
        if (projectId != null) {
            project = projectRepository.findById(projectId)
                    .orElseThrow(() -> new ResourceNotFoundException("Project not found with id: " + projectId));
        }
        
        // 验证任务存在（如果有）
        Task task = null;
        if (taskId != null) {
            task = taskRepository.findById(taskId)
                    .orElseThrow(() -> new ResourceNotFoundException("Task not found with id: " + taskId));
        }
        
        // 验证审核存在（如果有）
        Approval approval = null;
        if (approvalId != null) {
            approval = approvalRepository.findById(approvalId)
                    .orElseThrow(() -> new ResourceNotFoundException("Approval not found with id: " + approvalId));
        }
        
        // 创建活动日志实体
        ActivityLog activityLog = new ActivityLog();
        activityLog.setActivityType(type);
        activityLog.setDescription(description);
        activityLog.setUser(user);
        activityLog.setProject(project);
        activityLog.setTask(task);
        activityLog.setApproval(approval);
        activityLog.setIpAddress(securityUtils.getCurrentUserIp());
        activityLog.setCreatedAt(LocalDateTime.now());
        
        // 保存活动日志
        ActivityLog savedActivityLog = activityLogRepository.save(activityLog);
        
        return convertToDTO(savedActivityLog);
    }

    @Override
    @Transactional
    public ActivityLogDTO logCurrentUserActivity(ActivityType type, String description, 
                                              Long projectId, Long taskId, Long approvalId) {
        Long currentUserId = securityUtils.getCurrentUserId();
        return logActivity(type, description, currentUserId, projectId, taskId, approvalId);
    }

    @Override
    public ActivityLogDTO getActivityLogById(Long id) {
        ActivityLog activityLog = activityLogRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Activity log not found with id: " + id));
        
        // 验证用户有权限查看该活动日志
        Long currentUserId = securityUtils.getCurrentUserId();
        if (activityLog.getProject() != null && 
                !projectService.isProjectMember(activityLog.getProject().getId(), currentUserId)) {
            throw new UnauthorizedException("User does not have permission to view this activity log");
        }
        
        return convertToDTO(activityLog);
    }

    @Override
    public PageResponse<ActivityLogDTO> getAllActivityLogs(Pageable pageable, Long userId, Long projectId, 
                                                       Long taskId, Long approvalId, ActivityType type, 
                                                       LocalDateTime startDate, LocalDateTime endDate) {
        // 验证当前用户是否为管理员
        if (!securityUtils.isCurrentUserAdmin()) {
            throw new UnauthorizedException("Only administrators can view all activity logs");
        }
        
        // 构建查询条件 - 使用现有的查询方法
        Page<ActivityLog> activityLogs;
        if (startDate != null && endDate != null) {
            activityLogs = activityLogRepository.findByCreatedAtBetween(startDate, endDate, pageable);
        } else {
            activityLogs = activityLogRepository.findAll(pageable);
        }
        
        // 转换为DTO并返回分页结果
        return PageResponse.of(activityLogs.map(this::convertToDTO));
    }

    @Override
    public PageResponse<ActivityLogDTO> getCurrentUserActivityLogs(Pageable pageable) {
        Long currentUserId = securityUtils.getCurrentUserId();
        
        // 查询当前用户的活动日志
        Page<ActivityLog> activityLogs = activityLogRepository.findByUserId(currentUserId, pageable);
        
        // 转换为DTO并返回分页结果
        return PageResponse.of(activityLogs.map(this::convertToDTO));
    }

    @Override
    public PageResponse<ActivityLogDTO> getProjectActivityLogs(Long projectId, Pageable pageable) {
        // 验证项目存在
        projectRepository.findById(projectId)
                .orElseThrow(() -> new ResourceNotFoundException("Project not found with id: " + projectId));
        
        // 验证用户是项目成员
        Long currentUserId = securityUtils.getCurrentUserId();
        if (!projectService.isProjectMember(projectId, currentUserId)) {
            throw new UnauthorizedException("User is not a member of the project");
        }
        
        // 查询项目活动日志
        Page<ActivityLog> activityLogs = activityLogRepository.findByProjectId(projectId, pageable);
        
        // 转换为DTO并返回分页结果
        return PageResponse.of(activityLogs.map(this::convertToDTO));
    }

    @Override
    public List<ActivityLogDTO> getTaskActivityLogs(Long taskId) {
        // 验证任务存在
        Task task = taskRepository.findById(taskId)
                .orElseThrow(() -> new ResourceNotFoundException("Task not found with id: " + taskId));
        
        // 验证用户是项目成员
        Long currentUserId = securityUtils.getCurrentUserId();
        if (!projectService.isProjectMember(task.getProject().getId(), currentUserId)) {
            throw new UnauthorizedException("User is not a member of the project");
        }
        
        // 查询任务活动日志
        List<ActivityLog> activityLogs = activityLogRepository.findByTaskId(taskId);
        
        // 转换为DTO并返回
        return activityLogs.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<ActivityLogDTO> getApprovalActivityLogs(Long approvalId) {
        // 验证审核存在
        Approval approval = approvalRepository.findById(approvalId)
                .orElseThrow(() -> new ResourceNotFoundException("Approval not found with id: " + approvalId));
        
        // 验证用户有权限查看
        Long currentUserId = securityUtils.getCurrentUserId();
        if (approval.getProject() != null && 
                !projectService.isProjectMember(approval.getProject().getId(), currentUserId)) {
            throw new UnauthorizedException("User does not have permission to view this approval's activity logs");
        }
        
        // 查询审核活动日志
        List<ActivityLog> activityLogs = activityLogRepository.findByApprovalId(approvalId);
        
        // 转换为DTO并返回
        return activityLogs.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<ActivityLogDTO> getRecentActivityLogs(int limit) {
        // 验证当前用户是否为管理员
        if (!securityUtils.isCurrentUserAdmin()) {
            throw new UnauthorizedException("Only administrators can view all recent activity logs");
        }
        
        // 查询最近的活动日志
        List<ActivityLog> activityLogs = activityLogRepository.findTop10ByOrderByCreatedAtDesc();
        
        // 转换为DTO并返回
        return activityLogs.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<ActivityLogDTO> getUserRecentActivityLogs(Long userId, int limit) {
        // 验证用户存在
        userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));
        
        // 验证当前用户是否为管理员或查询自己的活动日志
        Long currentUserId = securityUtils.getCurrentUserId();
        if (!securityUtils.isCurrentUserAdmin() && !currentUserId.equals(userId)) {
            throw new UnauthorizedException("User does not have permission to view other user's activity logs");
        }
        
        // 查询用户最近的活动日志
        List<ActivityLog> activityLogs = activityLogRepository.findTop10ByUserIdOrderByCreatedAtDesc(userId);
        
        // 转换为DTO并返回
        return activityLogs.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<ActivityLogDTO> getProjectRecentActivityLogs(Long projectId, int limit) {
        // 验证项目存在
        projectRepository.findById(projectId)
                .orElseThrow(() -> new ResourceNotFoundException("Project not found with id: " + projectId));
        
        // 验证用户是项目成员
        Long currentUserId = securityUtils.getCurrentUserId();
        if (!projectService.isProjectMember(projectId, currentUserId)) {
            throw new UnauthorizedException("User is not a member of the project");
        }
        
        // 查询项目最近的活动日志
        List<ActivityLog> activityLogs = activityLogRepository.findTop10ByProjectIdOrderByCreatedAtDesc(projectId);
        
        // 转换为DTO并返回
        return activityLogs.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public ActivityLogDTO convertToDTO(ActivityLog activityLog) {
        if (activityLog == null) {
            return null;
        }
        
        ActivityLogDTO dto = new ActivityLogDTO();
        dto.setId(activityLog.getId());
        dto.setActivityType(activityLog.getActivityType());
        dto.setDescription(activityLog.getDescription());
        
        // 设置用户信息
        if (activityLog.getUser() != null) {
            dto.setUserId(activityLog.getUser().getId());
            dto.setUsername(activityLog.getUser().getUsername());
        }
        
        // 设置项目信息
        if (activityLog.getProject() != null) {
            dto.setProjectId(activityLog.getProject().getId());
            dto.setProjectName(activityLog.getProject().getName());
        }
        
        // 设置任务信息
        if (activityLog.getTask() != null) {
            dto.setTaskId(activityLog.getTask().getId());
            dto.setTaskTitle(activityLog.getTask().getTitle());
        }
        
        // 设置审核信息
        if (activityLog.getApproval() != null) {
            dto.setApprovalId(activityLog.getApproval().getId());
            dto.setApprovalTitle(activityLog.getApproval().getTitle());
        }
        
        dto.setIpAddress(activityLog.getIpAddress());
        dto.setCreatedAt(activityLog.getCreatedAt());
        
        return dto;
    }
}