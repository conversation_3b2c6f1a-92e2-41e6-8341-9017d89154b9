package com.example.projectmanagement.service.impl;

import com.example.projectmanagement.dto.UserDTO;
import com.example.projectmanagement.dto.UserRegistrationRequest;
import com.example.projectmanagement.dto.PageResponse;
import com.example.projectmanagement.entity.ActivityLog;
import com.example.projectmanagement.entity.ActivityType;
import com.example.projectmanagement.entity.User;
import com.example.projectmanagement.entity.UserRole;
import com.example.projectmanagement.exception.ResourceNotFoundException;
import com.example.projectmanagement.exception.UserAlreadyExistsException;
import com.example.projectmanagement.exception.ValidationException;
import com.example.projectmanagement.repository.ActivityLogRepository;
import com.example.projectmanagement.repository.UserRepository;
import com.example.projectmanagement.service.UserService;
import com.example.projectmanagement.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户服务实现类
 * 实现用户相关的业务逻辑
 */
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final UserRepository userRepository;
    private final ActivityLogRepository activityLogRepository;
    private final PasswordEncoder passwordEncoder;
    private final SecurityUtils securityUtils;

    /**
     * 注册新用户
     * @param request 用户注册请求
     * @return 注册成功的用户DTO
     */
    @Override
    @Transactional
    public UserDTO registerUser(UserDTO userDTO) {
        // 将UserDTO转换为UserRegistrationRequest
        UserRegistrationRequest request = UserRegistrationRequest.builder()
                .username(userDTO.getUsername())
                .email(userDTO.getEmail())
                .fullName(userDTO.getFullName())
                .phone(userDTO.getPhone())
                .department(userDTO.getDepartment())
                .position(userDTO.getPosition())
                .password("defaultPassword123") // 默认密码，实际应用中应该由前端提供
                .confirmPassword("defaultPassword123")
                .build();
        // 验证用户名和邮箱是否已存在
        if (userRepository.existsByUsername(request.getUsername())) {
            throw new UserAlreadyExistsException("用户名已存在");
        }
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new UserAlreadyExistsException("邮箱已存在");
        }
        
        // 验证密码是否匹配
        if (!request.getPassword().equals(request.getConfirmPassword())) {
            throw new ValidationException("两次输入的密码不一致");
        }
        
        // 创建新用户
        User user = User.builder()
                .username(request.getUsername())
                .password(passwordEncoder.encode(request.getPassword()))
                .email(request.getEmail())
                .fullName(request.getFullName())
                .phone(request.getPhone())
                .department(request.getDepartment())
                .position(request.getPosition())
                .role(UserRole.MEMBER) // 默认为普通成员角色
                .active(true)
                .build();
        
        User savedUser = userRepository.save(user);
        
        // 记录活动日志
        ActivityLog activityLog = ActivityLog.builder()
                .activityType(ActivityType.USER_REGISTER)
                .description(String.format("用户 %s 注册成功", user.getUsername()))
                .user(savedUser)
                .ipAddress(securityUtils.getCurrentIpAddress())
                .build();
        activityLogRepository.save(activityLog);
        
        return convertToDTO(savedUser);
    }

    /**
     * 根据ID查找用户
     * @param id 用户ID
     * @return 用户DTO
     */
    @Override
    public UserDTO getUserById(Long id) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("用户不存在"));
        return convertToDTO(user);
    }

    /**
     * 根据用户名查找用户
     * @param username 用户名
     * @return 用户DTO
     */
    @Override
    public UserDTO getUserByUsername(String username) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("用户不存在"));
        return convertToDTO(user);
    }

    /**
     * 获取当前登录用户
     * @return 当前用户DTO
     */
    @Override
    public UserDTO getCurrentUser() {
        User currentUser = securityUtils.getCurrentUser();
        return convertToDTO(currentUser);
    }

    /**
     * 更新用户信息
     * @param id 用户ID
     * @param userDTO 用户DTO
     * @return 更新后的用户DTO
     */
    @Override
    @Transactional
    public UserDTO updateUser(Long id, UserDTO userDTO) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("用户不存在"));
        
        // 更新用户信息
        user.setFullName(userDTO.getFullName());
        user.setAvatar(userDTO.getAvatar());
        user.setPhone(userDTO.getPhone());
        user.setDepartment(userDTO.getDepartment());
        user.setPosition(userDTO.getPosition());
        
        // 只有管理员可以更改用户角色和状态
        User currentUser = securityUtils.getCurrentUser();
        if (currentUser.getRole() == UserRole.ADMIN) {
            user.setRole(userDTO.getRole());
            user.setActive(userDTO.isActive());
        }
        
        User updatedUser = userRepository.save(user);
        
        // 记录活动日志
        ActivityLog activityLog = ActivityLog.builder()
                .activityType(ActivityType.USER_UPDATE)
                .description(String.format("用户 %s 信息已更新", user.getUsername()))
                .user(currentUser)
                .ipAddress(securityUtils.getCurrentIpAddress())
                .build();
        activityLogRepository.save(activityLog);
        
        return convertToDTO(updatedUser);
    }

    /**
     * 更新当前用户信息
     * @param userDTO 用户DTO
     * @return 更新后的用户DTO
     */
    @Override
    @Transactional
    public UserDTO updateCurrentUser(UserDTO userDTO) {
        User currentUser = securityUtils.getCurrentUser();
        return updateUser(currentUser.getId(), userDTO);
    }

    /**
     * 修改用户密码
     * @param id 用户ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 是否修改成功
     */
    @Override
    @Transactional
    public boolean changePassword(Long id, String oldPassword, String newPassword) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("用户不存在"));
        
        // 验证旧密码
        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw new ValidationException("旧密码不正确");
        }
        
        // 更新密码
        user.setPassword(passwordEncoder.encode(newPassword));
        userRepository.save(user);
        
        // 记录活动日志
        User currentUser = securityUtils.getCurrentUser();
        ActivityLog activityLog = ActivityLog.builder()
                .activityType(ActivityType.USER_PASSWORD_CHANGE)
                .description(String.format("用户 %s 密码已修改", user.getUsername()))
                .user(currentUser)
                .ipAddress(securityUtils.getCurrentIpAddress())
                .build();
        activityLogRepository.save(activityLog);
        
        return true;
    }

    /**
     * 修改当前用户密码
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 是否修改成功
     */
    @Override
    @Transactional
    public boolean changeCurrentUserPassword(String oldPassword, String newPassword) {
        User currentUser = securityUtils.getCurrentUser();
        return changePassword(currentUser.getId(), oldPassword, newPassword);
    }

    /**
     * 获取用户列表（分页）
     * @param pageable 分页参数
     * @return 用户DTO分页结果
     */
    @Override
    public Page<UserDTO> getAllUsers(Pageable pageable) {
        return userRepository.findAll(pageable)
                .map(this::convertToDTO);
    }

    /**
     * 获取用户列表（带过滤条件）
     * @param pageable 分页参数
     * @param keyword 关键词
     * @param departmentId 部门ID
     * @param active 是否活跃
     * @return 用户DTO分页结果
     */
    @Override
    public PageResponse<UserDTO> getAllUsers(Pageable pageable, String keyword, Long departmentId, Boolean active) {
        Page<User> userPage = userRepository.findAll(pageable);
        
        List<UserDTO> userDTOs = userPage.getContent().stream()
                .filter(user -> {
                    // 简单的过滤逻辑
                    if (keyword != null && !keyword.trim().isEmpty()) {
                        return user.getUsername().contains(keyword) || 
                               user.getFullName().contains(keyword) || 
                               user.getEmail().contains(keyword);
                    }
                    if (active != null) {
                        return user.isActive() == active;
                    }
                    return true;
                })
                .map(this::convertToDTO)
                .collect(Collectors.toList());
        
        return PageResponse.<UserDTO>builder()
                .content(userDTOs)
                .page(userPage.getNumber())
                .size(userPage.getSize())
                .totalElements((long) userDTOs.size())
                .totalPages(userPage.getTotalPages())
                .first(userPage.isFirst())
                .last(userPage.isLast())
                .build();
    }

    /**
     * 获取活跃用户列表
     * @return 活跃用户DTO列表
     */
    @Override
    public List<UserDTO> getActiveUsers() {
        return userRepository.findByActiveTrue().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 禁用用户
     * @param id 用户ID
     * @return 是否禁用成功
     */
    @Override
    @Transactional
    public boolean disableUser(Long id) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("用户不存在"));
        
        // 不能禁用自己
        User currentUser = securityUtils.getCurrentUser();
        if (user.getId().equals(currentUser.getId())) {
            throw new ValidationException("不能禁用当前登录用户");
        }
        
        // 只有管理员可以禁用用户
        if (currentUser.getRole() != UserRole.ADMIN) {
            throw new ValidationException("没有权限禁用用户");
        }
        
        user.setActive(false);
        userRepository.save(user);
        
        // 记录活动日志
        ActivityLog activityLog = ActivityLog.builder()
                .activityType(ActivityType.USER_UPDATE)
                .description(String.format("用户 %s 已被禁用", user.getUsername()))
                .user(currentUser)
                .ipAddress(securityUtils.getCurrentIpAddress())
                .build();
        activityLogRepository.save(activityLog);
        
        return true;
    }

    /**
     * 启用用户
     * @param id 用户ID
     * @return 是否启用成功
     */
    @Override
    @Transactional
    public boolean enableUser(Long id) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("用户不存在"));
        
        // 只有管理员可以启用用户
        User currentUser = securityUtils.getCurrentUser();
        if (currentUser.getRole() != UserRole.ADMIN) {
            throw new ValidationException("没有权限启用用户");
        }
        
        user.setActive(true);
        userRepository.save(user);
        
        // 记录活动日志
        ActivityLog activityLog = ActivityLog.builder()
                .activityType(ActivityType.USER_UPDATE)
                .description(String.format("用户 %s 已被启用", user.getUsername()))
                .user(currentUser)
                .ipAddress(securityUtils.getCurrentIpAddress())
                .build();
        activityLogRepository.save(activityLog);
        
        return true;
    }

    /**
     * 检查用户名是否存在
     * @param username 用户名
     * @return 是否存在
     */
    @Override
    public boolean isUsernameExists(String username) {
        return userRepository.existsByUsername(username);
    }

    /**
     * 检查邮箱是否存在
     * @param email 邮箱
     * @return 是否存在
     */
    @Override
    public boolean isEmailExists(String email) {
        return userRepository.existsByEmail(email);
    }

    /**
     * 获取项目成员列表
     * @param projectId 项目ID
     * @return 用户DTO列表
     */
    @Override
    public List<UserDTO> getProjectMembers(Long projectId) {
        return userRepository.findProjectMembers(projectId).stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 将用户实体转换为DTO
     * @param user 用户实体
     * @return 用户DTO
     */
    @Override
    public UserDTO convertToDTO(User user) {
        return UserDTO.builder()
                .id(user.getId())
                .username(user.getUsername())
                .email(user.getEmail())
                .fullName(user.getFullName())
                .avatar(user.getAvatar())
                .phone(user.getPhone())
                .department(user.getDepartment())
                .position(user.getPosition())
                .role(user.getRole())
                .active(user.isActive())
                .createdAt(user.getCreatedAt())
                .updatedAt(user.getUpdatedAt())
                .build();
    }
}