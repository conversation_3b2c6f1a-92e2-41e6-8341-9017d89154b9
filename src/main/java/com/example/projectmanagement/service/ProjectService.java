package com.example.projectmanagement.service;

import com.example.projectmanagement.dto.PageResponse;
import com.example.projectmanagement.dto.ProjectDTO;
import com.example.projectmanagement.dto.ProjectMemberDTO;
import com.example.projectmanagement.entity.Project;
import com.example.projectmanagement.entity.ProjectMember;
import com.example.projectmanagement.entity.ProjectStatus;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 项目服务接口
 * 定义项目相关的业务逻辑方法
 */
public interface ProjectService {

    /**
     * 创建新项目
     * @param projectDTO 项目DTO
     * @return 创建成功的项目DTO
     */
    ProjectDTO createProject(ProjectDTO projectDTO);

    /**
     * 根据ID查找项目
     * @param id 项目ID
     * @return 项目DTO
     */
    ProjectDTO getProjectById(Long id);

    /**
     * 更新项目信息
     * @param id 项目ID
     * @param projectDTO 项目DTO
     * @return 更新后的项目DTO
     */
    ProjectDTO updateProject(Long id, ProjectDTO projectDTO);

    /**
     * 删除项目（逻辑删除，设置为归档状态）
     * @param id 项目ID
     * @return 是否删除成功
     */
    boolean deleteProject(Long id);

    /**
     * 恢复已归档的项目
     * @param id 项目ID
     * @return 是否恢复成功
     */
    boolean restoreProject(Long id);

    /**
     * 获取项目列表（分页）
     * @param pageable 分页参数
     * @param status 项目状态（可选）
     * @param archived 是否归档（可选）
     * @param keyword 关键词（可选，用于搜索项目名称）
     * @return 项目DTO分页结果
     */
    PageResponse<ProjectDTO> getAllProjects(Pageable pageable, ProjectStatus status, Boolean archived, String keyword);

    /**
     * 获取当前用户参与的项目列表（分页）
     * @param pageable 分页参数
     * @param status 项目状态（可选）
     * @param archived 是否归档（可选）
     * @param keyword 关键词（可选，用于搜索项目名称）
     * @return 项目DTO分页结果
     */
    PageResponse<ProjectDTO> getCurrentUserProjects(Pageable pageable, ProjectStatus status, Boolean archived, String keyword);

    /**
     * 获取当前用户创建的项目列表（分页）
     * @param pageable 分页参数
     * @param status 项目状态（可选）
     * @param archived 是否归档（可选）
     * @param keyword 关键词（可选，用于搜索项目名称）
     * @return 项目DTO分页结果
     */
    PageResponse<ProjectDTO> getCurrentUserCreatedProjects(Pageable pageable, ProjectStatus status, Boolean archived, String keyword);

    /**
     * 更新项目状态
     * @param id 项目ID
     * @param status 新状态
     * @return 更新后的项目DTO
     */
    ProjectDTO updateProjectStatus(Long id, ProjectStatus status);

    /**
     * 添加项目成员
     * @param projectId 项目ID
     * @param userId 用户ID
     * @param role 项目角色
     * @return 项目成员DTO
     */
    ProjectMemberDTO addProjectMember(Long projectId, Long userId, String role);

    /**
     * 移除项目成员
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 是否移除成功
     */
    boolean removeProjectMember(Long projectId, Long userId);

    /**
     * 更新项目成员角色
     * @param projectId 项目ID
     * @param userId 用户ID
     * @param role 新角色
     * @return 更新后的项目成员DTO
     */
    ProjectMemberDTO updateProjectMemberRole(Long projectId, Long userId, String role);

    /**
     * 获取项目成员列表
     * @param projectId 项目ID
     * @return 项目成员DTO列表
     */
    List<ProjectMemberDTO> getProjectMembers(Long projectId);

    /**
     * 检查用户是否为项目成员
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 是否为项目成员
     */
    boolean isProjectMember(Long projectId, Long userId);

    /**
     * 检查当前用户是否为项目成员
     * @param projectId 项目ID
     * @return 是否为项目成员
     */
    boolean isCurrentUserProjectMember(Long projectId);

    /**
     * 检查用户是否为项目管理员
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 是否为项目管理员
     */
    boolean isProjectAdmin(Long projectId, Long userId);

    /**
     * 检查当前用户是否为项目管理员
     * @param projectId 项目ID
     * @return 是否为项目管理员
     */
    boolean isCurrentUserProjectAdmin(Long projectId);

    /**
     * 将项目实体转换为DTO
     * @param project 项目实体
     * @return 项目DTO
     */
    ProjectDTO convertToDTO(Project project);

    /**
     * 将项目成员实体转换为DTO
     * @param projectMember 项目成员实体
     * @return 项目成员DTO
     */
    ProjectMemberDTO convertToDTO(ProjectMember projectMember);
}