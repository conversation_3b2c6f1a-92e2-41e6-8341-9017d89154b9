package com.example.projectmanagement.service;

import com.example.projectmanagement.dto.PageResponse;
import com.example.projectmanagement.dto.UserDTO;
import com.example.projectmanagement.dto.UserRegistrationRequest;
import com.example.projectmanagement.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 用户服务接口
 * 定义用户相关的业务逻辑方法
 */
public interface UserService {
    
    /**
     * 注册新用户
     * @param userDTO 用户DTO
     * @return 注册成功的用户DTO
     */
    UserDTO registerUser(UserDTO userDTO);
    
    /**
     * 根据ID查找用户
     * @param id 用户ID
     * @return 用户DTO
     */
    UserDTO getUserById(Long id);
    
    /**
     * 根据用户名查找用户
     * @param username 用户名
     * @return 用户DTO
     */
    UserDTO getUserByUsername(String username);
    
    /**
     * 获取当前登录用户
     * @return 当前用户DTO
     */
    UserDTO getCurrentUser();
    
    /**
     * 更新用户信息
     * @param id 用户ID
     * @param userDTO 用户DTO
     * @return 更新后的用户DTO
     */
    UserDTO updateUser(Long id, UserDTO userDTO);
    
    /**
     * 更新当前用户信息
     * @param userDTO 用户DTO
     * @return 更新后的用户DTO
     */
    UserDTO updateCurrentUser(UserDTO userDTO);
    
    /**
     * 修改用户密码
     * @param id 用户ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 是否修改成功
     */
    boolean changePassword(Long id, String oldPassword, String newPassword);
    
    /**
     * 修改当前用户密码
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 是否修改成功
     */
    boolean changeCurrentUserPassword(String oldPassword, String newPassword);
    
    /**
     * 获取用户列表（分页）
     * @param pageable 分页参数
     * @return 用户DTO分页结果
     */
    Page<UserDTO> getAllUsers(Pageable pageable);
    
    /**
     * 获取用户列表（分页，带搜索条件）
     * @param pageable 分页参数
     * @param keyword 关键词
     * @param departmentId 部门ID
     * @param active 是否活跃
     * @return 用户DTO分页结果
     */
    PageResponse<UserDTO> getAllUsers(Pageable pageable, String keyword, Long departmentId, Boolean active);
    
    /**
     * 获取活跃用户列表
     * @return 活跃用户DTO列表
     */
    List<UserDTO> getActiveUsers();
    
    /**
     * 禁用用户
     * @param id 用户ID
     * @return 是否禁用成功
     */
    boolean disableUser(Long id);
    
    /**
     * 启用用户
     * @param id 用户ID
     * @return 是否启用成功
     */
    boolean enableUser(Long id);
    
    /**
     * 检查用户名是否存在
     * @param username 用户名
     * @return 是否存在
     */
    boolean isUsernameExists(String username);
    
    /**
     * 检查邮箱是否存在
     * @param email 邮箱
     * @return 是否存在
     */
    boolean isEmailExists(String email);
    
    /**
     * 获取项目成员列表
     * @param projectId 项目ID
     * @return 用户DTO列表
     */
    List<UserDTO> getProjectMembers(Long projectId);
    
    /**
     * 将用户实体转换为DTO
     * @param user 用户实体
     * @return 用户DTO
     */
    UserDTO convertToDTO(User user);
}