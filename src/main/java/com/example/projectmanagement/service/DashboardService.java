package com.example.projectmanagement.service;

import com.example.projectmanagement.dto.ActivityLogDTO;
import com.example.projectmanagement.dto.DashboardStatsDTO;

import java.util.List;
import java.util.Map;

/**
 * 仪表板服务接口
 * 定义仪表板统计相关的业务逻辑方法
 */
public interface DashboardService {

    /**
     * 获取系统仪表板统计信息（管理员视图）
     * @return 仪表板统计DTO
     */
    DashboardStatsDTO getSystemStats();

    /**
     * 获取当前用户的仪表板统计信息
     * @return 仪表板统计DTO
     */
    DashboardStatsDTO getCurrentUserStats();

    /**
     * 获取项目仪表板统计信息
     * @param projectId 项目ID
     * @return 仪表板统计DTO
     */
    DashboardStatsDTO getProjectStats(Long projectId);

    /**
     * 获取最近活动日志
     * @param limit 限制数量
     * @return 活动日志DTO列表
     */
    List<ActivityLogDTO> getRecentActivities(int limit);

    /**
     * 获取项目进度统计
     * @return 项目状态及其数量的映射
     */
    Map<String, Long> getProjectProgressStats();

    /**
     * 获取任务状态分布
     * @return 任务状态及其数量的映射
     */
    Map<String, Long> getTaskStatusDistribution();

    /**
     * 获取审核类型分布
     * @return 审核类型及其数量的映射
     */
    Map<String, Long> getApprovalTypeDistribution();

    /**
     * 获取项目任务状态分布
     * @param projectId 项目ID
     * @return 任务状态及其数量的映射
     */
    Map<String, Long> getProjectTaskStatusDistribution(Long projectId);

    /**
     * 获取项目成员工作量统计
     * @param projectId 项目ID
     * @return 用户名及其任务数量的映射
     */
    Map<String, Long> getProjectMemberWorkloadStats(Long projectId);

    /**
     * 获取用户参与的项目统计
     * @param userId 用户ID
     * @return 项目状态及其数量的映射
     */
    Map<String, Long> getUserProjectStats(Long userId);

    /**
     * 获取用户任务统计
     * @param userId 用户ID
     * @return 任务状态及其数量的映射
     */
    Map<String, Long> getUserTaskStats(Long userId);

    /**
     * 获取当前用户参与的项目统计
     * @return 项目状态及其数量的映射
     */
    Map<String, Long> getCurrentUserProjectStats();

    /**
     * 获取当前用户任务统计
     * @return 任务状态及其数量的映射
     */
    Map<String, Long> getCurrentUserTaskStats();
}