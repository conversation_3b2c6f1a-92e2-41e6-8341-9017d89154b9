package com.example.projectmanagement.repository;

import com.example.projectmanagement.entity.Project;
import com.example.projectmanagement.entity.ProjectStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * 项目仓库接口
 * 提供项目相关的数据访问方法
 */
@Repository
public interface ProjectRepository extends JpaRepository<Project, Long> {
    
    /**
     * 查找用户创建的项目
     * @param creatorId 创建者ID
     * @param pageable 分页参数
     * @return 项目分页结果
     */
    Page<Project> findByCreatorId(Long creatorId, Pageable pageable);
    
    /**
     * 查找用户参与的项目
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 项目分页结果
     */
    @Query("SELECT DISTINCT p FROM Project p JOIN p.members m WHERE m.user.id = :userId AND m.active = true AND p.archived = false")
    Page<Project> findProjectsByUserId(Long userId, Pageable pageable);
    
    /**
     * 根据状态查找项目
     * @param status 项目状态
     * @param pageable 分页参数
     * @return 项目分页结果
     */
    Page<Project> findByStatus(ProjectStatus status, Pageable pageable);
    
    /**
     * 查找未归档的项目
     * @param pageable 分页参数
     * @return 项目分页结果
     */
    Page<Project> findByArchivedFalse(Pageable pageable);
    
    /**
     * 查找已归档的项目
     * @param pageable 分页参数
     * @return 项目分页结果
     */
    Page<Project> findByArchivedTrue(Pageable pageable);
    
    /**
     * 查找即将到期的项目（截止日期在未来7天内）
     * @param today 今天日期
     * @param endDate 结束日期（今天+7天）
     * @return 项目列表
     */
    List<Project> findByEndDateBetweenAndArchivedFalse(LocalDate today, LocalDate endDate);
    
    /**
     * 根据名称模糊查询项目
     * @param name 项目名称（部分）
     * @param pageable 分页参数
     * @return 项目分页结果
     */
    Page<Project> findByNameContainingIgnoreCaseAndArchivedFalse(String name, Pageable pageable);
    
    /**
     * 统计用户创建的项目数量
     * @param creatorId 创建者ID
     * @return 项目数量
     */
    long countByCreatorId(Long creatorId);
    
    /**
     * 统计各状态的项目数量
     * @param status 项目状态
     * @return 项目数量
     */
    long countByStatus(ProjectStatus status);
    
    /**
     * 统计活跃项目数量（未归档）
     * @return 项目数量
     */
    long countByArchivedFalse();
    
    /**
     * 统计用户在特定状态下的项目数量
     * @param userId 用户ID
     * @param status 项目状态
     * @return 项目数量
     */
    long countByUserIdAndStatus(Long userId, ProjectStatus status);
    
    /**
     * 统计用户的项目数量
     * @param userId 用户ID
     * @return 项目数量
     */
    long countByUserId(Long userId);
    
    /**
     * 根据用户ID、状态、归档状态和名称查找项目
     * @param userId 用户ID
     * @param status 项目状态
     * @param archived 归档状态
     * @param name 项目名称
     * @param pageable 分页参数
     * @return 项目分页结果
     */
    @Query("SELECT DISTINCT p FROM Project p JOIN p.members m WHERE m.user.id = :userId AND m.active = true AND p.status = :status AND p.archived = :archived AND p.name LIKE %:name%")
    Page<Project> findByUserIdAndStatusAndArchivedAndNameContainingIgnoreCase(Long userId, ProjectStatus status, Boolean archived, String name, Pageable pageable);
    
    /**
     * 根据用户ID、状态和归档状态查找项目
     * @param userId 用户ID
     * @param status 项目状态
     * @param archived 归档状态
     * @param pageable 分页参数
     * @return 项目分页结果
     */
    @Query("SELECT DISTINCT p FROM Project p JOIN p.members m WHERE m.user.id = :userId AND m.active = true AND p.status = :status AND p.archived = :archived")
    Page<Project> findByUserIdAndStatusAndArchived(Long userId, ProjectStatus status, Boolean archived, Pageable pageable);
    
    /**
     * 根据用户ID、状态和名称查找项目
     * @param userId 用户ID
     * @param status 项目状态
     * @param name 项目名称
     * @param pageable 分页参数
     * @return 项目分页结果
     */
    @Query("SELECT DISTINCT p FROM Project p JOIN p.members m WHERE m.user.id = :userId AND m.active = true AND p.status = :status AND p.name LIKE %:name%")
    Page<Project> findByUserIdAndStatusAndNameContainingIgnoreCase(Long userId, ProjectStatus status, String name, Pageable pageable);
    
    /**
     * 根据用户ID、归档状态和名称查找项目
     * @param userId 用户ID
     * @param archived 归档状态
     * @param name 项目名称
     * @param pageable 分页参数
     * @return 项目分页结果
     */
    @Query("SELECT DISTINCT p FROM Project p JOIN p.members m WHERE m.user.id = :userId AND m.active = true AND p.archived = :archived AND p.name LIKE %:name%")
    Page<Project> findByUserIdAndArchivedAndNameContainingIgnoreCase(Long userId, Boolean archived, String name, Pageable pageable);
    
    /**
     * 根据用户ID和归档状态查找项目
     * @param userId 用户ID
     * @param archived 归档状态
     * @param pageable 分页参数
     * @return 项目分页结果
     */
    @Query("SELECT DISTINCT p FROM Project p JOIN p.members m WHERE m.user.id = :userId AND m.active = true AND p.archived = :archived")
     Page<Project> findByUserIdAndArchived(Long userId, Boolean archived, Pageable pageable);
     
     /**
      * 根据归档状态查找项目
      * @param archived 归档状态
      * @param pageable 分页参数
      * @return 项目分页结果
      */
     Page<Project> findByArchived(Boolean archived, Pageable pageable);
     
     /**
      * 根据名称模糊查询项目（不限制归档状态）
      * @param name 项目名称
      * @param pageable 分页参数
      * @return 项目分页结果
      */
     Page<Project> findByNameContainingIgnoreCase(String name, Pageable pageable);
     
     /**
      * 根据用户ID和状态查找项目
      * @param userId 用户ID
      * @param status 项目状态
      * @param pageable 分页参数
      * @return 项目分页结果
      */
     @Query("SELECT DISTINCT p FROM Project p JOIN p.members m WHERE m.user.id = :userId AND m.active = true AND p.status = :status")
     Page<Project> findByUserIdAndStatus(Long userId, ProjectStatus status, Pageable pageable);
     
     /**
      * 根据用户ID和名称查找项目
      * @param userId 用户ID
      * @param name 项目名称
      * @param pageable 分页参数
      * @return 项目分页结果
      */
     @Query("SELECT DISTINCT p FROM Project p JOIN p.members m WHERE m.user.id = :userId AND m.active = true AND p.name LIKE %:name%")
     Page<Project> findByUserIdAndNameContainingIgnoreCase(Long userId, String name, Pageable pageable);
     
     /**
      * 根据用户ID查找项目
      * @param userId 用户ID
      * @param pageable 分页参数
      * @return 项目分页结果
      */
     @Query("SELECT DISTINCT p FROM Project p JOIN p.members m WHERE m.user.id = :userId AND m.active = true")
     Page<Project> findByUserId(Long userId, Pageable pageable);
}