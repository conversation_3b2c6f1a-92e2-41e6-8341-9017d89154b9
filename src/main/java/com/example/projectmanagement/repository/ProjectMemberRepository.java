package com.example.projectmanagement.repository;

import com.example.projectmanagement.entity.ProjectMember;
import com.example.projectmanagement.entity.ProjectRole;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 项目成员仓库接口
 * 提供项目成员相关的数据访问方法
 */
@Repository
public interface ProjectMemberRepository extends JpaRepository<ProjectMember, Long> {
    
    /**
     * 查找项目的所有成员
     * @param projectId 项目ID
     * @return 项目成员列表
     */
    List<ProjectMember> findByProjectId(Long projectId);
    
    /**
     * 查找项目的活跃成员
     * @param projectId 项目ID
     * @return 活跃的项目成员列表
     */
    List<ProjectMember> findByProjectIdAndActiveTrue(Long projectId);
    
    /**
     * 查找用户参与的所有项目成员关系
     * @param userId 用户ID
     * @return 项目成员列表
     */
    List<ProjectMember> findByUserId(Long userId);
    
    /**
     * 查找用户参与的活跃项目成员关系
     * @param userId 用户ID
     * @return 活跃的项目成员列表
     */
    List<ProjectMember> findByUserIdAndActiveTrue(Long userId);
    
    /**
     * 查找特定项目中的特定用户
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 项目成员（可选）
     */
    Optional<ProjectMember> findByProjectIdAndUserId(Long projectId, Long userId);
    
    /**
     * 查找项目中具有特定角色的成员
     * @param projectId 项目ID
     * @param role 项目角色
     * @return 项目成员列表
     */
    List<ProjectMember> findByProjectIdAndRole(Long projectId, ProjectRole role);
    
    /**
     * 检查用户是否是项目成员
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 是否存在
     */
    boolean existsByProjectIdAndUserId(Long projectId, Long userId);
    
    /**
     * 检查用户是否是项目的活跃成员
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 是否存在
     */
    boolean existsByProjectIdAndUserIdAndActiveTrue(Long projectId, Long userId);
    
    /**
     * 统计项目成员数量
     * @param projectId 项目ID
     * @return 成员数量
     */
    long countByProjectId(Long projectId);
    
    /**
     * 统计项目活跃成员数量
     * @param projectId 项目ID
     * @return 活跃成员数量
     */
    long countByProjectIdAndActiveTrue(Long projectId);
    
    /**
     * 检查用户是否是项目中具有特定角色的活跃成员
     * @param projectId 项目ID
     * @param userId 用户ID
     * @param role 项目角色
     * @return 是否存在
     */
    boolean existsByProjectIdAndUserIdAndRoleAndActiveTrue(Long projectId, Long userId, ProjectRole role);
}