package com.example.projectmanagement.repository;

import com.example.projectmanagement.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 用户仓库接口
 * 提供用户相关的数据访问方法
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    
    /**
     * 根据用户名查找用户
     * @param username 用户名
     * @return 用户对象（可选）
     */
    Optional<User> findByUsername(String username);
    
    /**
     * 根据邮箱查找用户
     * @param email 邮箱
     * @return 用户对象（可选）
     */
    Optional<User> findByEmail(String email);
    
    /**
     * 检查用户名是否存在
     * @param username 用户名
     * @return 是否存在
     */
    boolean existsByUsername(String username);
    
    /**
     * 检查邮箱是否存在
     * @param email 邮箱
     * @return 是否存在
     */
    boolean existsByEmail(String email);
    
    /**
     * 查找活跃用户列表
     * @return 活跃用户列表
     */
    List<User> findByActiveTrue();
    
    /**
     * 根据部门查找用户列表
     * @param department 部门
     * @return 用户列表
     */
    List<User> findByDepartment(String department);
    
    /**
     * 统计活跃用户数量
     * @return 活跃用户数量
     */
    long countByActiveTrue();
    
    /**
     * 查找项目成员
     * @param projectId 项目ID
     * @return 用户列表
     */
    @Query("SELECT u FROM User u JOIN u.projectMemberships pm WHERE pm.project.id = :projectId AND pm.active = true")
    List<User> findProjectMembers(Long projectId);
}