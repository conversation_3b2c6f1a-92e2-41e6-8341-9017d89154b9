package com.example.projectmanagement.repository;

import com.example.projectmanagement.entity.Task;
import com.example.projectmanagement.entity.TaskPriority;
import com.example.projectmanagement.entity.TaskStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * 任务仓库接口
 * 提供任务相关的数据访问方法
 */
@Repository
public interface TaskRepository extends JpaRepository<Task, Long> {
    
    /**
     * 查找项目的所有任务
     * @param projectId 项目ID
     * @param pageable 分页参数
     * @return 任务分页结果
     */
    Page<Task> findByProjectId(Long projectId, Pageable pageable);
    
    /**
     * 查找项目的未归档任务
     * @param projectId 项目ID
     * @param pageable 分页参数
     * @return 任务分页结果
     */
    Page<Task> findByProjectIdAndArchivedFalse(Long projectId, Pageable pageable);
    
    /**
     * 查找用户创建的任务
     * @param creatorId 创建者ID
     * @param pageable 分页参数
     * @return 任务分页结果
     */
    Page<Task> findByCreatorId(Long creatorId, Pageable pageable);
    
    /**
     * 查找分配给用户的任务
     * @param assigneeId 负责人ID
     * @param pageable 分页参数
     * @return 任务分页结果
     */
    Page<Task> findByAssigneeId(Long assigneeId, Pageable pageable);
    
    /**
     * 查找分配给用户的未归档任务
     * @param assigneeId 负责人ID
     * @param pageable 分页参数
     * @return 任务分页结果
     */
    Page<Task> findByAssigneeIdAndArchivedFalse(Long assigneeId, Pageable pageable);
    
    /**
     * 根据状态查找任务
     * @param status 任务状态
     * @param pageable 分页参数
     * @return 任务分页结果
     */
    Page<Task> findByStatus(TaskStatus status, Pageable pageable);
    
    /**
     * 根据优先级查找任务
     * @param priority 任务优先级
     * @param pageable 分页参数
     * @return 任务分页结果
     */
    Page<Task> findByPriority(TaskPriority priority, Pageable pageable);
    
    /**
     * 查找即将到期的任务
     * @param today 今天日期
     * @param endDate 结束日期（今天+7天）
     * @param pageable 分页参数
     * @return 任务分页结果
     */
    Page<Task> findByDueDateBetweenAndArchivedFalse(LocalDate today, LocalDate endDate, Pageable pageable);
    
    /**
     * 查找已逾期的任务
     * @param today 今天日期
     * @param pageable 分页参数
     * @return 任务分页结果
     */
    Page<Task> findByDueDateBeforeAndStatusNotAndArchivedFalse(LocalDate today, TaskStatus completedStatus, Pageable pageable);
    
    /**
     * 根据标题模糊查询任务
     * @param title 任务标题（部分）
     * @param pageable 分页参数
     * @return 任务分页结果
     */
    Page<Task> findByTitleContainingIgnoreCaseAndArchivedFalse(String title, Pageable pageable);
    
    /**
     * 统计项目任务数量
     * @param projectId 项目ID
     * @return 任务数量
     */
    long countByProjectId(Long projectId);
    
    /**
     * 统计项目中各状态的任务数量
     * @param projectId 项目ID
     * @param status 任务状态
     * @return 任务数量
     */
    long countByProjectIdAndStatus(Long projectId, TaskStatus status);
    
    /**
     * 统计分配给用户的任务数量
     * @param assigneeId 负责人ID
     * @return 任务数量
     */
    long countByAssigneeId(Long assigneeId);
    
    /**
     * 统计分配给用户的未完成任务数量
     * @param assigneeId 负责人ID
     * @param status 已完成状态
     * @return 任务数量
     */
    long countByAssigneeIdAndStatusNot(Long assigneeId, TaskStatus status);
    
    /**
     * 查询项目的任务状态分布
     * @param projectId 项目ID
     * @return 状态和数量的列表
     */
    @Query("SELECT t.status, COUNT(t) FROM Task t WHERE t.project.id = :projectId AND t.archived = false GROUP BY t.status")
    List<Object[]> getTaskStatusDistribution(Long projectId);
    
    /**
     * 根据状态统计任务数量
     * @param status 任务状态
     * @return 任务数量
     */
    long countByStatus(TaskStatus status);
    
    /**
     * 统计分配给用户或由用户创建的任务数量
     * @param assigneeId 负责人ID
     * @param creatorId 创建者ID
     * @return 任务数量
     */
    long countByAssigneeIdOrCreatorId(Long assigneeId, Long creatorId);
    
    /**
     * 统计分配给用户的特定状态任务数量
     * @param assigneeId 负责人ID
     * @param status 任务状态
     * @return 任务数量
     */
    long countByAssigneeIdAndStatus(Long assigneeId, TaskStatus status);
    
    /**
     * 统计项目中分配给特定用户的任务数量
     * @param projectId 项目ID
     * @param assigneeId 负责人ID
     * @return 任务数量
     */
    long countByProjectIdAndAssigneeId(Long projectId, Long assigneeId);
    
    /**
     * 根据项目ID和过滤条件查找任务
     * @param projectId 项目ID
     * @param status 任务状态
     * @param priority 任务优先级
     * @param assigneeId 负责人ID
     * @param archived 是否归档
     * @param keyword 关键词
     * @param pageable 分页参数
     * @return 任务分页结果
     */
    @Query("SELECT t FROM Task t WHERE t.project.id = :projectId " +
           "AND (:status IS NULL OR t.status = :status) " +
           "AND (:priority IS NULL OR t.priority = :priority) " +
           "AND (:assigneeId IS NULL OR t.assignee.id = :assigneeId) " +
           "AND (:archived IS NULL OR t.archived = :archived) " +
           "AND (:keyword IS NULL OR LOWER(t.title) LIKE LOWER(CONCAT('%', :keyword, '%')) OR LOWER(t.description) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<Task> findByProjectIdAndFilters(Long projectId, TaskStatus status, TaskPriority priority, Long assigneeId, Boolean archived, String keyword, Pageable pageable);
    
    /**
     * 根据过滤条件查找任务
     * @param status 任务状态
     * @param priority 任务优先级
     * @param assigneeId 负责人ID
     * @param archived 是否归档
     * @param keyword 关键词
     * @param pageable 分页参数
     * @return 任务分页结果
     */
    @Query("SELECT t FROM Task t WHERE " +
           "(:status IS NULL OR t.status = :status) " +
           "AND (:priority IS NULL OR t.priority = :priority) " +
           "AND (:assigneeId IS NULL OR t.assignee.id = :assigneeId) " +
           "AND (:archived IS NULL OR t.archived = :archived) " +
           "AND (:keyword IS NULL OR LOWER(t.title) LIKE LOWER(CONCAT('%', :keyword, '%')) OR LOWER(t.description) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<Task> findByFilters(TaskStatus status, TaskPriority priority, Long assigneeId, Boolean archived, String keyword, Pageable pageable);
    
    /**
     * 根据用户项目和过滤条件查找任务
     * @param userId 用户ID
     * @param status 任务状态
     * @param priority 任务优先级
     * @param assigneeId 负责人ID
     * @param archived 是否归档
     * @param keyword 关键词
     * @param pageable 分页参数
     * @return 任务分页结果
     */
    @Query("SELECT t FROM Task t WHERE t.project.id IN " +
            "(SELECT pm.project.id FROM ProjectMember pm WHERE pm.user.id = :userId) " +
            "AND (:status IS NULL OR t.status = :status) " +
            "AND (:priority IS NULL OR t.priority = :priority) " +
            "AND (:assigneeId IS NULL OR t.assignee.id = :assigneeId) " +
            "AND (:archived IS NULL OR t.archived = :archived) " +
            "AND (:keyword IS NULL OR LOWER(t.title) LIKE LOWER(CONCAT('%', :keyword, '%')) OR LOWER(t.description) LIKE LOWER(CONCAT('%', :keyword, '%')))")
     Page<Task> findByUserProjectsAndFilters(Long userId, TaskStatus status, TaskPriority priority, Long assigneeId, Boolean archived, String keyword, Pageable pageable);
     
     /**
      * 查找项目中即将到期的任务
      * @param projectId 项目ID
      * @param startDate 开始日期
      * @param endDate 结束日期
      * @param status 排除的状态
      * @return 任务列表
      */
     List<Task> findByProjectIdAndDueDateBetweenAndStatusNotAndArchivedFalse(Long projectId, LocalDate startDate, LocalDate endDate, TaskStatus status);
     
     /**
      * 查找项目中已逾期的任务
      * @param projectId 项目ID
      * @param date 日期
      * @param status 排除的状态
      * @return 任务列表
      */
     List<Task> findByProjectIdAndDueDateBeforeAndStatusNotAndArchivedFalse(Long projectId, LocalDate date, TaskStatus status);
     
     /**
      * 查找用户项目中已逾期的任务
      * @param userId 用户ID
      * @param date 日期
      * @param status 排除的状态
      * @return 任务列表
      */
     @Query("SELECT t FROM Task t WHERE t.project.id IN " +
            "(SELECT pm.project.id FROM ProjectMember pm WHERE pm.user.id = :userId) " +
            "AND t.dueDate < :date AND t.status != :status AND t.archived = false")
     List<Task> findByUserProjectsAndDueDateBeforeAndStatusNotAndArchivedFalse(Long userId, LocalDate date, TaskStatus status);
     
     /**
      * 查找分配给用户的即将到期任务
      * @param assigneeId 负责人ID
      * @param startDate 开始日期
      * @param endDate 结束日期
      * @param status 排除的状态
      * @return 任务列表
      */
     List<Task> findByAssigneeIdAndDueDateBetweenAndStatusNotAndArchivedFalse(Long assigneeId, LocalDate startDate, LocalDate endDate, TaskStatus status);
     
     /**
      * 查找分配给用户的已逾期任务
      * @param assigneeId 负责人ID
      * @param date 日期
      * @param status 排除的状态
      * @return 任务列表
      */
     List<Task> findByAssigneeIdAndDueDateBeforeAndStatusNotAndArchivedFalse(Long assigneeId, LocalDate date, TaskStatus status);
     
     /**
      * 查找用户项目中即将到期的任务
      * @param userId 用户ID
      * @param startDate 开始日期
      * @param endDate 结束日期
      * @param status 排除的状态
      * @return 任务列表
      */
     @Query("SELECT t FROM Task t WHERE t.project.id IN " +
            "(SELECT pm.project.id FROM ProjectMember pm WHERE pm.user.id = :userId) " +
            "AND t.dueDate BETWEEN :startDate AND :endDate AND t.status != :status AND t.archived = false")
     List<Task> findByUserProjectsAndDueDateBetweenAndStatusNotAndArchivedFalse(Long userId, LocalDate startDate, LocalDate endDate, TaskStatus status);
     
     /**
      * 查找项目中已归档的即将到期任务
      * @param projectId 项目ID
      * @param startDate 开始日期
      * @param endDate 结束日期
      * @return 任务列表
      */
     List<Task> findByProjectIdAndDueDateBetweenAndArchivedFalse(Long projectId, LocalDate startDate, LocalDate endDate);
     
     /**
      * 查找已归档的即将到期任务（无分页）
      * @param startDate 开始日期
      * @param endDate 结束日期
      * @return 任务列表
      */
     List<Task> findByDueDateBetweenAndArchivedFalse(LocalDate startDate, LocalDate endDate);
     
     /**
      * 根据日期范围查找任务
      * @param startDate 开始日期
      * @param endDate 结束日期
      * @param userId 用户ID
      * @return 任务列表
      */
     @Query("SELECT t FROM Task t WHERE t.dueDate BETWEEN :startDate AND :endDate " +
            "AND (t.assignee.id = :userId OR t.creator.id = :userId)")
     List<Task> findByDueDateRange(LocalDate startDate, LocalDate endDate, Long userId);
     
     /**
      * 查找用户项目中已归档的即将到期任务
      * @param userId 用户ID
      * @param startDate 开始日期
      * @param endDate 结束日期
      * @return 任务列表
      */
     @Query("SELECT t FROM Task t WHERE t.project.id IN " +
             "(SELECT pm.project.id FROM ProjectMember pm WHERE pm.user.id = :userId) " +
             "AND t.dueDate BETWEEN :startDate AND :endDate AND t.archived = false")
      List<Task> findByUserProjectsAndDueDateBetweenAndArchivedFalse(Long userId, LocalDate startDate, LocalDate endDate);
      
      /**
       * 根据项目ID、负责人ID和过滤条件查找任务
       * @param projectId 项目ID
       * @param assigneeId 负责人ID
       * @param status 任务状态
       * @param priority 任务优先级
       * @param archived 是否归档
       * @param keyword 关键词
       * @param pageable 分页参数
       * @return 任务分页结果
       */
      @Query("SELECT t FROM Task t WHERE t.project.id = :projectId " +
             "AND t.assignee.id = :assigneeId " +
             "AND (:status IS NULL OR t.status = :status) " +
             "AND (:priority IS NULL OR t.priority = :priority) " +
             "AND (:archived IS NULL OR t.archived = :archived) " +
             "AND (:keyword IS NULL OR LOWER(t.title) LIKE LOWER(CONCAT('%', :keyword, '%')) OR LOWER(t.description) LIKE LOWER(CONCAT('%', :keyword, '%')))")
      Page<Task> findByProjectIdAndAssigneeIdAndFilters(Long projectId, Long assigneeId, TaskStatus status, TaskPriority priority, Boolean archived, String keyword, Pageable pageable);
      
      /**
       * 根据负责人ID和过滤条件查找任务
       * @param assigneeId 负责人ID
       * @param status 任务状态
       * @param priority 任务优先级
       * @param archived 是否归档
       * @param keyword 关键词
       * @param pageable 分页参数
       * @return 任务分页结果
       */
      @Query("SELECT t FROM Task t WHERE t.assignee.id = :assigneeId " +
             "AND (:status IS NULL OR t.status = :status) " +
             "AND (:priority IS NULL OR t.priority = :priority) " +
             "AND (:archived IS NULL OR t.archived = :archived) " +
             "AND (:keyword IS NULL OR LOWER(t.title) LIKE LOWER(CONCAT('%', :keyword, '%')) OR LOWER(t.description) LIKE LOWER(CONCAT('%', :keyword, '%')))")
      Page<Task> findByAssigneeIdAndFilters(Long assigneeId, TaskStatus status, TaskPriority priority, Boolean archived, String keyword, Pageable pageable);
      
      /**
        * 查找即将到期的任务（无状态过滤）
        * @param startDate 开始日期
        * @param endDate 结束日期
        * @param status 排除的状态
        * @return 任务列表
        */
       List<Task> findByDueDateBetweenAndStatusNotAndArchivedFalse(LocalDate startDate, LocalDate endDate, TaskStatus status);
       
       /**
        * 根据项目ID、创建者ID和过滤条件查找任务
        * @param projectId 项目ID
        * @param creatorId 创建者ID
        * @param status 任务状态
        * @param priority 任务优先级
        * @param archived 是否归档
        * @param keyword 关键词
        * @param pageable 分页参数
        * @return 任务分页结果
        */
       @Query("SELECT t FROM Task t WHERE t.project.id = :projectId " +
              "AND t.creator.id = :creatorId " +
              "AND (:status IS NULL OR t.status = :status) " +
              "AND (:priority IS NULL OR t.priority = :priority) " +
              "AND (:archived IS NULL OR t.archived = :archived) " +
              "AND (:keyword IS NULL OR LOWER(t.title) LIKE LOWER(CONCAT('%', :keyword, '%')) OR LOWER(t.description) LIKE LOWER(CONCAT('%', :keyword, '%')))")
       Page<Task> findByProjectIdAndCreatorIdAndFilters(Long projectId, Long creatorId, TaskStatus status, TaskPriority priority, Boolean archived, String keyword, Pageable pageable);
       
       /**
        * 根据创建者ID和过滤条件查找任务
        * @param creatorId 创建者ID
        * @param status 任务状态
        * @param priority 任务优先级
        * @param archived 是否归档
        * @param keyword 关键词
        * @param pageable 分页参数
        * @return 任务分页结果
        */
       @Query("SELECT t FROM Task t WHERE t.creator.id = :creatorId " +
              "AND (:status IS NULL OR t.status = :status) " +
              "AND (:priority IS NULL OR t.priority = :priority) " +
              "AND (:archived IS NULL OR t.archived = :archived) " +
              "AND (:keyword IS NULL OR LOWER(t.title) LIKE LOWER(CONCAT('%', :keyword, '%')) OR LOWER(t.description) LIKE LOWER(CONCAT('%', :keyword, '%')))")
       Page<Task> findByCreatorIdAndFilters(Long creatorId, TaskStatus status, TaskPriority priority, Boolean archived, String keyword, Pageable pageable);
}