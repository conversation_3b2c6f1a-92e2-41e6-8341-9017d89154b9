package com.example.projectmanagement.repository;

import com.example.projectmanagement.entity.ActivityLog;
import com.example.projectmanagement.entity.ActivityType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 活动日志仓库接口
 * 提供活动日志相关的数据访问方法
 */
@Repository
public interface ActivityLogRepository extends JpaRepository<ActivityLog, Long> {
    
    /**
     * 查找用户的活动日志
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 活动日志分页结果
     */
    Page<ActivityLog> findByUserId(Long userId, Pageable pageable);
    
    /**
     * 查找项目的活动日志
     * @param projectId 项目ID
     * @param pageable 分页参数
     * @return 活动日志分页结果
     */
    Page<ActivityLog> findByProjectId(Long projectId, Pageable pageable);
    
    /**
     * 查找任务的活动日志
     * @param taskId 任务ID
     * @param pageable 分页参数
     * @return 活动日志分页结果
     */
    Page<ActivityLog> findByTaskId(Long taskId, Pageable pageable);
    
    /**
     * 查找审核的活动日志
     * @param approvalId 审核ID
     * @param pageable 分页参数
     * @return 活动日志分页结果
     */
    Page<ActivityLog> findByApprovalId(Long approvalId, Pageable pageable);
    
    /**
     * 根据活动类型查找活动日志
     * @param activityType 活动类型
     * @param pageable 分页参数
     * @return 活动日志分页结果
     */
    Page<ActivityLog> findByActivityType(ActivityType activityType, Pageable pageable);
    
    /**
     * 查找特定时间段内的活动日志
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageable 分页参数
     * @return 活动日志分页结果
     */
    Page<ActivityLog> findByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);
    
    /**
     * 查找最近的活动日志
     * @param limit 限制数量
     * @return 活动日志列表
     */
    List<ActivityLog> findTop10ByOrderByCreatedAtDesc();
    
    /**
     * 查找项目的最近活动日志
     * @param projectId 项目ID
     * @param limit 限制数量
     * @return 活动日志列表
     */
    List<ActivityLog> findTop10ByProjectIdOrderByCreatedAtDesc(Long projectId);
    
    /**
     * 查找用户的最近活动日志
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 活动日志列表
     */
    List<ActivityLog> findTop10ByUserIdOrderByCreatedAtDesc(Long userId);
    
    /**
     * 统计项目活动日志数量
     * @param projectId 项目ID
     * @return 活动日志数量
     */
    long countByProjectId(Long projectId);
    
    /**
     * 统计用户活动日志数量
     * @param userId 用户ID
     * @return 活动日志数量
     */
    long countByUserId(Long userId);
    
    /**
     * 统计特定类型的活动日志数量
     * @param activityType 活动类型
     * @return 活动日志数量
     */
    long countByActivityType(ActivityType activityType);
    
    /**
     * 根据用户ID和活动类型查找活动日志
     * @param userId 用户ID
     * @param activityType 活动类型
     * @param pageable 分页参数
     * @return 活动日志分页结果
     */
    Page<ActivityLog> findByUserIdAndActivityType(Long userId, ActivityType activityType, Pageable pageable);
    
    /**
     * 根据项目ID和活动类型查找活动日志
     * @param projectId 项目ID
     * @param activityType 活动类型
     * @param pageable 分页参数
     * @return 活动日志分页结果
     */
    Page<ActivityLog> findByProjectIdAndActivityType(Long projectId, ActivityType activityType, Pageable pageable);
    
    /**
     * 根据用户ID、活动类型和时间范围查找活动日志
     * @param userId 用户ID
     * @param activityType 活动类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageable 分页参数
     * @return 活动日志分页结果
     */
    Page<ActivityLog> findByUserIdAndActivityTypeAndCreatedAtBetween(Long userId, ActivityType activityType, LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);
    
    /**
     * 根据项目ID、活动类型和时间范围查找活动日志
     * @param projectId 项目ID
     * @param activityType 活动类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageable 分页参数
     * @return 活动日志分页结果
     */
    Page<ActivityLog> findByProjectIdAndActivityTypeAndCreatedAtBetween(Long projectId, ActivityType activityType, LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);
    
    /**
     * 查找任务的活动日志（无分页）
     * @param taskId 任务ID
     * @return 活动日志列表
     */
    List<ActivityLog> findByTaskId(Long taskId);
    
    /**
     * 查找审核的活动日志（无分页）
     * @param approvalId 审核ID
     * @return 活动日志列表
     */
    List<ActivityLog> findByApprovalId(Long approvalId);
}