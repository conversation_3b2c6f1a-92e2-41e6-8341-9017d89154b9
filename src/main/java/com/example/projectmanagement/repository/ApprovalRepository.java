package com.example.projectmanagement.repository;

import com.example.projectmanagement.entity.Approval;
import com.example.projectmanagement.entity.ApprovalStatus;
import com.example.projectmanagement.entity.ApprovalType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 审核仓库接口
 * 提供审核相关的数据访问方法
 */
@Repository
public interface ApprovalRepository extends JpaRepository<Approval, Long> {
    
    /**
     * 查找项目的所有审核
     * @param projectId 项目ID
     * @param pageable 分页参数
     * @return 审核分页结果
     */
    Page<Approval> findByProjectId(Long projectId, Pageable pageable);
    
    /**
     * 查找任务的所有审核
     * @param taskId 任务ID
     * @param pageable 分页参数
     * @return 审核分页结果
     */
    Page<Approval> findByTaskId(Long taskId, Pageable pageable);
    
    /**
     * 查找用户发起的审核
     * @param requesterId 申请人ID
     * @param pageable 分页参数
     * @return 审核分页结果
     */
    Page<Approval> findByRequesterId(Long requesterId, Pageable pageable);
    
    /**
     * 查找用户审核的审核
     * @param reviewerId 审核人ID
     * @param pageable 分页参数
     * @return 审核分页结果
     */
    Page<Approval> findByReviewerId(Long reviewerId, Pageable pageable);
    
    /**
     * 根据状态查找审核
     * @param status 审核状态
     * @param pageable 分页参数
     * @return 审核分页结果
     */
    Page<Approval> findByStatus(ApprovalStatus status, Pageable pageable);
    
    /**
     * 根据类型查找审核
     * @param type 审核类型
     * @param pageable 分页参数
     * @return 审核分页结果
     */
    Page<Approval> findByType(ApprovalType type, Pageable pageable);
    
    /**
     * 查找项目中待审核的审核
     * @param projectId 项目ID
     * @param status 审核状态（待审核）
     * @param pageable 分页参数
     * @return 审核分页结果
     */
    Page<Approval> findByProjectIdAndStatus(Long projectId, ApprovalStatus status, Pageable pageable);
    
    /**
     * 查找特定时间段内的审核
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageable 分页参数
     * @return 审核分页结果
     */
    Page<Approval> findByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);
    
    /**
     * 根据标题模糊查询审核
     * @param title 审核标题（部分）
     * @param pageable 分页参数
     * @return 审核分页结果
     */
    Page<Approval> findByTitleContainingIgnoreCase(String title, Pageable pageable);
    
    /**
     * 统计项目审核数量
     * @param projectId 项目ID
     * @return 审核数量
     */
    long countByProjectId(Long projectId);
    
    /**
     * 统计项目中各状态的审核数量
     * @param projectId 项目ID
     * @param status 审核状态
     * @return 审核数量
     */
    long countByProjectIdAndStatus(Long projectId, ApprovalStatus status);
    
    /**
     * 统计用户待审核的数量
     * @param reviewerId 审核人ID
     * @param status 审核状态（待审核）
     * @return 审核数量
     */
    long countByReviewerIdAndStatus(Long reviewerId, ApprovalStatus status);
    
    /**
     * 查询审核类型分布
     * @return 类型和数量的列表
     */
    @Query("SELECT a.type, COUNT(a) FROM Approval a GROUP BY a.type")
    List<Object[]> getApprovalTypeDistribution();
    
    /**
     * 查询审核状态分布
     * @return 状态和数量的列表
     */
    @Query("SELECT a.status, COUNT(a) FROM Approval a GROUP BY a.status")
    List<Object[]> getApprovalStatusDistribution();
    
    /**
     * 根据状态统计审核数量
     * @param status 审核状态
     * @return 审核数量
     */
    long countByStatus(ApprovalStatus status);
    
    /**
     * 统计用户发起或审核的审核数量
     * @param requesterId 申请人ID
     * @param approverId 审核人ID
     * @return 审核数量
     */
    @Query("SELECT COUNT(a) FROM Approval a WHERE a.requester.id = :requesterId OR a.approver.id = :approverId")
    long countByRequesterIdOrApproverId(Long requesterId, Long approverId);

    /**
     * 统计审核人的特定状态审核数量
     * @param approverId 审核人ID
     * @param status 审核状态
     * @return 审核数量
     */
    @Query("SELECT COUNT(a) FROM Approval a WHERE a.approver.id = :approverId AND a.status = :status")
    long countByApproverIdAndStatus(Long approverId, ApprovalStatus status);

    /**
     * 统计申请人的特定状态审核数量
     * @param requesterId 申请人ID
     * @param status 审核状态
     * @return 审核数量
     */
    @Query("SELECT COUNT(a) FROM Approval a WHERE a.requester.id = :requesterId AND a.status = :status")
    long countByRequesterIdAndStatus(Long requesterId, ApprovalStatus status);
    
    /**
     * 根据类型统计审核数量
     * @param type 审核类型
     * @return 审核数量
     */
    long countByType(ApprovalType type);
    
    /**
     * 根据过滤条件查找审核
     * @param requesterId 申请人ID
     * @param reviewerId 审核人ID
     * @param approverId 批准人ID
     * @param projectId 项目ID
     * @param status 审核状态
     * @param type 审核类型
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param keyword 关键词
     * @param pageable 分页参数
     * @return 审核分页结果
     */
    @Query("SELECT a FROM Approval a WHERE " +
           "(:requesterId IS NULL OR a.requester.id = :requesterId) AND " +
           "(:reviewerId IS NULL OR a.reviewer.id = :reviewerId) AND " +
           "(:approverId IS NULL OR a.approver.id = :approverId) AND " +
           "(:projectId IS NULL OR a.project.id = :projectId) AND " +
           "(:status IS NULL OR a.status = :status) AND " +
           "(:type IS NULL OR a.type = :type) AND " +
           "(:startDate IS NULL OR a.createdAt >= :startDate) AND " +
           "(:endDate IS NULL OR a.createdAt <= :endDate) AND " +
           "(:keyword IS NULL OR LOWER(a.title) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<Approval> findByFilters(Long requesterId, Long reviewerId, Long approverId,
                                Long projectId, ApprovalStatus status, ApprovalType type,
                                LocalDateTime startDate, LocalDateTime endDate,
                                String keyword, Pageable pageable);
    
    /**
     * 查找任务的所有审核（无分页）
     * @param taskId 任务ID
     * @return 审核列表
     */
    List<Approval> findByTaskId(Long taskId);
}