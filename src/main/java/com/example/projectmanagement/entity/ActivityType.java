package com.example.projectmanagement.entity;

public enum ActivityType {
    // 用户相关活动
    USER_REGISTER,
    USER_LOGIN,
    USER_LOGOUT,
    USER_UPDATE,
    USER_PASSWORD_CHANGE,
    
    // 项目相关活动
    PROJECT_CREATE,
    PROJECT_UPDATE,
    PROJECT_DELETE,
    PROJECT_ARCHIVE,
    PROJECT_RESTORE,
    PROJECT_MEMBER_ADD,
    PROJECT_MEMBER_REMOVE,
    PROJECT_MEMBER_ROLE_CHANGE,
    PROJECT_STATUS_CHANGE,
    
    // 任务相关活动
    TASK_CREATE,
    TASK_UPDATE,
    TASK_DELETE,
    TASK_ARCHIVE,
    TASK_RESTORE,
    TASK_STATUS_CHANGE,
    TASK_ASSIGNEE_CHANGE,
    TASK_PRIORITY_CHANGE,
    
    // 审核相关活动
    APPROVAL_CREATED,
    APPROVAL_REQUEST,
    APPROVAL_SUBMITTED,
    APPROVAL_APPROVE,
    APPROVAL_APPROVED,
    APPROVA<PERSON>_REJECT,
    APPROVAL_REJECTED,
    APPROVAL_CANCEL,
    APPROVAL_CANCELED,
    APPROVAL_UPDATED,
    APPROVAL_REVIEWED,
    APPROVAL_COMPLETED
}