# 项目管理系统配置文件

# 应用基本配置
spring.application.name=project-management-system
server.port=8080
server.servlet.context-path=/

# 数据库配置
spring.datasource.url=**********************************************************************************************************************************
spring.datasource.username=pm_user
spring.datasource.password=your_password
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# 连接池配置
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.max-lifetime=1200000
spring.datasource.hikari.connection-timeout=20000

# JPA配置
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.properties.hibernate.jdbc.time_zone=UTC

# JSON配置
spring.jackson.time-zone=GMT+8
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.serialization.write-dates-as-timestamps=false

# 文件上传配置
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# JWT配置
jwt.secret=mySecretKey123456789012345678901234567890
jwt.expiration=86400000
jwt.refresh-expiration=604800000

# 日志配置
logging.level.com.example=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

# 邮件配置（可选）
# spring.mail.host=smtp.gmail.com
# spring.mail.port=587
# spring.mail.username=<EMAIL>
# spring.mail.password=your-password
# spring.mail.properties.mail.smtp.auth=true
# spring.mail.properties.mail.smtp.starttls.enable=true

# 缓存配置
spring.cache.type=simple

# 国际化配置
spring.messages.basename=messages
spring.messages.encoding=UTF-8

# 开发环境配置
spring.profiles.active=dev

# 跨域配置
cors.allowed-origins=http://localhost:3000,http://localhost:8081
cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
cors.allowed-headers=*
cors.allow-credentials=true

# 管理端点配置
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=when-authorized

# 应用信息
info.app.name=项目管理系统
info.app.description=一个功能完善的项目管理系统
info.app.version=1.0.0
info.app.encoding=UTF-8
info.app.java.version=11