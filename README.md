# 项目管理系统 (Project Management System)

## 项目简介

这是一个基于Spring Boot的项目管理系统，旨在为团队提供高效的项目协作和任务管理解决方案。系统支持用户认证、项目管理、任务分配、审核流程等核心功能。

## 主要功能

### 核心功能模块
- **用户认证系统**：用户注册、登录、权限管理
- **项目管理**：项目创建、编辑、删除、成员管理
- **待办事项管理**：任务创建、分配、状态跟踪
- **审核流程**：项目审批、权限审核
- **数据统计**：项目进度、任务统计

### 用户角色
- **普通用户**：查看项目、管理个人待办事项
- **项目经理**：创建项目、分配任务、管理团队
- **管理员**：系统管理、用户审核、权限分配

## 技术栈

### 后端技术
- **框架**：Spring Boot 2.7+
- **安全**：Spring Security
- **数据访问**：Spring Data JPA
- **数据库**：MySQL 8.0+
- **构建工具**：Maven
- **Java版本**：JDK 11+

### 前端技术
- **技术**：HTML5 + CSS3 + JavaScript (ES6+)
- **UI框架**：Bootstrap 5 / Tailwind CSS
- **HTTP客户端**：Fetch API / Axios
- **模块化**：ES6 Modules
- **构建工具**：Webpack / Vite (可选)

## 快速开始

### 环境要求
- JDK 11 或更高版本
- Maven 3.6+
- MySQL 8.0+
- Node.js 16+ (前端开发)

### 后端启动
```bash
# 克隆项目
git clone <repository-url>
cd fs2

# 配置数据库
# 修改 src/main/resources/application.properties 中的数据库配置

# 编译并运行
mvn clean install
mvn spring-boot:run
```

### 前端启动
```bash
# 进入前端目录
cd frontend

# 如果使用构建工具，安装依赖
npm install

# 启动开发服务器（如果使用构建工具）
npm run dev
# 或者直接使用Live Server等工具打开index.html

# 构建生产版本（如果使用构建工具）
npm run build
```

## 项目结构

```
fs2/
├── src/main/java/com/example/          # Java源代码
│   ├── controller/                     # 控制器层
│   ├── service/                        # 服务层
│   ├── repository/                     # 数据访问层
│   ├── entity/                         # 实体类
│   ├── dto/                           # 数据传输对象
│   ├── config/                        # 配置类
│   └── security/                      # 安全配置
├── src/main/resources/                # 资源文件
│   ├── application.properties         # 应用配置
│   └── db/migration/                  # 数据库迁移脚本
├── frontend/                          # 前端代码
│   ├── src/                          # Vue.js源代码
│   ├── public/                       # 静态资源
│   └── package.json                  # 前端依赖
├── docs/                             # 项目文档
│   ├── requirements.md               # 需求文档
│   ├── development.md                # 开发文档
│   └── api.md                        # API文档
└── README.md                         # 项目说明
```

## API文档

系统提供RESTful API接口，详细的API文档请参考 [API文档](docs/api.md)

## 开发指南

详细的开发指南和规范请参考 [开发文档](docs/development.md)

## 需求文档

完整的功能需求和业务流程请参考 [需求文档](docs/requirements.md)

## 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

如有问题或建议，请通过以下方式联系：
- 邮箱：[<EMAIL>]
- 项目Issues：[GitHub Issues链接]

## 更新日志

### v1.0.0 (开发中)
- 初始版本
- 实现用户认证系统
- 实现基础项目管理功能
- 实现待办事项管理
- 实现审核流程