<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前后端连接测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">前后端连接测试</h3>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <button id="testHealthBtn" class="btn btn-primary">测试健康检查</button>
                            <button id="testPingBtn" class="btn btn-success">测试Ping</button>
                            <button id="testStatusBtn" class="btn btn-info">测试状态</button>
                            <button id="clearBtn" class="btn btn-warning">清空结果</button>
                        </div>
                        
                        <div id="results" class="mt-3">
                            <!-- 测试结果将显示在这里 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8080';
        
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const alertClass = type === 'success' ? 'alert-success' : 
                              type === 'error' ? 'alert-danger' : 'alert-info';
            
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert ${alertClass}`;
            alertDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            results.appendChild(alertDiv);
        }
        
        // 测试健康检查
        document.getElementById('testHealthBtn').addEventListener('click', async () => {
            try {
                addResult('正在测试健康检查...', 'info');
                const response = await fetch(`${API_BASE_URL}/actuator/health`);
                const data = await response.json();
                
                if (response.ok) {
                    addResult(`健康检查成功: ${JSON.stringify(data)}`, 'success');
                } else {
                    addResult(`健康检查失败: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                addResult(`健康检查错误: ${error.message}`, 'error');
            }
        });
        
        // 测试Ping端点
        document.getElementById('testPingBtn').addEventListener('click', async () => {
            try {
                addResult('正在测试Ping端点...', 'info');
                const response = await fetch(`${API_BASE_URL}/api/test/ping`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    addResult(`Ping测试成功: ${JSON.stringify(data)}`, 'success');
                } else {
                    addResult(`Ping测试失败: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                addResult(`Ping测试错误: ${error.message}`, 'error');
            }
        });

        // 测试状态端点
        document.getElementById('testStatusBtn').addEventListener('click', async () => {
            try {
                addResult('正在测试状态端点...', 'info');
                const response = await fetch(`${API_BASE_URL}/api/test/status`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    addResult(`状态测试成功: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    addResult(`状态测试失败: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                addResult(`状态测试错误: ${error.message}`, 'error');
            }
        });

        // 清空结果
        document.getElementById('clearBtn').addEventListener('click', () => {
            document.getElementById('results').innerHTML = '';
            addResult('结果已清空', 'info');
        });
        
        // 页面加载时自动测试
        window.addEventListener('load', () => {
            addResult('页面加载完成，可以开始测试', 'info');
        });
    </script>
</body>
</html>
