/**
 * Service Worker for PWA Support
 * 提供离线缓存、推送通知等功能
 */

const CACHE_NAME = 'project-management-v1.0.0';
const STATIC_CACHE = 'static-v1.0.0';
const DYNAMIC_CACHE = 'dynamic-v1.0.0';

// 需要缓存的静态资源
const STATIC_ASSETS = [
    '/',
    '/index.html',
    '/login.html',
    '/assets/css/style.css',
    '/assets/css/responsive.css',
    '/assets/js/main.js',
    '/assets/js/api/auth.js',
    '/assets/js/components/dashboard.js',
    '/assets/js/components/projects.js',
    '/assets/js/components/tasks.js',
    '/assets/js/components/users.js',
    '/assets/images/logo.svg',
    '/assets/images/default-avatar.svg',
    '/manifest.json'
];

// 需要网络优先的资源（API请求等）
const NETWORK_FIRST_URLS = [
    '/api/',
    '/auth/'
];

// 需要缓存优先的资源
const CACHE_FIRST_URLS = [
    '/assets/',
    '/images/',
    '.css',
    '.js',
    '.svg',
    '.png',
    '.jpg',
    '.jpeg',
    '.gif',
    '.webp'
];

/**
 * Service Worker 安装事件
 */
self.addEventListener('install', event => {
    console.log('Service Worker 安装中...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE)
            .then(cache => {
                console.log('缓存静态资源...');
                return cache.addAll(STATIC_ASSETS);
            })
            .then(() => {
                console.log('静态资源缓存完成');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('缓存静态资源失败:', error);
            })
    );
});

/**
 * Service Worker 激活事件
 */
self.addEventListener('activate', event => {
    console.log('Service Worker 激活中...');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        // 删除旧版本的缓存
                        if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
                            console.log('删除旧缓存:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker 激活完成');
                return self.clients.claim();
            })
    );
});

/**
 * 网络请求拦截
 */
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);
    
    // 跳过非 GET 请求
    if (request.method !== 'GET') {
        return;
    }
    
    // 跳过 chrome-extension 等特殊协议
    if (!url.protocol.startsWith('http')) {
        return;
    }
    
    event.respondWith(handleRequest(request));
});

/**
 * 处理网络请求
 * @param {Request} request - 请求对象
 * @returns {Promise<Response>} 响应
 */
async function handleRequest(request) {
    const url = new URL(request.url);
    
    try {
        // API 请求 - 网络优先策略
        if (isNetworkFirst(url.pathname)) {
            return await networkFirst(request);
        }
        
        // 静态资源 - 缓存优先策略
        if (isCacheFirst(url.pathname)) {
            return await cacheFirst(request);
        }
        
        // HTML 页面 - 网络优先，缓存后备
        if (request.headers.get('accept')?.includes('text/html')) {
            return await networkFirst(request);
        }
        
        // 默认策略 - 缓存优先
        return await cacheFirst(request);
        
    } catch (error) {
        console.error('请求处理失败:', error);
        
        // 如果是 HTML 请求且离线，返回离线页面
        if (request.headers.get('accept')?.includes('text/html')) {
            return await getOfflinePage();
        }
        
        // 其他请求返回网络错误
        return new Response('网络错误', {
            status: 408,
            statusText: 'Network Error'
        });
    }
}

/**
 * 网络优先策略
 * @param {Request} request - 请求对象
 * @returns {Promise<Response>} 响应
 */
async function networkFirst(request) {
    try {
        // 尝试网络请求
        const networkResponse = await fetch(request);
        
        // 如果是成功响应，缓存它
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
        
    } catch (error) {
        // 网络失败，尝试从缓存获取
        console.log('网络请求失败，尝试缓存:', request.url);
        const cachedResponse = await caches.match(request);
        
        if (cachedResponse) {
            return cachedResponse;
        }
        
        throw error;
    }
}

/**
 * 缓存优先策略
 * @param {Request} request - 请求对象
 * @returns {Promise<Response>} 响应
 */
async function cacheFirst(request) {
    // 先尝试从缓存获取
    const cachedResponse = await caches.match(request);
    
    if (cachedResponse) {
        return cachedResponse;
    }
    
    // 缓存中没有，尝试网络请求
    try {
        const networkResponse = await fetch(request);
        
        // 缓存成功的响应
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
        
    } catch (error) {
        console.error('网络和缓存都失败:', request.url, error);
        throw error;
    }
}

/**
 * 获取离线页面
 * @returns {Promise<Response>} 离线页面响应
 */
async function getOfflinePage() {
    const cache = await caches.open(STATIC_CACHE);
    const cachedResponse = await cache.match('/index.html');
    
    if (cachedResponse) {
        return cachedResponse;
    }
    
    // 如果没有缓存的页面，返回简单的离线提示
    return new Response(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>离线模式</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <style>
                body {
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    height: 100vh;
                    margin: 0;
                    background: #f5f5f5;
                    color: #333;
                }
                .offline-container {
                    text-align: center;
                    padding: 2rem;
                    background: white;
                    border-radius: 8px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }
                .offline-icon {
                    font-size: 4rem;
                    margin-bottom: 1rem;
                }
                h1 {
                    margin: 0 0 1rem 0;
                    color: #666;
                }
                p {
                    margin: 0;
                    color: #888;
                }
                .retry-btn {
                    margin-top: 1rem;
                    padding: 0.5rem 1rem;
                    background: #007bff;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;
                }
                .retry-btn:hover {
                    background: #0056b3;
                }
            </style>
        </head>
        <body>
            <div class="offline-container">
                <div class="offline-icon">📱</div>
                <h1>当前处于离线模式</h1>
                <p>请检查网络连接后重试</p>
                <button class="retry-btn" onclick="location.reload()">重新加载</button>
            </div>
        </body>
        </html>
    `, {
        headers: {
            'Content-Type': 'text/html; charset=utf-8'
        }
    });
}

/**
 * 判断是否使用网络优先策略
 * @param {string} pathname - 路径
 * @returns {boolean} 是否网络优先
 */
function isNetworkFirst(pathname) {
    return NETWORK_FIRST_URLS.some(url => pathname.startsWith(url));
}

/**
 * 判断是否使用缓存优先策略
 * @param {string} pathname - 路径
 * @returns {boolean} 是否缓存优先
 */
function isCacheFirst(pathname) {
    return CACHE_FIRST_URLS.some(url => 
        pathname.startsWith(url) || pathname.includes(url)
    );
}

/**
 * 推送通知事件
 */
self.addEventListener('push', event => {
    console.log('收到推送消息:', event);
    
    let notificationData = {
        title: '新消息',
        body: '您有新的通知',
        icon: '/assets/images/logo.svg',
        badge: '/assets/images/logo.svg',
        tag: 'default',
        requireInteraction: false,
        actions: [
            {
                action: 'view',
                title: '查看',
                icon: '/assets/images/view-icon.svg'
            },
            {
                action: 'dismiss',
                title: '忽略',
                icon: '/assets/images/dismiss-icon.svg'
            }
        ]
    };
    
    if (event.data) {
        try {
            const data = event.data.json();
            notificationData = { ...notificationData, ...data };
        } catch (error) {
            console.error('解析推送数据失败:', error);
        }
    }
    
    event.waitUntil(
        self.registration.showNotification(notificationData.title, notificationData)
    );
});

/**
 * 通知点击事件
 */
self.addEventListener('notificationclick', event => {
    console.log('通知被点击:', event);
    
    event.notification.close();
    
    if (event.action === 'view') {
        // 打开应用
        event.waitUntil(
            clients.openWindow('/')
        );
    } else if (event.action === 'dismiss') {
        // 忽略通知
        console.log('通知被忽略');
    } else {
        // 默认行为：打开应用
        event.waitUntil(
            clients.openWindow('/')
        );
    }
});

/**
 * 后台同步事件
 */
self.addEventListener('sync', event => {
    console.log('后台同步事件:', event.tag);
    
    if (event.tag === 'background-sync') {
        event.waitUntil(doBackgroundSync());
    }
});

/**
 * 执行后台同步
 */
async function doBackgroundSync() {
    try {
        console.log('执行后台同步...');
        
        // 这里可以添加需要同步的数据逻辑
        // 例如：同步离线时创建的数据到服务器
        
        // 获取离线数据
        const offlineData = await getOfflineData();
        
        if (offlineData.length > 0) {
            // 同步到服务器
            await syncDataToServer(offlineData);
            
            // 清除已同步的离线数据
            await clearSyncedOfflineData();
            
            console.log('后台同步完成');
        }
        
    } catch (error) {
        console.error('后台同步失败:', error);
    }
}

/**
 * 获取离线数据
 * @returns {Promise<Array>} 离线数据
 */
async function getOfflineData() {
    // 这里应该从 IndexedDB 或其他存储中获取离线数据
    // 暂时返回空数组
    return [];
}

/**
 * 同步数据到服务器
 * @param {Array} data - 要同步的数据
 */
async function syncDataToServer(data) {
    // 这里应该实现具体的同步逻辑
    console.log('同步数据到服务器:', data);
}

/**
 * 清除已同步的离线数据
 */
async function clearSyncedOfflineData() {
    // 这里应该清除已同步的数据
    console.log('清除已同步的离线数据');
}

/**
 * 消息事件（与主线程通信）
 */
self.addEventListener('message', event => {
    console.log('Service Worker 收到消息:', event.data);
    
    if (event.data && event.data.type) {
        switch (event.data.type) {
            case 'SKIP_WAITING':
                self.skipWaiting();
                break;
                
            case 'GET_VERSION':
                event.ports[0].postMessage({
                    version: CACHE_NAME
                });
                break;
                
            case 'CLEAR_CACHE':
                clearAllCaches().then(() => {
                    event.ports[0].postMessage({
                        success: true
                    });
                });
                break;
                
            default:
                console.log('未知消息类型:', event.data.type);
        }
    }
});

/**
 * 清除所有缓存
 */
async function clearAllCaches() {
    const cacheNames = await caches.keys();
    await Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
    );
    console.log('所有缓存已清除');
}

console.log('Service Worker 脚本加载完成');