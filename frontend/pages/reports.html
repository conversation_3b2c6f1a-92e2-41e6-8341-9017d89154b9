<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>报告管理 - 项目管理系统</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- 自定义样式 -->
    <link href="../assets/css/main.css" rel="stylesheet">
    <link href="../assets/css/reports.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="../index.html">
                <i class="fas fa-project-diagram me-2"></i>
                项目管理系统
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.html">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="projects.html">
                            <i class="fas fa-folder me-1"></i>项目
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="tasks.html">
                            <i class="fas fa-tasks me-1"></i>任务
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="reports.html">
                            <i class="fas fa-chart-bar me-1"></i>报告
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="approvals.html">
                            <i class="fas fa-check-circle me-1"></i>审核
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="users.html">
                            <i class="fas fa-users me-1"></i>用户
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>
                            <span id="currentUserName">用户</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>个人资料</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container-fluid py-4">
        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2 class="mb-1">
                    <i class="fas fa-chart-bar text-primary me-2"></i>
                    报告管理
                </h2>
                <p class="text-muted mb-0">查看项目进度、任务统计和团队绩效报告</p>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-primary" onclick="refreshReports()">
                    <i class="fas fa-sync-alt me-1"></i>刷新
                </button>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#exportReportModal">
                    <i class="fas fa-download me-1"></i>导出报告
                </button>
            </div>
        </div>

        <!-- 报告类型选择 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">报告类型</label>
                                <select class="form-select" id="reportType">
                                    <option value="overview">概览报告</option>
                                    <option value="project">项目报告</option>
                                    <option value="task">任务报告</option>
                                    <option value="user">用户绩效</option>
                                    <option value="workload">工作量统计</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">时间范围</label>
                                <select class="form-select" id="timeRange">
                                    <option value="7">最近7天</option>
                                    <option value="30" selected>最近30天</option>
                                    <option value="90">最近90天</option>
                                    <option value="custom">自定义</option>
                                </select>
                            </div>
                            <div class="col-md-2" id="startDateContainer" style="display: none;">
                                <label class="form-label">开始日期</label>
                                <input type="date" class="form-control" id="startDate">
                            </div>
                            <div class="col-md-2" id="endDateContainer" style="display: none;">
                                <label class="form-label">结束日期</label>
                                <input type="date" class="form-control" id="endDate">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <button class="btn btn-primary d-block w-100" onclick="generateReport()">
                                    <i class="fas fa-chart-line me-1"></i>生成报告
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 报告内容区域 -->
        <div id="reportContent">
            <!-- 概览报告 -->
            <div id="overviewReport" class="report-section">
                <!-- 统计卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-primary">
                                <i class="fas fa-project-diagram"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="totalProjects">0</h3>
                                <p>总项目数</p>
                                <small class="text-success">
                                    <i class="fas fa-arrow-up"></i>
                                    <span id="projectsGrowth">0%</span> 较上期
                                </small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-success">
                                <i class="fas fa-tasks"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="totalTasks">0</h3>
                                <p>总任务数</p>
                                <small class="text-success">
                                    <i class="fas fa-arrow-up"></i>
                                    <span id="tasksGrowth">0%</span> 较上期
                                </small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-warning">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="totalHours">0</h3>
                                <p>总工时</p>
                                <small class="text-success">
                                    <i class="fas fa-arrow-up"></i>
                                    <span id="hoursGrowth">0%</span> 较上期
                                </small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-info">
                                <i class="fas fa-percentage"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="completionRate">0%</h3>
                                <p>完成率</p>
                                <small class="text-success">
                                    <i class="fas fa-arrow-up"></i>
                                    <span id="completionGrowth">0%</span> 较上期
                                </small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 图表区域 -->
                <div class="row mb-4">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-chart-line me-2"></i>项目进度趋势
                                </h5>
                            </div>
                            <div class="card-body">
                                <canvas id="progressChart" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-chart-pie me-2"></i>任务状态分布
                                </h5>
                            </div>
                            <div class="card-body">
                                <canvas id="taskStatusChart" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 项目列表 -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-list me-2"></i>项目概览
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>项目名称</th>
                                                <th>负责人</th>
                                                <th>进度</th>
                                                <th>任务数</th>
                                                <th>完成率</th>
                                                <th>状态</th>
                                                <th>截止时间</th>
                                            </tr>
                                        </thead>
                                        <tbody id="projectOverviewTable">
                                            <!-- 项目数据将在这里动态生成 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 其他报告类型的内容区域 -->
            <div id="projectReport" class="report-section" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-project-diagram me-2"></i>项目详细报告
                        </h5>
                    </div>
                    <div class="card-body" id="projectReportContent">
                        <!-- 项目报告内容 -->
                    </div>
                </div>
            </div>

            <div id="taskReport" class="report-section" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-tasks me-2"></i>任务统计报告
                        </h5>
                    </div>
                    <div class="card-body" id="taskReportContent">
                        <!-- 任务报告内容 -->
                    </div>
                </div>
            </div>

            <div id="userReport" class="report-section" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-users me-2"></i>用户绩效报告
                        </h5>
                    </div>
                    <div class="card-body" id="userReportContent">
                        <!-- 用户报告内容 -->
                    </div>
                </div>
            </div>

            <div id="workloadReport" class="report-section" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-bar me-2"></i>工作量统计报告
                        </h5>
                    </div>
                    <div class="card-body" id="workloadReportContent">
                        <!-- 工作量报告内容 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 导出报告模态框 -->
    <div class="modal fade" id="exportReportModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">导出报告</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="exportReportForm">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">导出格式</label>
                            <select class="form-select" name="format">
                                <option value="pdf">PDF</option>
                                <option value="excel">Excel</option>
                                <option value="csv">CSV</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">报告类型</label>
                            <select class="form-select" name="reportType">
                                <option value="overview">概览报告</option>
                                <option value="project">项目报告</option>
                                <option value="task">任务报告</option>
                                <option value="user">用户绩效</option>
                                <option value="workload">工作量统计</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">时间范围</label>
                            <div class="row g-2">
                                <div class="col-6">
                                    <input type="date" class="form-control" name="startDate" required>
                                </div>
                                <div class="col-6">
                                    <input type="date" class="form-control" name="endDate" required>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-download me-1"></i>导出
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 应用脚本 -->
    <script src="../assets/js/config.js"></script>
    <script src="../assets/js/api/auth.js"></script>
    <script src="../assets/js/api/projects.js"></script>
    <script src="../assets/js/api/tasks.js"></script>
    <script src="../assets/js/api/users.js"></script>
    <script src="../assets/js/reports.js"></script>
    <script src="../assets/js/main.js"></script>
</body>
</html>