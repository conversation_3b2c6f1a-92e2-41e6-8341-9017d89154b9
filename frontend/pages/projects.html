<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目管理 - 项目管理系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- 自定义样式 -->
    <link href="../assets/css/main.css" rel="stylesheet">
    <link href="../assets/css/projects.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="../index.html">
                <i class="fas fa-project-diagram me-2"></i>
                项目管理系统
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.html">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="projects.html">
                            <i class="fas fa-folder me-1"></i>项目
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="tasks.html">
                            <i class="fas fa-tasks me-1"></i>任务
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.html">
                            <i class="fas fa-chart-bar me-1"></i>报告
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="approvals.html">
                            <i class="fas fa-check-circle me-1"></i>审核
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="users.html">
                            <i class="fas fa-users me-1"></i>用户
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>
                            <span id="currentUserName">用户</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.html"><i class="fas fa-user me-2"></i>个人资料</a></li>
                            <li><a class="dropdown-item" href="settings.html"><i class="fas fa-cog me-2"></i>设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <div class="main-content">
        <div class="container-fluid">
            <!-- 页面标题和操作栏 -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">项目管理</h1>
                    <p class="text-muted mb-0">管理您的项目和团队协作</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-secondary" onclick="refreshProjects()">
                        <i class="fas fa-sync-alt me-1"></i>刷新
                    </button>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createProjectModal">
                        <i class="fas fa-plus me-1"></i>创建项目
                    </button>
                </div>
            </div>

            <!-- 筛选和搜索栏 -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" class="form-control" id="searchInput" placeholder="搜索项目名称或描述...">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <select class="form-select" id="statusFilter">
                                <option value="">所有状态</option>
                                <option value="PLANNING">规划中</option>
                                <option value="IN_PROGRESS">进行中</option>
                                <option value="COMPLETED">已完成</option>
                                <option value="ON_HOLD">暂停</option>
                                <option value="CANCELLED">已取消</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select class="form-select" id="priorityFilter">
                                <option value="">所有优先级</option>
                                <option value="HIGH">高</option>
                                <option value="MEDIUM">中</option>
                                <option value="LOW">低</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select class="form-select" id="sortBy">
                                <option value="createTime">创建时间</option>
                                <option value="updateTime">更新时间</option>
                                <option value="name">项目名称</option>
                                <option value="priority">优先级</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <div class="btn-group w-100" role="group">
                                <input type="radio" class="btn-check" name="viewMode" id="gridView" value="grid" checked>
                                <label class="btn btn-outline-secondary" for="gridView">
                                    <i class="fas fa-th"></i>
                                </label>
                                <input type="radio" class="btn-check" name="viewMode" id="listView" value="list">
                                <label class="btn btn-outline-secondary" for="listView">
                                    <i class="fas fa-list"></i>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 项目统计卡片 -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title mb-0">总项目数</h6>
                                    <h3 class="mb-0" id="totalProjects">0</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-folder fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title mb-0">进行中</h6>
                                    <h3 class="mb-0" id="activeProjects">0</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-play-circle fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title mb-0">已完成</h6>
                                    <h3 class="mb-0" id="completedProjects">0</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-circle fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title mb-0">我参与的</h6>
                                    <h3 class="mb-0" id="myProjects">0</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-user-friends fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 项目列表 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">项目列表</h5>
                </div>
                <div class="card-body">
                    <!-- 加载状态 -->
                    <div id="projectsLoading" class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2 text-muted">正在加载项目数据...</p>
                    </div>

                    <!-- 网格视图 -->
                    <div id="projectsGrid" class="row g-3" style="display: none;"></div>

                    <!-- 列表视图 -->
                    <div id="projectsList" class="table-responsive" style="display: none;">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>项目名称</th>
                                    <th>状态</th>
                                    <th>优先级</th>
                                    <th>负责人</th>
                                    <th>进度</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="projectsTableBody"></tbody>
                        </table>
                    </div>

                    <!-- 空状态 -->
                    <div id="emptyState" class="text-center py-5" style="display: none;">
                        <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">暂无项目</h5>
                        <p class="text-muted">点击上方"创建项目"按钮开始您的第一个项目</p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createProjectModal">
                            <i class="fas fa-plus me-1"></i>创建项目
                        </button>
                    </div>
                </div>
            </div>

            <!-- 分页 -->
            <nav aria-label="项目分页" class="mt-4">
                <ul class="pagination justify-content-center" id="pagination"></ul>
            </nav>
        </div>
    </div>

    <!-- 创建项目模态框 -->
    <div class="modal fade" id="createProjectModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">创建新项目</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="createProjectForm" class="needs-validation" novalidate>
                    <div class="modal-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="projectName" class="form-label">项目名称 *</label>
                                <input type="text" class="form-control" id="projectName" required>
                                <div class="invalid-feedback">请输入项目名称</div>
                            </div>
                            <div class="col-md-6">
                                <label for="projectCode" class="form-label">项目编码</label>
                                <input type="text" class="form-control" id="projectCode" placeholder="自动生成">
                            </div>
                            <div class="col-12">
                                <label for="projectDescription" class="form-label">项目描述</label>
                                <textarea class="form-control" id="projectDescription" rows="3" placeholder="请描述项目的目标和范围..."></textarea>
                            </div>
                            <div class="col-md-4">
                                <label for="projectPriority" class="form-label">优先级</label>
                                <select class="form-select" id="projectPriority">
                                    <option value="MEDIUM" selected>中</option>
                                    <option value="HIGH">高</option>
                                    <option value="LOW">低</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="projectStartDate" class="form-label">开始日期</label>
                                <input type="date" class="form-control" id="projectStartDate">
                            </div>
                            <div class="col-md-4">
                                <label for="projectEndDate" class="form-label">结束日期</label>
                                <input type="date" class="form-control" id="projectEndDate">
                            </div>
                            <div class="col-12">
                                <label for="projectMembers" class="form-label">项目成员</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="memberSearch" placeholder="搜索用户...">
                                    <button class="btn btn-outline-secondary" type="button" onclick="searchUsers()">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                                <div id="selectedMembers" class="mt-2"></div>
                                <div id="userSearchResults" class="mt-2"></div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">
                            <span class="btn-text">创建项目</span>
                            <span class="btn-loading d-none">
                                <span class="spinner-border spinner-border-sm me-1"></span>
                                创建中...
                            </span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 项目详情模态框 -->
    <div class="modal fade" id="projectDetailModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="projectDetailTitle">项目详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="projectDetailContent">
                    <!-- 项目详情内容将在这里动态加载 -->
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 应用配置 -->
    <script src="../assets/js/config.js"></script>
    <!-- API接口 -->
    <script src="../assets/js/api/auth.js"></script>
    <script src="../assets/js/api/projects.js"></script>
    <!-- 主应用 -->
    <script src="../assets/js/main.js"></script>
    <!-- 项目管理 -->
    <script src="../assets/js/projects.js"></script>
</body>
</html>