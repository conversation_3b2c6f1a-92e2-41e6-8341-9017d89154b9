<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>登录 - 项目管理系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- 自定义样式 -->
    <link href="../assets/css/login.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container-fluid vh-100">
        <div class="row h-100">
            <!-- 左侧背景区域 -->
            <div class="col-lg-6 d-none d-lg-flex align-items-center justify-content-center bg-primary">
                <div class="text-center text-white">
                    <div class="mb-4">
                        <i class="fas fa-project-diagram fa-5x mb-3"></i>
                        <h1 class="display-4 fw-bold">项目管理系统</h1>
                        <p class="lead">高效协作，精准管理，成就卓越项目</p>
                    </div>
                    <div class="row text-center mt-5">
                        <div class="col-4">
                            <i class="fas fa-users fa-2x mb-2"></i>
                            <h5>团队协作</h5>
                            <p class="small">多人协作，实时同步</p>
                        </div>
                        <div class="col-4">
                            <i class="fas fa-tasks fa-2x mb-2"></i>
                            <h5>任务管理</h5>
                            <p class="small">任务分配，进度跟踪</p>
                        </div>
                        <div class="col-4">
                            <i class="fas fa-chart-line fa-2x mb-2"></i>
                            <h5>数据分析</h5>
                            <p class="small">数据可视，决策支持</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 右侧登录表单区域 -->
            <div class="col-lg-6 d-flex align-items-center justify-content-center">
                <div class="login-form-container">
                    <div class="text-center mb-4">
                        <div class="d-lg-none mb-3">
                            <i class="fas fa-project-diagram fa-3x text-primary"></i>
                        </div>
                        <h2 class="fw-bold text-dark">欢迎回来</h2>
                        <p class="text-muted">请登录您的账户</p>
                    </div>
                    
                    <!-- 登录表单 -->
                    <form id="loginForm" class="needs-validation" novalidate>
                        <div class="mb-3">
                            <label for="username" class="form-label">
                                <i class="fas fa-user me-2"></i>用户名
                            </label>
                            <input type="text" 
                                   class="form-control form-control-lg" 
                                   id="username" 
                                   name="username"
                                   placeholder="请输入用户名"
                                   required>
                            <div class="invalid-feedback">
                                请输入用户名
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock me-2"></i>密码
                            </label>
                            <div class="input-group">
                                <input type="password" 
                                       class="form-control form-control-lg" 
                                       id="password" 
                                       name="password"
                                       placeholder="请输入密码"
                                       required>
                                <button class="btn btn-outline-secondary" 
                                        type="button" 
                                        id="togglePassword"
                                        title="显示/隐藏密码">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="invalid-feedback">
                                请输入密码
                            </div>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="rememberMe">
                            <label class="form-check-label" for="rememberMe">
                                记住我
                            </label>
                        </div>
                        
                        <div class="d-grid mb-3">
                            <button type="submit" class="btn btn-primary btn-lg" id="loginBtn">
                                <span class="btn-text">登录</span>
                                <span class="btn-loading d-none">
                                    <span class="spinner-border spinner-border-sm me-2"></span>
                                    登录中...
                                </span>
                            </button>
                        </div>
                        
                        <div class="text-center">
                            <a href="#" class="text-decoration-none" onclick="showForgotPassword()">
                                忘记密码？
                            </a>
                            <span class="text-muted mx-2">|</span>
                            <a href="#" class="text-decoration-none" onclick="showRegister()">
                                注册账户
                            </a>
                        </div>
                    </form>
                    
                    <!-- 错误提示 -->
                    <div id="errorAlert" class="alert alert-danger mt-3 d-none" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <span id="errorMessage"></span>
                    </div>
                    
                    <!-- 成功提示 -->
                    <div id="successAlert" class="alert alert-success mt-3 d-none" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <span id="successMessage"></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 注册模态框 -->
    <div class="modal fade" id="registerModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-user-plus me-2"></i>
                        注册新账户
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="registerForm" class="needs-validation" novalidate>
                        <div class="mb-3">
                            <label for="regUsername" class="form-label">用户名</label>
                            <input type="text" class="form-control" id="regUsername" required>
                            <div class="invalid-feedback">请输入用户名</div>
                        </div>
                        <div class="mb-3">
                            <label for="regEmail" class="form-label">邮箱</label>
                            <input type="email" class="form-control" id="regEmail" required>
                            <div class="invalid-feedback">请输入有效的邮箱地址</div>
                        </div>
                        <div class="mb-3">
                            <label for="regPassword" class="form-label">密码</label>
                            <input type="password" class="form-control" id="regPassword" required minlength="6">
                            <div class="invalid-feedback">密码至少6位字符</div>
                        </div>
                        <div class="mb-3">
                            <label for="regConfirmPassword" class="form-label">确认密码</label>
                            <input type="password" class="form-control" id="regConfirmPassword" required>
                            <div class="invalid-feedback">两次密码输入不一致</div>
                        </div>
                        <div class="mb-3">
                            <label for="regRealName" class="form-label">真实姓名</label>
                            <input type="text" class="form-control" id="regRealName" required>
                            <div class="invalid-feedback">请输入真实姓名</div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="handleRegister()">
                        注册
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 忘记密码模态框 -->
    <div class="modal fade" id="forgotPasswordModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-key me-2"></i>
                        重置密码
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p class="text-muted mb-3">请输入您的邮箱地址，我们将发送重置密码的链接到您的邮箱。</p>
                    <form id="forgotPasswordForm">
                        <div class="mb-3">
                            <label for="resetEmail" class="form-label">邮箱地址</label>
                            <input type="email" class="form-control" id="resetEmail" required>
                            <div class="invalid-feedback">请输入有效的邮箱地址</div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="handleForgotPassword()">
                        发送重置链接
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 应用配置 -->
    <script>
        // 应用配置
        const APP_CONFIG = {
            API_BASE_URL: 'http://localhost:8080/api',
            TOKEN_KEY: 'pms_token',
            USER_KEY: 'pms_user',
            REFRESH_TOKEN_KEY: 'pms_refresh_token'
        };
    </script>
    <!-- 登录页面脚本 -->
    <script src="../assets/js/login.js"></script>
</body>
</html>