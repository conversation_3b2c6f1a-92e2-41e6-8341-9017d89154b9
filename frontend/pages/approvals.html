<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>审核管理 - 项目管理系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- 自定义样式 -->
    <link href="../assets/css/main.css" rel="stylesheet">
    <link href="../assets/css/approvals.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="../index.html">
                <i class="fas fa-project-diagram me-2"></i>
                项目管理系统
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.html">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="projects.html">
                            <i class="fas fa-folder me-1"></i>项目
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="tasks.html">
                            <i class="fas fa-tasks me-1"></i>任务
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.html">
                            <i class="fas fa-chart-bar me-1"></i>报告
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="approvals.html">
                            <i class="fas fa-check-circle me-1"></i>审核
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="users.html">
                            <i class="fas fa-users me-1"></i>用户
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>
                            <span id="currentUserName">用户</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>个人资料</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="main-content">
        <div class="container-fluid">
            <!-- 页面标题 -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">审核管理</h1>
                    <p class="text-muted mb-0">管理系统审核流程和审批事项</p>
                </div>
                <div>
                    <button type="button" class="btn btn-outline-secondary" id="refreshApprovals">
                        <i class="fas fa-sync-alt me-2"></i>刷新
                    </button>
                    <button type="button" class="btn btn-primary" id="batchApproveBtn" disabled>
                        <i class="fas fa-check me-2"></i>批量审批
                    </button>
                </div>
            </div>

            <!-- 统计卡片 -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card stat-card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-warning">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="ms-3">
                                    <h5 class="card-title mb-0" id="pendingApprovals">0</h5>
                                    <p class="card-text text-muted">待审核</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card stat-card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-success">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="ms-3">
                                    <h5 class="card-title mb-0" id="approvedCount">0</h5>
                                    <p class="card-text text-muted">已批准</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card stat-card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-danger">
                                    <i class="fas fa-times-circle"></i>
                                </div>
                                <div class="ms-3">
                                    <h5 class="card-title mb-0" id="rejectedCount">0</h5>
                                    <p class="card-text text-muted">已拒绝</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card stat-card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-info">
                                    <i class="fas fa-calendar-check"></i>
                                </div>
                                <div class="ms-3">
                                    <h5 class="card-title mb-0" id="todayApprovals">0</h5>
                                    <p class="card-text text-muted">今日处理</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 筛选和搜索 -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">搜索</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="searchInput" placeholder="搜索审核内容">
                                <button class="btn btn-outline-secondary" type="button" id="searchBtn">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">审核类型</label>
                            <select class="form-select" id="typeFilter">
                                <option value="">全部类型</option>
                                <option value="PROJECT_CREATE">项目创建</option>
                                <option value="PROJECT_UPDATE">项目更新</option>
                                <option value="USER_ROLE">用户角色</option>
                                <option value="PROJECT_JOIN">项目加入</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">状态</label>
                            <select class="form-select" id="statusFilter">
                                <option value="">全部状态</option>
                                <option value="PENDING">待审核</option>
                                <option value="APPROVED">已批准</option>
                                <option value="REJECTED">已拒绝</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">申请人</label>
                            <select class="form-select" id="requesterFilter">
                                <option value="">全部申请人</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">日期范围</label>
                            <div class="d-flex gap-2">
                                <input type="date" class="form-control" id="startDate">
                                <input type="date" class="form-control" id="endDate">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 审核列表 -->
            <div class="card mb-4">
                <div class="card-header bg-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">审核列表</h5>
                        <div class="d-flex align-items-center gap-3">
                            <div class="btn-group">
                                <button type="button" class="btn btn-success btn-sm" id="bulk-approve" disabled>
                                    <i class="fas fa-check"></i> 批准
                                </button>
                                <button type="button" class="btn btn-danger btn-sm" id="bulk-reject" disabled>
                                    <i class="fas fa-times"></i> 拒绝
                                </button>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="selectAllApprovals">
                                <label class="form-check-label" for="selectAllApprovals">全选</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover align-middle mb-0">
                        <thead class="table-light">
                            <tr>
                                <th width="40">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="headerCheckbox">
                                    </div>
                                </th>
                                <th>ID</th>
                                <th>类型</th>
                                <th>申请人</th>
                                <th>目标</th>
                                <th>申请时间</th>
                                <th>状态</th>
                                <th width="150">操作</th>
                            </tr>
                        </thead>
                        <tbody id="approvals-table-body">
                            <!-- 审核列表将在这里动态加载 -->
                        </tbody>
                    </table>
                </div>
                <div id="approvals-table" style="display: none;"></div>
                <div class="card-footer bg-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <div id="page-info" class="text-muted small">显示 0 到 0，共 0 条</div>
                        <nav aria-label="审核分页" id="pagination">
                            <!-- 分页将在这里动态加载 -->
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载动画 -->
    <div id="loading-overlay" class="loading-overlay" style="display: none;">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
        </div>
    </div>

    <!-- 消息提示容器 -->
    <div id="alerts-container" class="position-fixed top-0 end-0 p-3" style="z-index: 1055;"></div>

    <!-- 审核详情模态框 -->
    <div class="modal fade" id="approval-detail-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">审核详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body" id="approvalDetailContent">
                    <!-- 审核详情将在这里动态加载 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批准确认模态框 -->
    <div class="modal fade" id="approve-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">确认批准</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <p>您确定要批准这个审核申请吗？</p>
                    <input type="hidden" id="approve-approval-id">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-success" id="confirm-approve">确认批准</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 拒绝确认模态框 -->
    <div class="modal fade" id="reject-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">确认拒绝</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="reject-approval-id">
                    <div class="form-group">
                        <label for="reject-reason" class="form-label">拒绝理由</label>
                        <textarea class="form-control" id="reject-reason" rows="4" placeholder="请输入拒绝理由..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirm-reject">确认拒绝</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量批准模态框 -->
    <div class="modal fade" id="bulk-approve-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">批量批准</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <p>您确定要批准选中的 <span id="bulk-approve-count">0</span> 项审核申请吗？</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-success" id="confirm-bulk-approve">确认批准</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量拒绝模态框 -->
    <div class="modal fade" id="bulk-reject-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">批量拒绝</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <p>您确定要拒绝选中的 <span id="bulk-reject-count">0</span> 项审核申请吗？</p>
                    <div class="form-group">
                        <label for="bulk-reject-reason" class="form-label">拒绝理由</label>
                        <textarea class="form-control" id="bulk-reject-reason" rows="4" placeholder="请输入拒绝理由..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirm-bulk-reject">确认拒绝</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 自定义脚本 -->
    <script src="../assets/js/config.js"></script>
    <script src="../assets/js/api/auth.js"></script>
    <script src="../assets/js/api/approvals.js"></script>
    <script src="../assets/js/approvals.js"></script>
    <script src="../assets/js/main.js"></script>
</body>
</html>