<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务管理 - 项目管理系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- 自定义样式 -->
    <link href="../assets/css/main.css" rel="stylesheet">
    <link href="../assets/css/tasks.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="../index.html">
                <i class="fas fa-project-diagram me-2"></i>
                项目管理系统
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.html">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="projects.html">
                            <i class="fas fa-folder me-1"></i>项目
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="tasks.html">
                            <i class="fas fa-tasks me-1"></i>任务
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.html">
                            <i class="fas fa-chart-bar me-1"></i>报告
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="approvals.html">
                            <i class="fas fa-check-circle me-1"></i>审核
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="users.html">
                            <i class="fas fa-users me-1"></i>用户
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>
                            <span id="currentUserName">用户</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>个人资料</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <div class="main-content">
        <div class="container-fluid">
            <!-- 页面标题和操作栏 -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">任务管理</h1>
                    <p class="text-muted mb-0">管理和跟踪项目任务</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary" onclick="refreshTasks()">
                        <i class="fas fa-sync-alt me-1"></i>刷新
                    </button>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createTaskModal">
                        <i class="fas fa-plus me-1"></i>创建任务
                    </button>
                </div>
            </div>

            <!-- 统计卡片 -->
            <div class="row mb-4" id="taskStats">
                <div class="col-md-3">
                    <div class="card stat-card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-primary">
                                    <i class="fas fa-tasks"></i>
                                </div>
                                <div class="ms-3">
                                    <h5 class="card-title mb-0" id="totalTasks">0</h5>
                                    <p class="card-text text-muted">总任务数</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card stat-card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-warning">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="ms-3">
                                    <h5 class="card-title mb-0" id="pendingTasks">0</h5>
                                    <p class="card-text text-muted">待办任务</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card stat-card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-info">
                                    <i class="fas fa-play-circle"></i>
                                </div>
                                <div class="ms-3">
                                    <h5 class="card-title mb-0" id="inProgressTasks">0</h5>
                                    <p class="card-text text-muted">进行中</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card stat-card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-success">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="ms-3">
                                    <h5 class="card-title mb-0" id="completedTasks">0</h5>
                                    <p class="card-text text-muted">已完成</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 筛选和搜索 -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">项目筛选</label>
                            <select class="form-select" id="projectFilter">
                                <option value="">所有项目</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">状态筛选</label>
                            <select class="form-select" id="statusFilter">
                                <option value="">所有状态</option>
                                <option value="TODO">待办</option>
                                <option value="IN_PROGRESS">进行中</option>
                                <option value="REVIEW">待审核</option>
                                <option value="COMPLETED">已完成</option>
                                <option value="CANCELLED">已取消</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">优先级筛选</label>
                            <select class="form-select" id="priorityFilter">
                                <option value="">所有优先级</option>
                                <option value="HIGH">高</option>
                                <option value="MEDIUM">中</option>
                                <option value="LOW">低</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">负责人筛选</label>
                            <select class="form-select" id="assigneeFilter">
                                <option value="">所有负责人</option>
                                <option value="me">我的任务</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">搜索任务</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="searchInput" placeholder="搜索任务标题或描述...">
                                <button class="btn btn-outline-secondary" type="button" onclick="searchTasks()">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 视图切换和排序 -->
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div class="btn-group" role="group">
                    <input type="radio" class="btn-check" name="viewMode" id="listView" value="list" checked>
                    <label class="btn btn-outline-primary" for="listView">
                        <i class="fas fa-list me-1"></i>列表视图
                    </label>
                    <input type="radio" class="btn-check" name="viewMode" id="kanbanView" value="kanban">
                    <label class="btn btn-outline-primary" for="kanbanView">
                        <i class="fas fa-columns me-1"></i>看板视图
                    </label>
                </div>
                
                <div class="d-flex align-items-center gap-2">
                    <label class="form-label mb-0">排序:</label>
                    <select class="form-select form-select-sm" id="sortBy" style="width: auto;">
                        <option value="created_at">创建时间</option>
                        <option value="due_date">截止时间</option>
                        <option value="priority">优先级</option>
                        <option value="status">状态</option>
                        <option value="title">标题</option>
                    </select>
                    <select class="form-select form-select-sm" id="sortOrder" style="width: auto;">
                        <option value="desc">降序</option>
                        <option value="asc">升序</option>
                    </select>
                </div>
            </div>

            <!-- 任务列表视图 -->
            <div id="listViewContainer">
                <div class="card">
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th width="5%">
                                            <input type="checkbox" class="form-check-input" id="selectAll">
                                        </th>
                                        <th width="30%">任务标题</th>
                                        <th width="15%">项目</th>
                                        <th width="10%">负责人</th>
                                        <th width="10%">状态</th>
                                        <th width="8%">优先级</th>
                                        <th width="12%">截止时间</th>
                                        <th width="10%">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="taskTableBody">
                                    <!-- 任务列表将在这里动态生成 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 看板视图 -->
            <div id="kanbanViewContainer" style="display: none;">
                <div class="kanban-board">
                    <div class="kanban-column" data-status="TODO">
                        <div class="kanban-header">
                            <h6 class="mb-0">待办 <span class="badge bg-secondary" id="todoCount">0</span></h6>
                        </div>
                        <div class="kanban-body" id="todoTasks">
                            <!-- 待办任务卡片 -->
                        </div>
                    </div>
                    
                    <div class="kanban-column" data-status="IN_PROGRESS">
                        <div class="kanban-header">
                            <h6 class="mb-0">进行中 <span class="badge bg-primary" id="inProgressCount">0</span></h6>
                        </div>
                        <div class="kanban-body" id="inProgressTasks">
                            <!-- 进行中任务卡片 -->
                        </div>
                    </div>
                    
                    <div class="kanban-column" data-status="REVIEW">
                        <div class="kanban-header">
                            <h6 class="mb-0">待审核 <span class="badge bg-warning" id="reviewCount">0</span></h6>
                        </div>
                        <div class="kanban-body" id="reviewTasks">
                            <!-- 待审核任务卡片 -->
                        </div>
                    </div>
                    
                    <div class="kanban-column" data-status="COMPLETED">
                        <div class="kanban-header">
                            <h6 class="mb-0">已完成 <span class="badge bg-success" id="completedCount">0</span></h6>
                        </div>
                        <div class="kanban-body" id="completedTasks">
                            <!-- 已完成任务卡片 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分页 -->
            <div class="d-flex justify-content-between align-items-center mt-4">
                <div class="text-muted">
                    显示第 <span id="pageStart">0</span> - <span id="pageEnd">0</span> 条，共 <span id="totalCount">0</span> 条
                </div>
                <nav>
                    <ul class="pagination mb-0" id="pagination">
                        <!-- 分页按钮将在这里动态生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- 创建任务模态框 -->
    <div class="modal fade" id="createTaskModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">创建任务</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="createTaskForm">
                    <div class="modal-body">
                        <div class="row g-3">
                            <div class="col-md-8">
                                <label class="form-label">任务标题 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="title" required>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">所属项目 <span class="text-danger">*</span></label>
                                <select class="form-select" name="project_id" required>
                                    <option value="">选择项目</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <label class="form-label">任务描述</label>
                                <textarea class="form-control" name="description" rows="3" placeholder="详细描述任务内容和要求..."></textarea>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">负责人</label>
                                <select class="form-select" name="assignee_id">
                                    <option value="">选择负责人</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">优先级</label>
                                <select class="form-select" name="priority">
                                    <option value="MEDIUM">中</option>
                                    <option value="HIGH">高</option>
                                    <option value="LOW">低</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">截止时间</label>
                                <input type="datetime-local" class="form-control" name="due_date">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">预计工时（小时）</label>
                                <input type="number" class="form-control" name="estimated_hours" min="0" step="0.5">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">标签</label>
                                <input type="text" class="form-control" name="tags" placeholder="用逗号分隔多个标签">
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>创建任务
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 任务详情模态框 -->
    <div class="modal fade" id="taskDetailModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">任务详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="taskDetailContent">
                    <!-- 任务详情内容将在这里动态生成 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" id="editTaskBtn">
                        <i class="fas fa-edit me-1"></i>编辑任务
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量操作模态框 -->
    <div class="modal fade" id="batchActionModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">批量操作</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>已选择 <span id="selectedCount">0</span> 个任务</p>
                    <div class="mb-3">
                        <label class="form-label">操作类型</label>
                        <select class="form-select" id="batchAction">
                            <option value="">选择操作</option>
                            <option value="status">更新状态</option>
                            <option value="priority">更新优先级</option>
                            <option value="assignee">分配负责人</option>
                            <option value="delete">删除任务</option>
                        </select>
                    </div>
                    <div id="batchActionOptions" style="display: none;">
                        <!-- 批量操作选项将在这里动态生成 -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="executeBatchAction">
                        <i class="fas fa-check me-1"></i>执行操作
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 应用脚本 -->
    <script src="../assets/js/config.js"></script>
    <script src="../assets/js/api/auth.js"></script>
    <script src="../assets/js/api/tasks.js"></script>
    <script src="../assets/js/tasks.js"></script>
    <script src="../assets/js/main.js"></script>
</body>
</html>