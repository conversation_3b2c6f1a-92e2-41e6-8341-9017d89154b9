/**
 * 登录页面样式
 * 专门为登录页面设计的样式文件
 */

:root {
    --primary-color: #0d6efd;
    --primary-dark: #0b5ed7;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --border-radius: 0.375rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --box-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

/* 全局样式 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

/* 左侧背景区域 */
.bg-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%) !important;
    position: relative;
    overflow: hidden;
}

.bg-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>') repeat;
    opacity: 0.1;
}

.bg-primary > * {
    position: relative;
    z-index: 1;
}

/* 登录表单容器 */
.login-form-container {
    width: 100%;
    max-width: 400px;
    padding: 2rem;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-lg);
    margin: 1rem;
}

@media (max-width: 991.98px) {
    .login-form-container {
        max-width: 500px;
        margin: 2rem 1rem;
    }
}

/* 表单样式增强 */
.form-control {
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    transform: translateY(-1px);
}

.form-control.is-invalid {
    border-color: var(--danger-color);
}

.form-control.is-valid {
    border-color: var(--success-color);
}

/* 标签样式 */
.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.form-label i {
    color: var(--primary-color);
}

/* 按钮样式增强 */
.btn {
    border-radius: var(--border-radius);
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border: none;
    box-shadow: 0 4px 15px rgba(13, 110, 253, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(13, 110, 253, 0.4);
}

.btn-primary:active {
    transform: translateY(0);
}

/* 按钮加载状态 */
.btn .btn-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
}

/* 输入组样式 */
.input-group .btn {
    border: 2px solid #e9ecef;
    border-left: none;
}

.input-group .form-control:focus + .btn {
    border-color: var(--primary-color);
}

/* 复选框样式 */
.form-check-input {
    width: 1.2em;
    height: 1.2em;
    border: 2px solid #dee2e6;
    border-radius: 0.25em;
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-check-label {
    font-weight: 500;
    color: var(--dark-color);
}

/* 链接样式 */
a {
    color: var(--primary-color);
    font-weight: 500;
    transition: color 0.3s ease;
}

a:hover {
    color: var(--primary-dark);
    text-decoration: underline !important;
}

/* 警告框样式增强 */
.alert {
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
    box-shadow: var(--box-shadow);
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c2c7 100%);
    color: #721c24;
}

.alert-success {
    background: linear-gradient(135deg, #d1e7dd 0%, #badbcc 100%);
    color: #0f5132;
}

/* 模态框样式增强 */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-lg);
}

.modal-header {
    background: linear-gradient(135deg, var(--light-color) 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.modal-title {
    font-weight: 600;
    color: var(--dark-color);
}

.modal-title i {
    color: var(--primary-color);
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* 应用动画 */
.login-form-container {
    animation: fadeInUp 0.6s ease-out;
}

.bg-primary .text-center > div {
    animation: slideInLeft 0.8s ease-out;
}

.bg-primary .row {
    animation: fadeInUp 1s ease-out 0.3s both;
}

/* 加载动画 */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* 响应式设计 */
@media (max-width: 575.98px) {
    .login-form-container {
        padding: 1.5rem;
        margin: 1rem 0.5rem;
    }
    
    .form-control {
        font-size: 16px; /* 防止iOS缩放 */
    }
    
    .btn-lg {
        padding: 0.75rem 1rem;
        font-size: 1rem;
    }
}

@media (max-width: 991.98px) {
    .bg-primary {
        padding: 2rem 1rem;
    }
    
    .bg-primary .display-4 {
        font-size: 2.5rem;
    }
    
    .bg-primary .fa-5x {
        font-size: 3rem !important;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .login-form-container {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .form-control {
        background-color: #4a5568;
        border-color: #718096;
        color: #e2e8f0;
    }
    
    .form-control:focus {
        background-color: #4a5568;
        color: #e2e8f0;
    }
    
    .form-label {
        color: #e2e8f0;
    }
    
    .text-muted {
        color: #a0aec0 !important;
    }
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 高对比度支持 */
@media (prefers-contrast: high) {
    .form-control {
        border-width: 3px;
    }
    
    .btn {
        border-width: 3px;
    }
    
    .alert {
        border-width: 2px;
        border-style: solid;
    }
    
    .alert-danger {
        border-color: var(--danger-color);
    }
    
    .alert-success {
        border-color: var(--success-color);
    }
}

/* 打印样式 */
@media print {
    .bg-primary,
    .modal,
    .btn {
        display: none !important;
    }
    
    .login-form-container {
        box-shadow: none;
        border: 1px solid #000;
    }
}