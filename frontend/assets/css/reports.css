/**
 * 报告管理页面样式
 */

/* 统计卡片 */
.stat-card {
    background: #fff;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    flex-shrink: 0;
}

.stat-content {
    flex: 1;
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    color: #2c3e50;
}

.stat-content p {
    margin: 0.25rem 0 0.5rem 0;
    color: #6c757d;
    font-weight: 500;
}

.stat-content small {
    font-size: 0.875rem;
    font-weight: 500;
}

/* 报告区域 */
.report-section {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 图表容器 */
.chart-container {
    position: relative;
    height: 300px;
    margin: 1rem 0;
}

/* 进度条样式 */
.progress-bar-custom {
    height: 8px;
    border-radius: 4px;
    background: #e9ecef;
    overflow: hidden;
    position: relative;
}

.progress-bar-custom .progress {
    height: 100%;
    border-radius: 4px;
    transition: width 0.6s ease;
}

.progress-bar-custom .progress.bg-success {
    background: linear-gradient(90deg, #28a745, #20c997);
}

.progress-bar-custom .progress.bg-warning {
    background: linear-gradient(90deg, #ffc107, #fd7e14);
}

.progress-bar-custom .progress.bg-danger {
    background: linear-gradient(90deg, #dc3545, #e83e8c);
}

/* 项目状态标签 */
.project-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.project-status.status-active {
    background: #d4edda;
    color: #155724;
}

.project-status.status-completed {
    background: #cce7ff;
    color: #004085;
}

.project-status.status-on-hold {
    background: #fff3cd;
    color: #856404;
}

.project-status.status-cancelled {
    background: #f8d7da;
    color: #721c24;
}

/* 优先级标签 */
.priority-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.priority-badge.priority-high {
    background: #fee;
    color: #dc3545;
}

.priority-badge.priority-medium {
    background: #fff8e1;
    color: #f57c00;
}

.priority-badge.priority-low {
    background: #e8f5e8;
    color: #2e7d32;
}

/* 表格样式增强 */
.table-hover tbody tr:hover {
    background-color: #f8f9fa;
    transform: scale(1.01);
    transition: all 0.2s ease;
}

.table th {
    background: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #495057;
    padding: 1rem 0.75rem;
}

.table td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
}

/* 用户头像 */
.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
}

/* 图表卡片 */
.chart-card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
    overflow: hidden;
}

.chart-card .card-header {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 1rem 1.5rem;
}

.chart-card .card-body {
    padding: 1.5rem;
}

/* 加载状态 */
.loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 3rem;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state-icon {
    font-size: 3rem;
    color: #dee2e6;
    margin-bottom: 1rem;
}

.empty-state-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #495057;
}

.empty-state-description {
    margin-bottom: 0;
}

/* 导出按钮样式 */
.export-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.export-btn {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    background: white;
    color: #495057;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.export-btn:hover {
    background: #f8f9fa;
    border-color: #adb5bd;
    color: #495057;
    text-decoration: none;
    transform: translateY(-1px);
}

.export-btn i {
    font-size: 0.875rem;
}

/* 时间范围选择器 */
.time-range-selector {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

/* 报告标题 */
.report-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e9ecef;
}

.report-title h3 {
    margin: 0;
    color: #2c3e50;
    font-weight: 700;
}

.report-title .badge {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
}

/* 数据表格增强 */
.data-table {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.data-table .table {
    margin: 0;
}

.data-table .table thead th {
    background: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .stat-card {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
    
    .stat-content h3 {
        font-size: 1.5rem;
    }
    
    .export-buttons {
        justify-content: center;
    }
    
    .chart-container {
        height: 250px;
    }
}

@media (max-width: 576px) {
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .stat-card {
        padding: 1rem;
    }
    
    .chart-card .card-body {
        padding: 1rem;
    }
}

/* 打印样式 */
@media print {
    .navbar,
    .btn,
    .export-buttons {
        display: none !important;
    }
    
    .container-fluid {
        padding: 0 !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #dee2e6 !important;
    }
    
    .stat-card {
        box-shadow: none !important;
        border: 1px solid #dee2e6 !important;
        break-inside: avoid;
    }
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 工具提示样式 */
.tooltip-inner {
    background: #2c3e50;
    color: white;
    border-radius: 6px;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
}

.tooltip.bs-tooltip-top .tooltip-arrow::before {
    border-top-color: #2c3e50;
}

.tooltip.bs-tooltip-bottom .tooltip-arrow::before {
    border-bottom-color: #2c3e50;
}

.tooltip.bs-tooltip-start .tooltip-arrow::before {
    border-left-color: #2c3e50;
}

.tooltip.bs-tooltip-end .tooltip-arrow::before {
    border-right-color: #2c3e50;
}