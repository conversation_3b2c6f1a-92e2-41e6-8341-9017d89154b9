/**
 * 审核管理页面样式
 */

/* 主内容区域 */
.main-content {
    padding-top: 70px;
    padding-bottom: 30px;
    min-height: 100vh;
}

/* 统计卡片 */
.stat-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

/* 审核类型标签 */
.approval-type {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
    text-align: center;
}

.approval-type.project-create {
    background-color: #e0f2fe;
    color: #0369a1;
}

.approval-type.project-update {
    background-color: #dbeafe;
    color: #1e40af;
}

.approval-type.user-role {
    background-color: #f3e8ff;
    color: #6b21a8;
}

.approval-type.project-join {
    background-color: #dcfce7;
    color: #166534;
}

/* 审核状态标签 */
.approval-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
    text-align: center;
    min-width: 70px;
}

.approval-status.pending {
    background-color: #fef3c7;
    color: #92400e;
}

.approval-status.approved {
    background-color: #d1fae5;
    color: #065f46;
}

.approval-status.rejected {
    background-color: #fee2e2;
    color: #991b1b;
}

/* 表格样式 */
.table th {
    font-weight: 600;
    color: #374151;
    white-space: nowrap;
}

.table td {
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: #f9fafb;
}

/* 用户头像 */
.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 8px;
}

.user-avatar-placeholder {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 14px;
    margin-right: 8px;
}

/* 审核详情 */
.approval-detail-header {
    background-color: #f9fafb;
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.approval-detail-section {
    margin-bottom: 24px;
}

.approval-detail-section h6 {
    font-weight: 600;
    color: #374151;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e5e7eb;
}

.approval-detail-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    padding: 8px 0;
}

.approval-detail-label {
    font-weight: 500;
    color: #6b7280;
}

.approval-detail-value {
    font-weight: 500;
    color: #111827;
}

.approval-timeline {
    position: relative;
    padding-left: 28px;
}

.approval-timeline-item {
    position: relative;
    padding-bottom: 20px;
}

.approval-timeline-item:before {
    content: '';
    position: absolute;
    left: -28px;
    top: 0;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: #e5e7eb;
    border: 3px solid #fff;
    z-index: 1;
}

.approval-timeline-item:after {
    content: '';
    position: absolute;
    left: -21px;
    top: 16px;
    width: 2px;
    height: calc(100% - 16px);
    background-color: #e5e7eb;
}

.approval-timeline-item:last-child:after {
    display: none;
}

.approval-timeline-item.pending:before {
    background-color: #fbbf24;
}

.approval-timeline-item.approved:before {
    background-color: #10b981;
}

.approval-timeline-item.rejected:before {
    background-color: #ef4444;
}

.approval-timeline-date {
    font-size: 0.75rem;
    color: #6b7280;
    margin-bottom: 4px;
}

.approval-timeline-content {
    background-color: #f9fafb;
    padding: 12px;
    border-radius: 8px;
}

/* 表单样式 */
.form-label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 6px;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* 分页样式 */
.pagination .page-link {
    color: var(--bs-primary);
    border-radius: 4px;
    margin: 0 2px;
}

.pagination .page-item.active .page-link {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 30px;
    height: 30px;
    border: 3px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: var(--bs-primary);
    animation: spin 1s ease-in-out infinite;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 40px 20px;
}

.empty-state-icon {
    font-size: 3rem;
    color: #d1d5db;
    margin-bottom: 16px;
}

.empty-state-text {
    color: #6b7280;
    font-size: 1rem;
    margin-bottom: 16px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .stat-card {
        margin-bottom: 16px;
    }
    
    .approval-detail-item {
        flex-direction: column;
    }
    
    .approval-detail-value {
        margin-top: 4px;
    }
}

/* 打印样式 */
@media print {
    .navbar,
    .btn,
    .form-check,
    .modal,
    .pagination {
        display: none !important;
    }
    
    .main-content {
        padding: 0;
    }
    
    .card {
        border: none;
        box-shadow: none;
    }
    
    .table th,
    .table td {
        padding: 8px;
    }
}