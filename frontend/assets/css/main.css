/**
 * 项目管理系统 - 主样式文件
 * 使用CSS自定义属性管理主题色彩
 */

:root {
    /* 主题色彩 */
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    
    /* 字体 */
    --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-base: 14px;
    --font-size-lg: 16px;
    --font-size-sm: 12px;
    
    /* 间距 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 3rem;
    
    /* 边框 */
    --border-radius: 0.375rem;
    --border-width: 1px;
    --border-color: #dee2e6;
    
    /* 阴影 */
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --box-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

/* 全局样式重置 */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.5;
    color: var(--dark-color);
    background-color: #f5f5f5;
}

/* 侧边栏样式 */
.sidebar {
    position: fixed;
    top: 56px;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: var(--spacing-md) 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.1);
    background-color: var(--light-color);
}

.sidebar .nav-link {
    color: var(--dark-color);
    padding: var(--spacing-sm) var(--spacing-md);
    margin: var(--spacing-xs) 0;
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
    background-color: rgba(0, 0, 0, 0.05);
    color: var(--primary-color);
}

.sidebar .nav-link.active {
    background-color: var(--primary-color);
    color: white;
}

.sidebar .nav-link i {
    width: 16px;
    text-align: center;
}

/* 主内容区域 */
main.col-md-9 {
    margin-left: 16.66667%;
}

@media (max-width: 767.98px) {
    .sidebar {
        position: static;
        height: auto;
    }
    
    main.col-md-9 {
        margin-left: 0;
    }
}

/* 卡片样式增强 */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-lg);
}

.card-header {
    background-color: white;
    border-bottom: var(--border-width) solid var(--border-color);
    font-weight: 600;
}

/* 统计卡片样式 */
.card.text-white .card-body {
    padding: var(--spacing-lg);
}

.card.text-white h4 {
    font-size: var(--font-size-base);
    font-weight: 500;
    margin-bottom: var(--spacing-xs);
}

.card.text-white h2 {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
}

/* 表格样式 */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: var(--dark-color);
    background-color: var(--light-color);
}

.table td {
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

/* 按钮样式增强 */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-md);
    transition: all 0.2s ease;
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.btn-outline-primary:hover {
    transform: translateY(-1px);
}

/* 徽章样式 */
.badge {
    font-weight: 500;
    padding: var(--spacing-xs) var(--spacing-sm);
}

/* 进度条样式 */
.progress {
    height: 8px;
    border-radius: 4px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 4px;
    transition: width 0.6s ease;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-md);
}

/* 表单样式 */
.form-control {
    border-radius: var(--border-radius);
    border: var(--border-width) solid var(--border-color);
    padding: var(--spacing-sm) var(--spacing-md);
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: var(--spacing-xs);
}

/* 模态框样式 */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-lg);
}

.modal-header {
    border-bottom: var(--border-width) solid var(--border-color);
    padding: var(--spacing-lg);
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-footer {
    border-top: var(--border-width) solid var(--border-color);
    padding: var(--spacing-lg);
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: var(--spacing-xs);
}

.status-indicator.active {
    background-color: var(--success-color);
}

.status-indicator.pending {
    background-color: var(--warning-color);
}

.status-indicator.completed {
    background-color: var(--info-color);
}

.status-indicator.cancelled {
    background-color: var(--danger-color);
}

/* 响应式设计 */
@media (max-width: 576px) {
    .container-fluid {
        padding-left: var(--spacing-sm);
        padding-right: var(--spacing-sm);
    }
    
    .card-body {
        padding: var(--spacing-md);
    }
    
    .btn {
        font-size: var(--font-size-sm);
    }
    
    .table-responsive {
        font-size: var(--font-size-sm);
    }
}

/* 工具类 */
.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.cursor-pointer {
    cursor: pointer;
}

.border-start-primary {
    border-left: 4px solid var(--primary-color) !important;
}

.border-start-success {
    border-left: 4px solid var(--success-color) !important;
}

.border-start-warning {
    border-left: 4px solid var(--warning-color) !important;
}

.border-start-danger {
    border-left: 4px solid var(--danger-color) !important;
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}