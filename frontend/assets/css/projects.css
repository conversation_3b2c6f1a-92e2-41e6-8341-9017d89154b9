/**
 * 项目管理页面样式
 */

/* 项目卡片样式 */
.project-card {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    overflow: hidden;
    height: 100%;
}

.project-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: var(--bs-primary);
}

.project-card .card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    padding: 1rem;
}

.project-card .card-body {
    padding: 1.25rem;
}

.project-card .card-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
    padding: 0.75rem 1.25rem;
}

/* 项目状态徽章 */
.status-badge {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.375rem 0.75rem;
    border-radius: 20px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-planning {
    background-color: #e3f2fd;
    color: #1976d2;
    border: 1px solid #bbdefb;
}

.status-in-progress {
    background-color: #e8f5e8;
    color: #2e7d32;
    border: 1px solid #c8e6c9;
}

.status-completed {
    background-color: #fff3e0;
    color: #f57c00;
    border: 1px solid #ffcc02;
}

.status-on-hold {
    background-color: #fce4ec;
    color: #c2185b;
    border: 1px solid #f8bbd9;
}

.status-cancelled {
    background-color: #ffebee;
    color: #d32f2f;
    border: 1px solid #ffcdd2;
}

/* 优先级徽章 */
.priority-badge {
    font-size: 0.7rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    text-transform: uppercase;
}

.priority-high {
    background-color: #ffebee;
    color: #d32f2f;
    border: 1px solid #ffcdd2;
}

.priority-medium {
    background-color: #fff3e0;
    color: #f57c00;
    border: 1px solid #ffcc02;
}

.priority-low {
    background-color: #e8f5e8;
    color: #2e7d32;
    border: 1px solid #c8e6c9;
}

/* 进度条样式 */
.progress-container {
    position: relative;
    margin-bottom: 0.5rem;
}

.progress {
    height: 8px;
    border-radius: 4px;
    background-color: #e9ecef;
    overflow: hidden;
}

.progress-bar {
    transition: width 0.6s ease;
    border-radius: 4px;
}

.progress-text {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

/* 项目成员头像 */
.member-avatars {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.member-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 1;
}

.member-avatar:not(:first-child) {
    margin-left: -8px;
}

.member-avatar:hover {
    z-index: 10;
    transform: scale(1.1);
    transition: all 0.2s ease;
}

.member-more {
    background: #6c757d;
    font-size: 0.7rem;
}

/* 项目操作按钮 */
.project-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.action-btn {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    font-size: 0.875rem;
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.action-btn.btn-outline-primary:hover {
    background-color: var(--bs-primary);
    color: white;
}

.action-btn.btn-outline-success:hover {
    background-color: var(--bs-success);
    color: white;
}

.action-btn.btn-outline-danger:hover {
    background-color: var(--bs-danger);
    color: white;
}

/* 筛选器样式 */
.filter-card {
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.filter-card .card-body {
    padding: 1.5rem;
}

.input-group-text {
    background-color: #f8f9fa;
    border-color: #ced4da;
    color: #6c757d;
}

.form-select:focus,
.form-control:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* 视图切换按钮 */
.btn-check:checked + .btn {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
    color: white;
}

/* 统计卡片动画 */
.stats-card {
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stats-number {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1;
}

/* 表格样式 */
.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    background-color: #f8f9fa;
    padding: 1rem 0.75rem;
}

.table td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* 空状态样式 */
.empty-state {
    padding: 3rem 1rem;
}

.empty-state i {
    opacity: 0.5;
}

/* 加载动画 */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 1rem;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* 模态框样式 */
.modal-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
}

.modal-title {
    font-weight: 600;
    color: #495057;
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
    padding: 1rem 2rem;
}

/* 表单样式增强 */
.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-control,
.form-select {
    border-radius: 8px;
    border: 1px solid #ced4da;
    padding: 0.75rem 1rem;
    transition: all 0.2s ease;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.invalid-feedback {
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* 成员选择器 */
.member-selector {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 0.75rem;
    background-color: #f8f9fa;
    margin-top: 0.5rem;
}

.selected-member {
    display: inline-flex;
    align-items: center;
    background-color: var(--bs-primary);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 20px;
    font-size: 0.875rem;
    margin: 0.25rem;
}

.selected-member .remove-member {
    background: none;
    border: none;
    color: white;
    margin-left: 0.5rem;
    padding: 0;
    font-size: 1rem;
    cursor: pointer;
}

.user-search-item {
    padding: 0.75rem;
    border-bottom: 1px solid #dee2e6;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.user-search-item:hover {
    background-color: #f8f9fa;
}

.user-search-item:last-child {
    border-bottom: none;
}

/* 分页样式 */
.pagination {
    margin-bottom: 0;
}

.page-link {
    border-radius: 6px;
    margin: 0 2px;
    border: 1px solid #dee2e6;
    color: #6c757d;
    padding: 0.5rem 0.75rem;
}

.page-link:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
    color: #495057;
}

.page-item.active .page-link {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-content {
        padding: 1rem;
    }
    
    .project-card {
        margin-bottom: 1rem;
    }
    
    .member-avatars {
        justify-content: flex-start;
    }
    
    .project-actions {
        justify-content: center;
        margin-top: 1rem;
    }
    
    .stats-card .card-body {
        text-align: center;
    }
    
    .filter-card .row {
        row-gap: 1rem;
    }
    
    .modal-dialog {
        margin: 1rem;
    }
    
    .modal-body {
        padding: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

@media (max-width: 576px) {
    .navbar-brand {
        font-size: 1rem;
    }
    
    .btn {
        font-size: 0.875rem;
        padding: 0.5rem 1rem;
    }
    
    .card-title {
        font-size: 1rem;
    }
    
    .stats-number {
        font-size: 1.5rem;
    }
    
    .member-avatar {
        width: 28px;
        height: 28px;
        font-size: 0.7rem;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .project-card {
        background-color: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .project-card .card-header {
        background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
        border-bottom-color: #4a5568;
    }
    
    .project-card .card-footer {
        background-color: #2d3748;
        border-top-color: #4a5568;
    }
    
    .filter-card {
        background-color: #2d3748;
        border-color: #4a5568;
    }
    
    .table th {
        background-color: #2d3748;
        color: #e2e8f0;
    }
    
    .table tbody tr:hover {
        background-color: #4a5568;
    }
    
    .modal-content {
        background-color: #2d3748;
        color: #e2e8f0;
    }
    
    .modal-header {
        background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
        border-bottom-color: #4a5568;
    }
    
    .modal-footer {
        background-color: #2d3748;
        border-top-color: #4a5568;
    }
}

/* 打印样式 */
@media print {
    .navbar,
    .modal,
    .project-actions,
    .pagination {
        display: none !important;
    }
    
    .main-content {
        margin-top: 0 !important;
        padding: 0 !important;
    }
    
    .project-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .card-body {
        padding: 1rem;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.5s ease-out;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background-color: #212529;
    border-radius: 6px;
    padding: 0.5rem 0.75rem;
}

/* 自定义滚动条 */
.modal-body::-webkit-scrollbar,
.table-responsive::-webkit-scrollbar {
    width: 6px;
}

.modal-body::-webkit-scrollbar-track,
.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb,
.table-responsive::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb:hover,
.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}