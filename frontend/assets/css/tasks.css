/**
 * 任务管理页面样式
 */

/* 统计卡片样式 */
.stat-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-icon.bg-primary {
    background: linear-gradient(135deg, #0d6efd, #0056b3);
}

.stat-icon.bg-warning {
    background: linear-gradient(135deg, #ffc107, #e0a800);
}

.stat-icon.bg-info {
    background: linear-gradient(135deg, #0dcaf0, #0aa2c0);
}

.stat-icon.bg-success {
    background: linear-gradient(135deg, #198754, #146c43);
}

/* 任务表格样式 */
.table th {
    border-top: none;
    font-weight: 600;
    color: var(--bs-gray-700);
    background-color: var(--bs-gray-50);
}

.table td {
    vertical-align: middle;
    padding: 1rem 0.75rem;
}

.table tbody tr:hover {
    background-color: var(--bs-gray-50);
}

/* 任务状态徽章 */
.task-status {
    padding: 0.375rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.task-status.status-todo {
    background-color: #f8f9fa;
    color: #6c757d;
    border: 1px solid #dee2e6;
}

.task-status.status-in-progress {
    background-color: #cff4fc;
    color: #055160;
    border: 1px solid #b6effb;
}

.task-status.status-review {
    background-color: #fff3cd;
    color: #664d03;
    border: 1px solid #ffecb5;
}

.task-status.status-completed {
    background-color: #d1e7dd;
    color: #0f5132;
    border: 1px solid #badbcc;
}

.task-status.status-cancelled {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c2c7;
}

/* 优先级徽章 */
.task-priority {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.task-priority.priority-high {
    background-color: #f8d7da;
    color: #721c24;
}

.task-priority.priority-medium {
    background-color: #fff3cd;
    color: #664d03;
}

.task-priority.priority-low {
    background-color: #d1e7dd;
    color: #0f5132;
}

/* 看板视图样式 */
.kanban-board {
    display: flex;
    gap: 1rem;
    overflow-x: auto;
    padding-bottom: 1rem;
    min-height: 600px;
}

.kanban-column {
    flex: 0 0 280px;
    background-color: var(--bs-gray-50);
    border-radius: 12px;
    padding: 1rem;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
}

.kanban-header {
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--bs-gray-200);
}

.kanban-body {
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

/* 看板任务卡片 */
.kanban-task-card {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.2s ease;
    border-left: 4px solid transparent;
}

.kanban-task-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.kanban-task-card.priority-high {
    border-left-color: #dc3545;
}

.kanban-task-card.priority-medium {
    border-left-color: #ffc107;
}

.kanban-task-card.priority-low {
    border-left-color: #198754;
}

.kanban-task-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--bs-gray-800);
    font-size: 0.9rem;
    line-height: 1.4;
}

.kanban-task-project {
    font-size: 0.75rem;
    color: var(--bs-gray-600);
    margin-bottom: 0.5rem;
}

.kanban-task-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.75rem;
    font-size: 0.75rem;
    color: var(--bs-gray-600);
}

.kanban-task-assignee {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.kanban-task-due {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.kanban-task-due.overdue {
    color: #dc3545;
    font-weight: 600;
}

.kanban-task-due.due-soon {
    color: #fd7e14;
    font-weight: 600;
}

/* 任务标签 */
.task-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    margin-top: 0.5rem;
}

.task-tag {
    background-color: var(--bs-gray-100);
    color: var(--bs-gray-700);
    padding: 0.125rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 500;
}

/* 用户头像 */
.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
}

.user-avatar.size-sm {
    width: 24px;
    height: 24px;
    font-size: 0.65rem;
}

.user-avatar.size-lg {
    width: 48px;
    height: 48px;
    font-size: 1rem;
}

/* 进度条样式 */
.task-progress {
    height: 6px;
    background-color: var(--bs-gray-200);
    border-radius: 3px;
    overflow: hidden;
    margin: 0.5rem 0;
}

.task-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #198754, #20c997);
    border-radius: 3px;
    transition: width 0.3s ease;
}

/* 操作按钮 */
.action-buttons {
    display: flex;
    gap: 0.25rem;
}

.action-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-btn:hover {
    transform: scale(1.1);
}

.action-btn.btn-view {
    background-color: var(--bs-info);
    color: white;
}

.action-btn.btn-edit {
    background-color: var(--bs-warning);
    color: white;
}

.action-btn.btn-delete {
    background-color: var(--bs-danger);
    color: white;
}

/* 筛选器样式 */
.filter-section {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--bs-gray-600);
}

.empty-state-icon {
    font-size: 4rem;
    color: var(--bs-gray-400);
    margin-bottom: 1rem;
}

.empty-state-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--bs-gray-700);
}

.empty-state-description {
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
}

/* 加载动画 */
.loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--bs-gray-200);
    border-top: 4px solid var(--bs-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 任务详情模态框样式 */
.task-detail-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;
}

.task-detail-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--bs-gray-800);
}

.task-detail-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.task-detail-meta-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.task-detail-meta-label {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--bs-gray-600);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.task-detail-meta-value {
    font-size: 0.9rem;
    color: var(--bs-gray-800);
}

.task-detail-description {
    background-color: var(--bs-gray-50);
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.task-detail-section {
    margin-bottom: 1.5rem;
}

.task-detail-section-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: var(--bs-gray-800);
    border-bottom: 2px solid var(--bs-gray-200);
    padding-bottom: 0.5rem;
}

/* 批量操作样式 */
.batch-actions {
    background-color: var(--bs-primary);
    color: white;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    display: none;
    align-items: center;
    justify-content: space-between;
}

.batch-actions.show {
    display: flex;
}

.batch-actions-info {
    font-weight: 600;
}

.batch-actions-buttons {
    display: flex;
    gap: 0.5rem;
}

/* 拖拽样式 */
.kanban-task-card.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

.kanban-column.drag-over {
    background-color: var(--bs-primary-bg-subtle);
    border: 2px dashed var(--bs-primary);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .kanban-board {
        flex-direction: column;
    }
    
    .kanban-column {
        flex: none;
        max-height: 400px;
    }
    
    .task-detail-meta {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .stat-card .card-body {
        padding: 1rem;
    }
    
    .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 1.25rem;
    }
}

@media (max-width: 576px) {
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .kanban-task-card {
        padding: 0.75rem;
    }
    
    .kanban-task-title {
        font-size: 0.85rem;
    }
    
    .filter-section .row {
        gap: 0.5rem;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .stat-card {
        background-color: var(--bs-dark);
        color: var(--bs-light);
    }
    
    .kanban-column {
        background-color: var(--bs-dark);
    }
    
    .kanban-task-card {
        background-color: var(--bs-gray-800);
        color: var(--bs-light);
    }
    
    .task-detail-description {
        background-color: var(--bs-gray-800);
        color: var(--bs-light);
    }
}

/* 打印样式 */
@media print {
    .navbar,
    .action-buttons,
    .batch-actions,
    .modal {
        display: none !important;
    }
    
    .main-content {
        margin-top: 0;
        padding: 1rem;
    }
    
    .kanban-board {
        display: block;
    }
    
    .kanban-column {
        break-inside: avoid;
        margin-bottom: 1rem;
    }
    
    .stat-card {
        break-inside: avoid;
    }
}

/* 自定义滚动条 */
.kanban-body::-webkit-scrollbar {
    width: 6px;
}

.kanban-body::-webkit-scrollbar-track {
    background: var(--bs-gray-100);
    border-radius: 3px;
}

.kanban-body::-webkit-scrollbar-thumb {
    background: var(--bs-gray-400);
    border-radius: 3px;
}

.kanban-body::-webkit-scrollbar-thumb:hover {
    background: var(--bs-gray-500);
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.75rem;
}

.tooltip-inner {
    max-width: 200px;
    text-align: left;
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .task-status,
    .task-priority {
        border-width: 2px;
        font-weight: 700;
    }
    
    .kanban-task-card {
        border: 2px solid var(--bs-gray-400);
    }
    
    .action-btn {
        border: 2px solid currentColor;
    }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 工具类 */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.text-truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.cursor-pointer {
    cursor: pointer;
}

.cursor-grab {
    cursor: grab;
}

.cursor-grabbing {
    cursor: grabbing;
}

.border-start-primary {
    border-left: 4px solid var(--bs-primary) !important;
}

.border-start-success {
    border-left: 4px solid var(--bs-success) !important;
}

.border-start-warning {
    border-left: 4px solid var(--bs-warning) !important;
}

.border-start-danger {
    border-left: 4px solid var(--bs-danger) !important;
}