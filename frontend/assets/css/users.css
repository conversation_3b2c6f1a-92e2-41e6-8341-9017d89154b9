/**
 * 用户管理页面样式
 */

/* 统计卡片 */
.stat-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.stat-icon.bg-primary {
    background: linear-gradient(135deg, var(--bs-primary), #4f46e5);
}

.stat-icon.bg-success {
    background: linear-gradient(135deg, var(--bs-success), #059669);
}

.stat-icon.bg-warning {
    background: linear-gradient(135deg, var(--bs-warning), #d97706);
}

.stat-icon.bg-info {
    background: linear-gradient(135deg, var(--bs-info), #0284c7);
}

/* 用户状态徽章 */
.user-status {
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.user-status.active {
    background-color: #d1fae5;
    color: #065f46;
}

.user-status.inactive {
    background-color: #f3f4f6;
    color: #6b7280;
}

.user-status.pending {
    background-color: #fef3c7;
    color: #92400e;
}

.user-status.suspended {
    background-color: #fee2e2;
    color: #991b1b;
}

/* 角色徽章 */
.user-role {
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: capitalize;
}

.user-role.admin {
    background-color: #ddd6fe;
    color: #5b21b6;
}

.user-role.manager {
    background-color: #bfdbfe;
    color: #1e40af;
}

.user-role.member {
    background-color: #d1fae5;
    color: #065f46;
}

.user-role.viewer {
    background-color: #f3f4f6;
    color: #6b7280;
}

/* 用户头像 */
.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #e5e7eb;
}

.user-avatar-placeholder {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
}

/* 用户信息 */
.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-details h6 {
    margin: 0;
    font-weight: 600;
    color: #1f2937;
}

.user-details p {
    margin: 0;
    font-size: 0.875rem;
    color: #6b7280;
}

/* 用户卡片视图 */
.user-card {
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    background: white;
    height: 100%;
}

.user-card:hover {
    border-color: var(--bs-primary);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.user-card-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
}

.user-card-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #e5e7eb;
}

.user-card-avatar-placeholder {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1.2rem;
}

.user-card-info h6 {
    margin: 0;
    font-weight: 600;
    color: #1f2937;
}

.user-card-info p {
    margin: 0;
    font-size: 0.875rem;
    color: #6b7280;
}

.user-card-meta {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 16px;
}

.user-card-meta-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.875rem;
    color: #6b7280;
}

.user-card-meta-item i {
    width: 16px;
    text-align: center;
}

.user-card-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

/* 表格样式 */
.table th {
    border-top: none;
    font-weight: 600;
    color: #374151;
    background-color: #f9fafb;
    padding: 12px;
}

.table td {
    padding: 12px;
    vertical-align: middle;
    border-color: #f3f4f6;
}

.table tbody tr:hover {
    background-color: #f8fafc;
}

/* 操作按钮 */
.action-buttons {
    display: flex;
    gap: 4px;
}

.action-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    font-size: 0.875rem;
}

.action-btn:hover {
    transform: scale(1.1);
}

.action-btn.view {
    background-color: #dbeafe;
    color: #1e40af;
}

.action-btn.view:hover {
    background-color: #bfdbfe;
}

.action-btn.edit {
    background-color: #fef3c7;
    color: #92400e;
}

.action-btn.edit:hover {
    background-color: #fde68a;
}

.action-btn.delete {
    background-color: #fee2e2;
    color: #991b1b;
}

.action-btn.delete:hover {
    background-color: #fecaca;
}

.action-btn.reset {
    background-color: #e0e7ff;
    color: #3730a3;
}

.action-btn.reset:hover {
    background-color: #c7d2fe;
}

/* 筛选器 */
.filter-section {
    background: #f8fafc;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 24px;
}

.filter-section .form-label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 6px;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
}

.empty-state i {
    font-size: 4rem;
    color: #d1d5db;
    margin-bottom: 20px;
}

.empty-state h5 {
    color: #6b7280;
    margin-bottom: 8px;
}

.empty-state p {
    color: #9ca3af;
}

/* 加载动画 */
.loading-state {
    text-align: center;
    padding: 60px 20px;
}

.loading-state .spinner-border {
    width: 3rem;
    height: 3rem;
}

/* 模态框样式 */
.modal-header {
    border-bottom: 1px solid #e5e7eb;
    padding: 20px 24px;
}

.modal-title {
    font-weight: 600;
    color: #1f2937;
}

.modal-body {
    padding: 24px;
}

.modal-footer {
    border-top: 1px solid #e5e7eb;
    padding: 16px 24px;
}

/* 表单样式 */
.form-label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 6px;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.text-danger {
    color: #dc2626 !important;
}

/* 用户详情模态框 */
.user-detail-header {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 24px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e5e7eb;
}

.user-detail-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid #e5e7eb;
}

.user-detail-avatar-placeholder {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1.5rem;
}

.user-detail-info h4 {
    margin: 0 0 8px 0;
    font-weight: 600;
    color: #1f2937;
}

.user-detail-info p {
    margin: 0 0 4px 0;
    color: #6b7280;
}

.user-detail-tabs .nav-link {
    color: #6b7280;
    border: none;
    border-bottom: 2px solid transparent;
    border-radius: 0;
    padding: 12px 16px;
}

.user-detail-tabs .nav-link.active {
    color: var(--bs-primary);
    border-bottom-color: var(--bs-primary);
    background: none;
}

.user-detail-content {
    padding: 24px 0;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f3f4f6;
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 600;
    color: #374151;
}

.info-value {
    color: #6b7280;
}

/* 活动日志 */
.activity-item {
    display: flex;
    gap: 12px;
    padding: 16px 0;
    border-bottom: 1px solid #f3f4f6;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    flex-shrink: 0;
}

.activity-icon.login {
    background-color: #d1fae5;
    color: #065f46;
}

.activity-icon.logout {
    background-color: #fee2e2;
    color: #991b1b;
}

.activity-icon.update {
    background-color: #fef3c7;
    color: #92400e;
}

.activity-content h6 {
    margin: 0 0 4px 0;
    font-weight: 600;
    color: #1f2937;
    font-size: 0.875rem;
}

.activity-content p {
    margin: 0;
    color: #6b7280;
    font-size: 0.8rem;
}

/* 权限列表 */
.permission-group {
    margin-bottom: 24px;
}

.permission-group h6 {
    font-weight: 600;
    color: #374151;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e5e7eb;
}

.permission-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
}

.permission-name {
    color: #6b7280;
}

.permission-status {
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
}

.permission-status.granted {
    background-color: #d1fae5;
    color: #065f46;
}

.permission-status.denied {
    background-color: #fee2e2;
    color: #991b1b;
}

/* 分页样式 */
.pagination {
    margin: 0;
}

.page-link {
    color: #6b7280;
    border-color: #e5e7eb;
    padding: 8px 12px;
}

.page-link:hover {
    color: var(--bs-primary);
    background-color: #f8fafc;
    border-color: #e5e7eb;
}

.page-item.active .page-link {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .stat-card {
        margin-bottom: 16px;
    }
    
    .user-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .user-card {
        margin-bottom: 16px;
    }
    
    .filter-section {
        padding: 16px;
    }
    
    .modal-dialog {
        margin: 16px;
    }
    
    .user-detail-header {
        flex-direction: column;
        text-align: center;
    }
}

@media (max-width: 576px) {
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .action-btn {
        width: 28px;
        height: 28px;
        font-size: 0.75rem;
    }
    
    .user-avatar,
    .user-avatar-placeholder {
        width: 32px;
        height: 32px;
        font-size: 0.75rem;
    }
    
    .user-card-avatar,
    .user-card-avatar-placeholder {
        width: 48px;
        height: 48px;
        font-size: 1rem;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .stat-card {
        background-color: #1f2937;
        border-color: #374151;
    }
    
    .user-card {
        background-color: #1f2937;
        border-color: #374151;
    }
    
    .table th {
        background-color: #374151;
        color: #f9fafb;
    }
    
    .table tbody tr:hover {
        background-color: #374151;
    }
    
    .filter-section {
        background-color: #374151;
    }
    
    .modal-content {
        background-color: #1f2937;
        color: #f9fafb;
    }
}

/* 打印样式 */
@media print {
    .navbar,
    .modal,
    .action-buttons,
    .pagination {
        display: none !important;
    }
    
    .main-content {
        margin-top: 0 !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
    
    .table {
        font-size: 12px;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

/* 工具提示 */
.tooltip {
    font-size: 0.75rem;
}

.tooltip-inner {
    background-color: #1f2937;
    color: #f9fafb;
    border-radius: 6px;
    padding: 6px 10px;
}

/* 自定义滚动条 */
.modal-body::-webkit-scrollbar,
.table-responsive::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.modal-body::-webkit-scrollbar-track,
.table-responsive::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb,
.table-responsive::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb:hover,
.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .user-status,
    .user-role {
        border: 1px solid currentColor;
    }
    
    .action-btn {
        border: 1px solid currentColor;
    }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
    .stat-card,
    .user-card,
    .action-btn {
        transition: none;
    }
    
    .fade-in,
    .slide-in {
        animation: none;
    }
}

/* 工具类 */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.text-truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.cursor-pointer {
    cursor: pointer;
}

.border-dashed {
    border-style: dashed !important;
}

.bg-light-primary {
    background-color: rgba(13, 110, 253, 0.1) !important;
}

.bg-light-success {
    background-color: rgba(25, 135, 84, 0.1) !important;
}

.bg-light-warning {
    background-color: rgba(255, 193, 7, 0.1) !important;
}

.bg-light-danger {
    background-color: rgba(220, 53, 69, 0.1) !important;
}