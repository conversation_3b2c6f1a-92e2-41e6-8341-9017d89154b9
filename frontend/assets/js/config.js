/**
 * 应用配置文件
 * 定义全局配置常量和设置
 */

// 应用配置对象
const APP_CONFIG = {
    // API配置
    API_BASE_URL: 'http://localhost:8080/api',
    API_TIMEOUT: 30000, // 30秒超时
    
    // 存储键名
    TOKEN_KEY: 'pm_access_token',
    REFRESH_TOKEN_KEY: 'pm_refresh_token',
    USER_KEY: 'pm_user_info',
    SETTINGS_KEY: 'pm_user_settings',
    THEME_KEY: 'pm_theme',
    LANGUAGE_KEY: 'pm_language',
    
    // 分页配置
    DEFAULT_PAGE_SIZE: 10,
    MAX_PAGE_SIZE: 100,
    
    // 文件上传配置
    MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
    ALLOWED_FILE_TYPES: [
        'image/jpeg',
        'image/png',
        'image/gif',
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'text/plain',
        'text/csv'
    ],
    
    // 日期格式
    DATE_FORMAT: 'YYYY-MM-DD',
    DATETIME_FORMAT: 'YYYY-MM-DD HH:mm:ss',
    TIME_FORMAT: 'HH:mm:ss',
    
    // 状态配置
    PROJECT_STATUS: {
        PLANNING: { text: '规划中', color: 'primary', icon: 'fa-lightbulb' },
        IN_PROGRESS: { text: '进行中', color: 'success', icon: 'fa-play-circle' },
        COMPLETED: { text: '已完成', color: 'warning', icon: 'fa-check-circle' },
        ON_HOLD: { text: '暂停', color: 'secondary', icon: 'fa-pause-circle' },
        CANCELLED: { text: '已取消', color: 'danger', icon: 'fa-times-circle' }
    },
    
    TASK_STATUS: {
        TODO: { text: '待办', color: 'secondary', icon: 'fa-circle' },
        IN_PROGRESS: { text: '进行中', color: 'primary', icon: 'fa-play-circle' },
        REVIEW: { text: '待审核', color: 'warning', icon: 'fa-eye' },
        COMPLETED: { text: '已完成', color: 'success', icon: 'fa-check-circle' },
        CANCELLED: { text: '已取消', color: 'danger', icon: 'fa-times-circle' }
    },
    
    PRIORITY_LEVELS: {
        HIGH: { text: '高', color: 'danger', icon: 'fa-arrow-up' },
        MEDIUM: { text: '中', color: 'warning', icon: 'fa-minus' },
        LOW: { text: '低', color: 'success', icon: 'fa-arrow-down' }
    },
    
    APPROVAL_STATUS: {
        PENDING: { text: '待审核', color: 'warning', icon: 'fa-clock' },
        APPROVED: { text: '已通过', color: 'success', icon: 'fa-check' },
        REJECTED: { text: '已拒绝', color: 'danger', icon: 'fa-times' },
        CANCELLED: { text: '已取消', color: 'secondary', icon: 'fa-ban' }
    },
    
    // 用户角色
    USER_ROLES: {
        ADMIN: { text: '管理员', color: 'danger' },
        PROJECT_MANAGER: { text: '项目经理', color: 'primary' },
        DEVELOPER: { text: '开发人员', color: 'info' },
        TESTER: { text: '测试人员', color: 'warning' },
        VIEWER: { text: '查看者', color: 'secondary' }
    },
    
    // 项目成员角色
    PROJECT_MEMBER_ROLES: {
        OWNER: { text: '项目负责人', color: 'danger' },
        MANAGER: { text: '项目经理', color: 'primary' },
        MEMBER: { text: '项目成员', color: 'info' },
        VIEWER: { text: '查看者', color: 'secondary' }
    },
    
    // 通知类型
    NOTIFICATION_TYPES: {
        TASK_ASSIGNED: { text: '任务分配', icon: 'fa-tasks' },
        TASK_COMPLETED: { text: '任务完成', icon: 'fa-check-circle' },
        PROJECT_UPDATED: { text: '项目更新', icon: 'fa-project-diagram' },
        APPROVAL_REQUEST: { text: '审核请求', icon: 'fa-clipboard-check' },
        DEADLINE_REMINDER: { text: '截止提醒', icon: 'fa-clock' },
        SYSTEM_MESSAGE: { text: '系统消息', icon: 'fa-bell' }
    },
    
    // 主题配置
    THEMES: {
        LIGHT: 'light',
        DARK: 'dark',
        AUTO: 'auto'
    },
    
    // 语言配置
    LANGUAGES: {
        ZH_CN: { code: 'zh-CN', name: '简体中文' },
        EN_US: { code: 'en-US', name: 'English' }
    },
    
    // 默认设置
    DEFAULT_SETTINGS: {
        theme: 'light',
        language: 'zh-CN',
        pageSize: 10,
        notifications: {
            email: true,
            browser: true,
            taskAssigned: true,
            taskCompleted: true,
            projectUpdated: true,
            approvalRequest: true,
            deadlineReminder: true
        },
        dashboard: {
            showRecentProjects: true,
            showRecentTasks: true,
            showStatistics: true,
            refreshInterval: 300000 // 5分钟
        }
    },
    
    // 缓存配置
    CACHE: {
        USER_INFO_TTL: 3600000, // 1小时
        PROJECT_LIST_TTL: 300000, // 5分钟
        TASK_LIST_TTL: 300000, // 5分钟
        STATISTICS_TTL: 600000 // 10分钟
    },
    
    // 错误代码
    ERROR_CODES: {
        UNAUTHORIZED: 401,
        FORBIDDEN: 403,
        NOT_FOUND: 404,
        VALIDATION_ERROR: 422,
        SERVER_ERROR: 500
    },
    
    // 正则表达式
    REGEX: {
        EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        PHONE: /^1[3-9]\d{9}$/,
        PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
        USERNAME: /^[a-zA-Z0-9_]{3,20}$/,
        PROJECT_CODE: /^[A-Z0-9]{3,10}$/
    },
    
    // 消息配置
    MESSAGES: {
        SUCCESS: {
            LOGIN: '登录成功',
            LOGOUT: '退出成功',
            SAVE: '保存成功',
            DELETE: '删除成功',
            UPDATE: '更新成功',
            CREATE: '创建成功'
        },
        ERROR: {
            NETWORK: '网络连接失败，请检查网络设置',
            UNAUTHORIZED: '登录已过期，请重新登录',
            FORBIDDEN: '权限不足，无法执行此操作',
            NOT_FOUND: '请求的资源不存在',
            VALIDATION: '输入数据格式不正确',
            SERVER: '服务器内部错误，请稍后重试',
            UNKNOWN: '未知错误，请联系管理员'
        },
        WARNING: {
            UNSAVED_CHANGES: '您有未保存的更改，确定要离开吗？',
            DELETE_CONFIRM: '确定要删除吗？此操作不可撤销。',
            LOGOUT_CONFIRM: '确定要退出登录吗？'
        }
    },
    
    // 动画配置
    ANIMATION: {
        DURATION: {
            FAST: 200,
            NORMAL: 300,
            SLOW: 500
        },
        EASING: {
            EASE_IN: 'ease-in',
            EASE_OUT: 'ease-out',
            EASE_IN_OUT: 'ease-in-out'
        }
    },
    
    // 图表配置
    CHART: {
        COLORS: {
            PRIMARY: '#0d6efd',
            SUCCESS: '#198754',
            WARNING: '#ffc107',
            DANGER: '#dc3545',
            INFO: '#0dcaf0',
            SECONDARY: '#6c757d'
        },
        DEFAULT_OPTIONS: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    },
    
    // 开发环境配置
    DEV: {
        DEBUG: true,
        LOG_LEVEL: 'debug',
        MOCK_API: false
    },
    
    // 生产环境配置
    PROD: {
        DEBUG: false,
        LOG_LEVEL: 'error',
        MOCK_API: false
    }
};

// 环境检测
const isDevelopment = window.location.hostname === 'localhost' || 
                    window.location.hostname === '127.0.0.1' ||
                    window.location.hostname.includes('dev');

// 根据环境设置配置
if (isDevelopment) {
    Object.assign(APP_CONFIG, APP_CONFIG.DEV);
} else {
    Object.assign(APP_CONFIG, APP_CONFIG.PROD);
    // 生产环境可能需要不同的API地址
    // APP_CONFIG.API_BASE_URL = 'https://api.yourprojectmanagement.com/api';
}

// 工具函数
const CONFIG_UTILS = {
    /**
     * 获取状态配置
     */
    getStatusConfig(type, status) {
        const configs = {
            project: APP_CONFIG.PROJECT_STATUS,
            task: APP_CONFIG.TASK_STATUS,
            approval: APP_CONFIG.APPROVAL_STATUS
        };
        return configs[type]?.[status] || { text: status, color: 'secondary', icon: 'fa-question' };
    },
    
    /**
     * 获取优先级配置
     */
    getPriorityConfig(priority) {
        return APP_CONFIG.PRIORITY_LEVELS[priority] || { text: priority, color: 'secondary', icon: 'fa-minus' };
    },
    
    /**
     * 获取角色配置
     */
    getRoleConfig(type, role) {
        const configs = {
            user: APP_CONFIG.USER_ROLES,
            project: APP_CONFIG.PROJECT_MEMBER_ROLES
        };
        return configs[type]?.[role] || { text: role, color: 'secondary' };
    },
    
    /**
     * 验证文件类型
     */
    isValidFileType(fileType) {
        return APP_CONFIG.ALLOWED_FILE_TYPES.includes(fileType);
    },
    
    /**
     * 验证文件大小
     */
    isValidFileSize(fileSize) {
        return fileSize <= APP_CONFIG.MAX_FILE_SIZE;
    },
    
    /**
     * 格式化文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    /**
     * 获取错误消息
     */
    getErrorMessage(errorCode) {
        const errorMap = {
            [APP_CONFIG.ERROR_CODES.UNAUTHORIZED]: APP_CONFIG.MESSAGES.ERROR.UNAUTHORIZED,
            [APP_CONFIG.ERROR_CODES.FORBIDDEN]: APP_CONFIG.MESSAGES.ERROR.FORBIDDEN,
            [APP_CONFIG.ERROR_CODES.NOT_FOUND]: APP_CONFIG.MESSAGES.ERROR.NOT_FOUND,
            [APP_CONFIG.ERROR_CODES.VALIDATION_ERROR]: APP_CONFIG.MESSAGES.ERROR.VALIDATION,
            [APP_CONFIG.ERROR_CODES.SERVER_ERROR]: APP_CONFIG.MESSAGES.ERROR.SERVER
        };
        return errorMap[errorCode] || APP_CONFIG.MESSAGES.ERROR.UNKNOWN;
    },
    
    /**
     * 获取用户设置
     */
    getUserSettings() {
        try {
            const settings = localStorage.getItem(APP_CONFIG.SETTINGS_KEY);
            return settings ? { ...APP_CONFIG.DEFAULT_SETTINGS, ...JSON.parse(settings) } : APP_CONFIG.DEFAULT_SETTINGS;
        } catch (error) {
            console.error('获取用户设置失败:', error);
            return APP_CONFIG.DEFAULT_SETTINGS;
        }
    },
    
    /**
     * 保存用户设置
     */
    saveUserSettings(settings) {
        try {
            const currentSettings = this.getUserSettings();
            const newSettings = { ...currentSettings, ...settings };
            localStorage.setItem(APP_CONFIG.SETTINGS_KEY, JSON.stringify(newSettings));
            return true;
        } catch (error) {
            console.error('保存用户设置失败:', error);
            return false;
        }
    },
    
    /**
     * 清除所有本地存储
     */
    clearStorage() {
        const keys = [
            APP_CONFIG.TOKEN_KEY,
            APP_CONFIG.REFRESH_TOKEN_KEY,
            APP_CONFIG.USER_KEY,
            APP_CONFIG.SETTINGS_KEY
        ];
        keys.forEach(key => localStorage.removeItem(key));
    }
};

// 导出到全局
window.APP_CONFIG = APP_CONFIG;
window.CONFIG_UTILS = CONFIG_UTILS;

// 兼容性导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { APP_CONFIG, CONFIG_UTILS };
}

// 开发环境调试信息
if (APP_CONFIG.DEBUG) {
    console.log('应用配置已加载:', APP_CONFIG);
    console.log('当前环境:', isDevelopment ? '开发环境' : '生产环境');
}