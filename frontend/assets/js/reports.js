/**
 * 报告管理页面逻辑
 */

class ReportManager {
    constructor() {
        this.currentReportType = 'overview';
        this.timeRange = {
            start: null,
            end: null
        };
        this.charts = {};
        this.projectAPI = new ProjectAPI();
        this.taskAPI = new TaskAPI();
        this.userAPI = new UserAPI();
        this.authAPI = new AuthAPI();
        
        this.init();
    }

    async init() {
        try {
            // 检查认证状态
            if (!await this.authAPI.checkAuth()) {
                window.location.href = 'login.html';
                return;
            }

            // 设置事件监听器
            this.setupEventListeners();
            
            // 设置默认时间范围
            this.setDefaultTimeRange();
            
            // 生成默认报告
            await this.generateReport();
            
        } catch (error) {
            console.error('初始化失败:', error);
            this.showMessage('初始化失败，请刷新页面重试', 'error');
        }
    }

    setupEventListeners() {
        // 报告类型切换
        document.getElementById('reportType').addEventListener('change', (e) => {
            this.currentReportType = e.target.value;
            this.switchReportView();
        });

        // 时间范围切换
        document.getElementById('timeRange').addEventListener('change', (e) => {
            this.handleTimeRangeChange(e.target.value);
        });

        // 自定义日期变化
        document.getElementById('startDate').addEventListener('change', () => {
            this.updateTimeRange();
        });

        document.getElementById('endDate').addEventListener('change', () => {
            this.updateTimeRange();
        });

        // 导出报告表单
        document.getElementById('exportReportForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.exportReport();
        });
    }

    setDefaultTimeRange() {
        const end = new Date();
        const start = new Date();
        start.setDate(end.getDate() - 30); // 默认最近30天
        
        this.timeRange.start = start;
        this.timeRange.end = end;
        
        // 设置日期输入框的值
        document.getElementById('startDate').value = this.formatDateForInput(start);
        document.getElementById('endDate').value = this.formatDateForInput(end);
    }

    handleTimeRangeChange(value) {
        const startDateContainer = document.getElementById('startDateContainer');
        const endDateContainer = document.getElementById('endDateContainer');
        
        if (value === 'custom') {
            startDateContainer.style.display = 'block';
            endDateContainer.style.display = 'block';
        } else {
            startDateContainer.style.display = 'none';
            endDateContainer.style.display = 'none';
            
            const end = new Date();
            const start = new Date();
            start.setDate(end.getDate() - parseInt(value));
            
            this.timeRange.start = start;
            this.timeRange.end = end;
        }
    }

    updateTimeRange() {
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;
        
        if (startDate && endDate) {
            this.timeRange.start = new Date(startDate);
            this.timeRange.end = new Date(endDate);
        }
    }

    switchReportView() {
        // 隐藏所有报告区域
        document.querySelectorAll('.report-section').forEach(section => {
            section.style.display = 'none';
        });
        
        // 显示当前选中的报告区域
        const currentSection = document.getElementById(`${this.currentReportType}Report`);
        if (currentSection) {
            currentSection.style.display = 'block';
        }
    }

    async generateReport() {
        try {
            this.showLoading(true);
            
            switch (this.currentReportType) {
                case 'overview':
                    await this.generateOverviewReport();
                    break;
                case 'project':
                    await this.generateProjectReport();
                    break;
                case 'task':
                    await this.generateTaskReport();
                    break;
                case 'user':
                    await this.generateUserReport();
                    break;
                case 'workload':
                    await this.generateWorkloadReport();
                    break;
            }
            
        } catch (error) {
            console.error('生成报告失败:', error);
            this.showMessage('生成报告失败', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    async generateOverviewReport() {
        // 获取统计数据
        const [projectStats, taskStats] = await Promise.all([
            this.getProjectStatistics(),
            this.getTaskStatistics()
        ]);
        
        // 更新统计卡片
        this.updateStatCards(projectStats, taskStats);
        
        // 生成图表
        await this.generateProgressChart();
        await this.generateTaskStatusChart();
        
        // 生成项目概览表格
        await this.generateProjectOverviewTable();
    }

    async getProjectStatistics() {
        try {
            // 模拟项目统计数据
            return {
                total: 15,
                active: 8,
                completed: 6,
                onHold: 1,
                growth: 12.5
            };
        } catch (error) {
            console.error('获取项目统计失败:', error);
            return { total: 0, active: 0, completed: 0, onHold: 0, growth: 0 };
        }
    }

    async getTaskStatistics() {
        try {
            // 模拟任务统计数据
            return {
                total: 156,
                todo: 45,
                inProgress: 38,
                review: 12,
                completed: 61,
                totalHours: 1248,
                completionRate: 72.5,
                growth: 8.3
            };
        } catch (error) {
            console.error('获取任务统计失败:', error);
            return {
                total: 0, todo: 0, inProgress: 0, review: 0, completed: 0,
                totalHours: 0, completionRate: 0, growth: 0
            };
        }
    }

    updateStatCards(projectStats, taskStats) {
        document.getElementById('totalProjects').textContent = projectStats.total;
        document.getElementById('totalTasks').textContent = taskStats.total;
        document.getElementById('totalHours').textContent = taskStats.totalHours;
        document.getElementById('completionRate').textContent = `${taskStats.completionRate}%`;
        
        document.getElementById('projectsGrowth').textContent = `${projectStats.growth}%`;
        document.getElementById('tasksGrowth').textContent = `${taskStats.growth}%`;
        document.getElementById('hoursGrowth').textContent = `${taskStats.growth}%`;
        document.getElementById('completionGrowth').textContent = `${taskStats.growth}%`;
    }

    async generateProgressChart() {
        const ctx = document.getElementById('progressChart').getContext('2d');
        
        // 销毁现有图表
        if (this.charts.progress) {
            this.charts.progress.destroy();
        }
        
        // 模拟进度数据
        const data = {
            labels: ['第1周', '第2周', '第3周', '第4周'],
            datasets: [{
                label: '项目完成数',
                data: [2, 4, 3, 6],
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                tension: 0.4,
                fill: true
            }, {
                label: '任务完成数',
                data: [15, 28, 22, 35],
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                tension: 0.4,
                fill: true
            }]
        };
        
        this.charts.progress = new Chart(ctx, {
            type: 'line',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    async generateTaskStatusChart() {
        const ctx = document.getElementById('taskStatusChart').getContext('2d');
        
        // 销毁现有图表
        if (this.charts.taskStatus) {
            this.charts.taskStatus.destroy();
        }
        
        const data = {
            labels: ['待办', '进行中', '待审核', '已完成'],
            datasets: [{
                data: [45, 38, 12, 61],
                backgroundColor: [
                    '#6c757d',
                    '#007bff',
                    '#ffc107',
                    '#28a745'
                ],
                borderWidth: 0
            }]
        };
        
        this.charts.taskStatus = new Chart(ctx, {
            type: 'doughnut',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    async generateProjectOverviewTable() {
        try {
            // 模拟项目数据
            const projects = [
                {
                    id: 1,
                    name: '电商平台重构',
                    manager: '张三',
                    progress: 75,
                    taskCount: 24,
                    completionRate: 68,
                    status: 'active',
                    dueDate: '2024-03-15'
                },
                {
                    id: 2,
                    name: '移动端应用开发',
                    manager: '李四',
                    progress: 45,
                    taskCount: 18,
                    completionRate: 42,
                    status: 'active',
                    dueDate: '2024-04-20'
                },
                {
                    id: 3,
                    name: '数据分析系统',
                    manager: '王五',
                    progress: 100,
                    taskCount: 15,
                    completionRate: 100,
                    status: 'completed',
                    dueDate: '2024-02-28'
                }
            ];
            
            const tbody = document.getElementById('projectOverviewTable');
            tbody.innerHTML = projects.map(project => `
                <tr>
                    <td>
                        <div class="fw-semibold">${this.escapeHtml(project.name)}</div>
                    </td>
                    <td>
                        <div class="d-flex align-items-center">
                            <div class="user-avatar me-2">
                                ${project.manager.charAt(0)}
                            </div>
                            ${this.escapeHtml(project.manager)}
                        </div>
                    </td>
                    <td>
                        <div class="progress-bar-custom">
                            <div class="progress bg-primary" style="width: ${project.progress}%"></div>
                        </div>
                        <small class="text-muted">${project.progress}%</small>
                    </td>
                    <td>
                        <span class="badge bg-light text-dark">${project.taskCount}</span>
                    </td>
                    <td>
                        <span class="fw-semibold ${project.completionRate >= 80 ? 'text-success' : project.completionRate >= 50 ? 'text-warning' : 'text-danger'}">
                            ${project.completionRate}%
                        </span>
                    </td>
                    <td>
                        <span class="project-status status-${project.status}">
                            ${this.getStatusText(project.status)}
                        </span>
                    </td>
                    <td>
                        <span class="${this.getDueDateClass(project.dueDate)}">
                            ${this.formatDate(project.dueDate)}
                        </span>
                    </td>
                </tr>
            `).join('');
            
        } catch (error) {
            console.error('生成项目概览表格失败:', error);
        }
    }

    async generateProjectReport() {
        const content = document.getElementById('projectReportContent');
        content.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <canvas id="projectProgressChart" height="300"></canvas>
                </div>
                <div class="col-md-6">
                    <canvas id="projectStatusChart" height="300"></canvas>
                </div>
            </div>
            <div class="mt-4">
                <h6>项目详细列表</h6>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>项目名称</th>
                                <th>开始时间</th>
                                <th>结束时间</th>
                                <th>进度</th>
                                <th>状态</th>
                                <th>团队成员</th>
                            </tr>
                        </thead>
                        <tbody id="projectDetailTable">
                            <!-- 项目详细数据 -->
                        </tbody>
                    </table>
                </div>
            </div>
        `;
        
        // 生成项目相关图表
        setTimeout(() => {
            this.generateProjectCharts();
        }, 100);
    }

    async generateTaskReport() {
        const content = document.getElementById('taskReportContent');
        content.innerHTML = `
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="stat-card">
                        <div class="stat-icon bg-primary">
                            <i class="fas fa-tasks"></i>
                        </div>
                        <div class="stat-content">
                            <h3>156</h3>
                            <p>总任务数</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stat-card">
                        <div class="stat-icon bg-success">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h3>61</h3>
                            <p>已完成</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stat-card">
                        <div class="stat-icon bg-warning">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h3>1248</h3>
                            <p>总工时</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-8">
                    <canvas id="taskTrendChart" height="300"></canvas>
                </div>
                <div class="col-md-4">
                    <canvas id="taskPriorityChart" height="300"></canvas>
                </div>
            </div>
        `;
        
        setTimeout(() => {
            this.generateTaskCharts();
        }, 100);
    }

    async generateUserReport() {
        const content = document.getElementById('userReportContent');
        content.innerHTML = `
            <div class="row mb-4">
                <div class="col-12">
                    <h6>团队成员绩效</h6>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>成员</th>
                                    <th>完成任务</th>
                                    <th>工作时长</th>
                                    <th>效率评分</th>
                                    <th>项目参与</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="user-avatar me-2">张</div>
                                            张三
                                        </div>
                                    </td>
                                    <td><span class="badge bg-success">24</span></td>
                                    <td>168小时</td>
                                    <td><span class="text-success fw-bold">95分</span></td>
                                    <td>3个项目</td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="user-avatar me-2">李</div>
                                            李四
                                        </div>
                                    </td>
                                    <td><span class="badge bg-success">18</span></td>
                                    <td>142小时</td>
                                    <td><span class="text-warning fw-bold">88分</span></td>
                                    <td>2个项目</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;
    }

    async generateWorkloadReport() {
        const content = document.getElementById('workloadReportContent');
        content.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <canvas id="workloadChart" height="300"></canvas>
                </div>
                <div class="col-md-6">
                    <canvas id="departmentWorkloadChart" height="300"></canvas>
                </div>
            </div>
        `;
        
        setTimeout(() => {
            this.generateWorkloadCharts();
        }, 100);
    }

    generateProjectCharts() {
        // 项目进度图表
        const progressCtx = document.getElementById('projectProgressChart')?.getContext('2d');
        if (progressCtx) {
            new Chart(progressCtx, {
                type: 'bar',
                data: {
                    labels: ['电商平台', '移动应用', '数据系统', '官网改版'],
                    datasets: [{
                        label: '完成进度',
                        data: [75, 45, 100, 30],
                        backgroundColor: '#007bff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
        }
        
        // 项目状态图表
        const statusCtx = document.getElementById('projectStatusChart')?.getContext('2d');
        if (statusCtx) {
            new Chart(statusCtx, {
                type: 'pie',
                data: {
                    labels: ['进行中', '已完成', '暂停', '计划中'],
                    datasets: [{
                        data: [8, 6, 1, 2],
                        backgroundColor: ['#007bff', '#28a745', '#ffc107', '#6c757d']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        }
    }

    generateTaskCharts() {
        // 任务趋势图表
        const trendCtx = document.getElementById('taskTrendChart')?.getContext('2d');
        if (trendCtx) {
            new Chart(trendCtx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        label: '创建任务',
                        data: [20, 25, 30, 28, 35, 32],
                        borderColor: '#007bff',
                        tension: 0.4
                    }, {
                        label: '完成任务',
                        data: [15, 22, 28, 25, 30, 29],
                        borderColor: '#28a745',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        }
        
        // 任务优先级图表
        const priorityCtx = document.getElementById('taskPriorityChart')?.getContext('2d');
        if (priorityCtx) {
            new Chart(priorityCtx, {
                type: 'doughnut',
                data: {
                    labels: ['高', '中', '低'],
                    datasets: [{
                        data: [25, 85, 46],
                        backgroundColor: ['#dc3545', '#ffc107', '#28a745']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        }
    }

    generateWorkloadCharts() {
        // 工作量分布图表
        const workloadCtx = document.getElementById('workloadChart')?.getContext('2d');
        if (workloadCtx) {
            new Chart(workloadCtx, {
                type: 'bar',
                data: {
                    labels: ['张三', '李四', '王五', '赵六', '钱七'],
                    datasets: [{
                        label: '工作时长(小时)',
                        data: [168, 142, 156, 134, 128],
                        backgroundColor: '#007bff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        }
        
        // 部门工作量图表
        const deptCtx = document.getElementById('departmentWorkloadChart')?.getContext('2d');
        if (deptCtx) {
            new Chart(deptCtx, {
                type: 'pie',
                data: {
                    labels: ['开发部', '设计部', '测试部', '产品部'],
                    datasets: [{
                        data: [450, 280, 320, 180],
                        backgroundColor: ['#007bff', '#28a745', '#ffc107', '#dc3545']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        }
    }

    async exportReport() {
        try {
            const formData = new FormData(document.getElementById('exportReportForm'));
            const format = formData.get('format');
            const reportType = formData.get('reportType');
            const startDate = formData.get('startDate');
            const endDate = formData.get('endDate');
            
            this.showMessage('正在导出报告...', 'info');
            
            // 模拟导出过程
            setTimeout(() => {
                this.showMessage(`${format.toUpperCase()}格式的${this.getReportTypeName(reportType)}已导出`, 'success');
                
                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('exportReportModal'));
                modal.hide();
            }, 2000);
            
        } catch (error) {
            console.error('导出报告失败:', error);
            this.showMessage('导出报告失败', 'error');
        }
    }

    getReportTypeName(type) {
        const typeMap = {
            'overview': '概览报告',
            'project': '项目报告',
            'task': '任务报告',
            'user': '用户绩效报告',
            'workload': '工作量统计报告'
        };
        return typeMap[type] || '报告';
    }

    getStatusText(status) {
        const statusMap = {
            'active': '进行中',
            'completed': '已完成',
            'on-hold': '暂停',
            'cancelled': '已取消'
        };
        return statusMap[status] || status;
    }

    getDueDateClass(dueDate) {
        const now = new Date();
        const due = new Date(dueDate);
        const diffDays = Math.ceil((due - now) / (1000 * 60 * 60 * 24));
        
        if (diffDays < 0) {
            return 'text-danger fw-bold';
        } else if (diffDays <= 7) {
            return 'text-warning fw-bold';
        }
        return 'text-muted';
    }

    formatDate(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN');
    }

    formatDateForInput(date) {
        return date.toISOString().split('T')[0];
    }

    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    showLoading(show) {
        const existingSpinner = document.querySelector('.loading-spinner');
        
        if (show && !existingSpinner) {
            const spinner = document.createElement('div');
            spinner.className = 'loading-spinner';
            spinner.innerHTML = '<div class="spinner"></div>';
            
            const reportContent = document.getElementById('reportContent');
            if (reportContent) {
                reportContent.appendChild(spinner);
            }
        } else if (!show && existingSpinner) {
            existingSpinner.remove();
        }
    }

    showMessage(message, type = 'info') {
        // 使用全局的 showToast 函数
        if (typeof showToast === 'function') {
            showToast(message, type);
        } else {
            alert(message);
        }
    }
}

// 全局函数
function refreshReports() {
    if (window.reportManager) {
        window.reportManager.generateReport();
    }
}

function generateReport() {
    if (window.reportManager) {
        window.reportManager.generateReport();
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    window.reportManager = new ReportManager();
});