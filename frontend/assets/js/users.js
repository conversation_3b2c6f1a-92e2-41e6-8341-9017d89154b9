/**
 * 用户管理页面逻辑
 */

class UserManager {
    constructor() {
        this.currentPage = 1;
        this.pageSize = 20;
        this.totalPages = 0;
        this.totalCount = 0;
        this.currentFilters = {};
        this.selectedUsers = new Set();
        this.viewMode = 'list'; // 'list' or 'card'
        this.sortBy = 'created_at';
        this.sortOrder = 'desc';
        
        this.userAPI = new UserAPI();
        this.init();
    }

    /**
     * 初始化
     */
    async init() {
        try {
            // 检查认证状态
            const authAPI = new AuthAPI();
            const isAuthenticated = await authAPI.checkAuth();
            if (!isAuthenticated) {
                window.location.href = 'login.html';
                return;
            }

            // 设置事件监听器
            this.setupEventListeners();
            
            // 加载初始数据
            await this.loadUsers();
            await this.loadStatistics();
            await this.loadDepartments();
            
        } catch (error) {
            console.error('初始化失败:', error);
            this.showMessage('初始化失败: ' + error.message, 'error');
        }
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 搜索
        const searchInput = document.getElementById('searchInput');
        const searchBtn = document.getElementById('searchBtn');
        
        searchBtn?.addEventListener('click', () => this.handleSearch());
        searchInput?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.handleSearch();
            }
        });

        // 筛选器
        const filters = ['statusFilter', 'roleFilter', 'departmentFilter'];
        filters.forEach(filterId => {
            const element = document.getElementById(filterId);
            element?.addEventListener('change', () => this.handleFilterChange());
        });

        // 重置筛选器
        document.getElementById('resetFilters')?.addEventListener('click', () => this.resetFilters());

        // 导出用户
        document.getElementById('exportUsers')?.addEventListener('click', () => this.exportUsers());

        // 视图模式切换
        document.querySelectorAll('input[name="viewMode"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                this.viewMode = e.target.id === 'cardView' ? 'card' : 'list';
                this.renderUsers();
            });
        });

        // 显示非活跃用户
        document.getElementById('showInactiveUsers')?.addEventListener('change', (e) => {
            this.currentFilters.include_inactive = e.target.checked;
            this.loadUsers();
        });

        // 全选
        document.getElementById('selectAll')?.addEventListener('change', (e) => {
            this.handleSelectAll(e.target.checked);
        });

        // 创建用户表单
        document.getElementById('createUserForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleCreateUser();
        });

        // 密码显示切换
        window.togglePassword = (button) => {
            const input = button.previousElementSibling;
            const icon = button.querySelector('i');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.className = 'fas fa-eye-slash';
            } else {
                input.type = 'password';
                icon.className = 'fas fa-eye';
            }
        };

        // 全局用户操作函数
        window.viewUser = (userId) => this.viewUser(userId);
        window.editUser = (userId) => this.editUser(userId);
        window.deleteUser = (userId) => this.deleteUser(userId);
        window.resetUserPassword = (userId) => this.resetUserPassword(userId);
        window.toggleUserStatus = (userId) => this.toggleUserStatus(userId);
    }

    /**
     * 加载用户列表
     */
    async loadUsers() {
        try {
            this.showLoading(true);
            
            const params = {
                page: this.currentPage,
                page_size: this.pageSize,
                sort_by: this.sortBy,
                sort_order: this.sortOrder,
                ...this.currentFilters
            };

            const response = await this.userAPI.getUsers(params);
            
            this.users = response.data || [];
            this.totalCount = response.total || 0;
            this.totalPages = Math.ceil(this.totalCount / this.pageSize);
            
            this.renderUsers();
            this.renderPagination();
            this.updatePageInfo();
            
        } catch (error) {
            console.error('加载用户列表失败:', error);
            this.showMessage('加载用户列表失败: ' + error.message, 'error');
            this.showEmptyState();
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * 加载统计数据
     */
    async loadStatistics() {
        try {
            const stats = await this.userAPI.getUserStatistics();
            
            document.getElementById('totalUsers').textContent = stats.total_users || 0;
            document.getElementById('activeUsers').textContent = stats.active_users || 0;
            document.getElementById('pendingUsers').textContent = stats.pending_users || 0;
            document.getElementById('newUsersToday').textContent = stats.new_users_today || 0;
            
            // 添加数字动画效果
            this.animateNumbers();
            
        } catch (error) {
            console.error('加载统计数据失败:', error);
        }
    }

    /**
     * 加载部门列表
     */
    async loadDepartments() {
        try {
            const departments = await this.userAPI.getDepartments();
            
            const departmentSelects = document.querySelectorAll('select[name="department_id"], #departmentFilter');
            departmentSelects.forEach(select => {
                // 清空现有选项（保留第一个默认选项）
                const firstOption = select.firstElementChild;
                select.innerHTML = '';
                if (firstOption) {
                    select.appendChild(firstOption);
                }
                
                // 添加部门选项
                departments.forEach(dept => {
                    const option = document.createElement('option');
                    option.value = dept.id;
                    option.textContent = dept.name;
                    select.appendChild(option);
                });
            });
            
        } catch (error) {
            console.error('加载部门列表失败:', error);
        }
    }

    /**
     * 渲染用户列表
     */
    renderUsers() {
        if (this.viewMode === 'list') {
            this.renderListView();
        } else {
            this.renderCardView();
        }
    }

    /**
     * 渲染列表视图
     */
    renderListView() {
        const container = document.getElementById('listViewContainer');
        const cardContainer = document.getElementById('cardViewContainer');
        const tbody = document.getElementById('usersTableBody');
        
        if (!container || !tbody) return;
        
        container.style.display = 'block';
        cardContainer.style.display = 'none';
        
        if (this.users.length === 0) {
            this.showEmptyState();
            return;
        }
        
        tbody.innerHTML = this.users.map(user => `
            <tr>
                <td>
                    <div class="form-check">
                        <input class="form-check-input user-checkbox" type="checkbox" 
                               value="${user.id}" onchange="userManager.handleUserSelect(${user.id}, this.checked)">
                    </div>
                </td>
                <td>
                    <div class="user-info">
                        ${this.renderUserAvatar(user)}
                        <div class="user-details">
                            <h6>${this.escapeHtml(user.full_name || user.username)}</h6>
                            <p>${this.escapeHtml(user.email)}</p>
                        </div>
                    </div>
                </td>
                <td>
                    <span class="user-role ${user.role}">${this.getRoleText(user.role)}</span>
                </td>
                <td>
                    ${user.department ? this.escapeHtml(user.department.name) : '-'}
                </td>
                <td>
                    <span class="user-status ${user.status}">${this.getStatusText(user.status)}</span>
                </td>
                <td>
                    ${user.last_login_at ? this.formatDate(user.last_login_at) : '从未登录'}
                </td>
                <td>
                    ${this.formatDate(user.created_at)}
                </td>
                <td>
                    <div class="action-buttons">
                        <button type="button" class="action-btn view" onclick="viewUser(${user.id})" 
                                title="查看详情">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button type="button" class="action-btn edit" onclick="editUser(${user.id})" 
                                title="编辑用户">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="action-btn reset" onclick="resetUserPassword(${user.id})" 
                                title="重置密码">
                            <i class="fas fa-key"></i>
                        </button>
                        <button type="button" class="action-btn delete" onclick="deleteUser(${user.id})" 
                                title="删除用户">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
        
        this.hideEmptyState();
    }

    /**
     * 渲染卡片视图
     */
    renderCardView() {
        const container = document.getElementById('cardViewContainer');
        const listContainer = document.getElementById('listViewContainer');
        
        if (!container) return;
        
        container.style.display = 'flex';
        listContainer.style.display = 'none';
        
        if (this.users.length === 0) {
            this.showEmptyState();
            return;
        }
        
        container.innerHTML = this.users.map(user => `
            <div class="col-md-6 col-lg-4">
                <div class="user-card">
                    <div class="user-card-header">
                        ${this.renderUserAvatar(user, 'card')}
                        <div class="user-card-info">
                            <h6>${this.escapeHtml(user.full_name || user.username)}</h6>
                            <p>${this.escapeHtml(user.email)}</p>
                        </div>
                    </div>
                    
                    <div class="user-card-meta">
                        <div class="user-card-meta-item">
                            <i class="fas fa-user-tag"></i>
                            <span class="user-role ${user.role}">${this.getRoleText(user.role)}</span>
                        </div>
                        <div class="user-card-meta-item">
                            <i class="fas fa-building"></i>
                            <span>${user.department ? this.escapeHtml(user.department.name) : '未分配部门'}</span>
                        </div>
                        <div class="user-card-meta-item">
                            <i class="fas fa-circle"></i>
                            <span class="user-status ${user.status}">${this.getStatusText(user.status)}</span>
                        </div>
                        <div class="user-card-meta-item">
                            <i class="fas fa-clock"></i>
                            <span>${user.last_login_at ? this.formatDate(user.last_login_at) : '从未登录'}</span>
                        </div>
                    </div>
                    
                    <div class="user-card-actions">
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewUser(${user.id})">
                            <i class="fas fa-eye me-1"></i>查看
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-warning" onclick="editUser(${user.id})">
                            <i class="fas fa-edit me-1"></i>编辑
                        </button>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" 
                                    data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-h"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="resetUserPassword(${user.id})">
                                    <i class="fas fa-key me-2"></i>重置密码
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="toggleUserStatus(${user.id})">
                                    <i class="fas fa-toggle-${user.status === 'active' ? 'off' : 'on'} me-2"></i>
                                    ${user.status === 'active' ? '停用' : '启用'}
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteUser(${user.id})">
                                    <i class="fas fa-trash me-2"></i>删除
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
        
        this.hideEmptyState();
    }

    /**
     * 渲染用户头像
     */
    renderUserAvatar(user, type = 'list') {
        const sizeClass = type === 'card' ? 'user-card-avatar' : 'user-avatar';
        const placeholderClass = type === 'card' ? 'user-card-avatar-placeholder' : 'user-avatar-placeholder';
        
        if (user.avatar) {
            return `<img src="${user.avatar}" alt="${this.escapeHtml(user.full_name || user.username)}" class="${sizeClass}">`;
        } else {
            const initials = this.getInitials(user.full_name || user.username);
            return `<div class="${placeholderClass}">${initials}</div>`;
        }
    }

    /**
     * 获取用户姓名首字母
     */
    getInitials(name) {
        if (!name) return '?';
        return name.split(' ').map(word => word.charAt(0)).join('').toUpperCase().slice(0, 2);
    }

    /**
     * 处理搜索
     */
    handleSearch() {
        const searchInput = document.getElementById('searchInput');
        const query = searchInput?.value.trim();
        
        if (query) {
            this.currentFilters.search = query;
        } else {
            delete this.currentFilters.search;
        }
        
        this.currentPage = 1;
        this.loadUsers();
    }

    /**
     * 处理筛选器变化
     */
    handleFilterChange() {
        const statusFilter = document.getElementById('statusFilter')?.value;
        const roleFilter = document.getElementById('roleFilter')?.value;
        const departmentFilter = document.getElementById('departmentFilter')?.value;
        
        // 更新筛选条件
        this.currentFilters = {};
        
        if (statusFilter) this.currentFilters.status = statusFilter;
        if (roleFilter) this.currentFilters.role = roleFilter;
        if (departmentFilter) this.currentFilters.department_id = departmentFilter;
        
        // 保持搜索条件
        const searchInput = document.getElementById('searchInput');
        const query = searchInput?.value.trim();
        if (query) this.currentFilters.search = query;
        
        this.currentPage = 1;
        this.loadUsers();
    }

    /**
     * 重置筛选器
     */
    resetFilters() {
        // 重置表单
        document.getElementById('searchInput').value = '';
        document.getElementById('statusFilter').value = '';
        document.getElementById('roleFilter').value = '';
        document.getElementById('departmentFilter').value = '';
        document.getElementById('showInactiveUsers').checked = false;
        
        // 清空筛选条件
        this.currentFilters = {};
        this.currentPage = 1;
        
        this.loadUsers();
    }

    /**
     * 处理用户选择
     */
    handleUserSelect(userId, checked) {
        if (checked) {
            this.selectedUsers.add(userId);
        } else {
            this.selectedUsers.delete(userId);
        }
        
        // 更新全选状态
        const selectAllCheckbox = document.getElementById('selectAll');
        if (selectAllCheckbox) {
            const totalCheckboxes = document.querySelectorAll('.user-checkbox').length;
            selectAllCheckbox.checked = this.selectedUsers.size === totalCheckboxes;
            selectAllCheckbox.indeterminate = this.selectedUsers.size > 0 && this.selectedUsers.size < totalCheckboxes;
        }
    }

    /**
     * 处理全选
     */
    handleSelectAll(checked) {
        const checkboxes = document.querySelectorAll('.user-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = checked;
            const userId = parseInt(checkbox.value);
            if (checked) {
                this.selectedUsers.add(userId);
            } else {
                this.selectedUsers.delete(userId);
            }
        });
    }

    /**
     * 处理创建用户
     */
    async handleCreateUser() {
        try {
            const form = document.getElementById('createUserForm');
            const formData = new FormData(form);
            
            // 验证密码
            const password = formData.get('password');
            const confirmPassword = formData.get('confirm_password');
            
            if (password !== confirmPassword) {
                this.showMessage('两次输入的密码不一致', 'error');
                return;
            }
            
            // 构建用户数据
            const userData = {
                username: formData.get('username'),
                email: formData.get('email'),
                full_name: formData.get('full_name'),
                phone: formData.get('phone'),
                role: formData.get('role'),
                department_id: formData.get('department_id') || null,
                password: password,
                notes: formData.get('notes'),
                send_welcome_email: formData.has('send_welcome_email')
            };
            
            // 显示加载状态
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>创建中...';
            submitBtn.disabled = true;
            
            // 创建用户
            await this.userAPI.createUser(userData);
            
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('createUserModal'));
            modal.hide();
            
            // 重置表单
            form.reset();
            
            // 刷新列表
            await this.loadUsers();
            await this.loadStatistics();
            
            this.showMessage('用户创建成功', 'success');
            
        } catch (error) {
            console.error('创建用户失败:', error);
            this.showMessage('创建用户失败: ' + error.message, 'error');
        } finally {
            // 恢复按钮状态
            const submitBtn = document.querySelector('#createUserForm button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-save me-2"></i>创建用户';
                submitBtn.disabled = false;
            }
        }
    }

    /**
     * 查看用户详情
     */
    async viewUser(userId) {
        try {
            const user = await this.userAPI.getUser(userId);
            this.showUserDetail(user);
        } catch (error) {
            console.error('获取用户详情失败:', error);
            this.showMessage('获取用户详情失败: ' + error.message, 'error');
        }
    }

    /**
     * 显示用户详情
     */
    showUserDetail(user) {
        const modal = new bootstrap.Modal(document.getElementById('userDetailModal'));
        const content = document.getElementById('userDetailContent');
        
        content.innerHTML = `
            <div class="user-detail-header">
                ${this.renderUserAvatar(user, 'detail')}
                <div class="user-detail-info">
                    <h4>${this.escapeHtml(user.full_name || user.username)}</h4>
                    <p>${this.escapeHtml(user.email)}</p>
                    <div class="d-flex gap-2">
                        <span class="user-role ${user.role}">${this.getRoleText(user.role)}</span>
                        <span class="user-status ${user.status}">${this.getStatusText(user.status)}</span>
                    </div>
                </div>
            </div>
            
            <ul class="nav nav-tabs user-detail-tabs" role="tablist">
                <li class="nav-item">
                    <a class="nav-link active" data-bs-toggle="tab" href="#userInfo">基本信息</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#userActivity">活动记录</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#userPermissions">权限设置</a>
                </li>
            </ul>
            
            <div class="tab-content user-detail-content">
                <div class="tab-pane fade show active" id="userInfo">
                    ${this.renderUserInfo(user)}
                </div>
                <div class="tab-pane fade" id="userActivity">
                    ${this.renderUserActivity(user)}
                </div>
                <div class="tab-pane fade" id="userPermissions">
                    ${this.renderUserPermissions(user)}
                </div>
            </div>
        `;
        
        modal.show();
    }

    /**
     * 渲染用户基本信息
     */
    renderUserInfo(user) {
        return `
            <div class="info-item">
                <span class="info-label">用户名</span>
                <span class="info-value">${this.escapeHtml(user.username)}</span>
            </div>
            <div class="info-item">
                <span class="info-label">姓名</span>
                <span class="info-value">${this.escapeHtml(user.full_name || '-')}</span>
            </div>
            <div class="info-item">
                <span class="info-label">邮箱</span>
                <span class="info-value">${this.escapeHtml(user.email)}</span>
            </div>
            <div class="info-item">
                <span class="info-label">手机号</span>
                <span class="info-value">${this.escapeHtml(user.phone || '-')}</span>
            </div>
            <div class="info-item">
                <span class="info-label">部门</span>
                <span class="info-value">${user.department ? this.escapeHtml(user.department.name) : '-'}</span>
            </div>
            <div class="info-item">
                <span class="info-label">角色</span>
                <span class="info-value">
                    <span class="user-role ${user.role}">${this.getRoleText(user.role)}</span>
                </span>
            </div>
            <div class="info-item">
                <span class="info-label">状态</span>
                <span class="info-value">
                    <span class="user-status ${user.status}">${this.getStatusText(user.status)}</span>
                </span>
            </div>
            <div class="info-item">
                <span class="info-label">创建时间</span>
                <span class="info-value">${this.formatDate(user.created_at)}</span>
            </div>
            <div class="info-item">
                <span class="info-label">最后登录</span>
                <span class="info-value">${user.last_login_at ? this.formatDate(user.last_login_at) : '从未登录'}</span>
            </div>
            <div class="info-item">
                <span class="info-label">备注</span>
                <span class="info-value">${this.escapeHtml(user.notes || '-')}</span>
            </div>
        `;
    }

    /**
     * 渲染用户活动记录
     */
    renderUserActivity(user) {
        // 这里应该从API获取用户活动记录
        // 暂时显示示例数据
        const activities = [
            { type: 'login', description: '用户登录系统', time: '2024-01-15 09:30:00' },
            { type: 'update', description: '更新个人资料', time: '2024-01-14 16:45:00' },
            { type: 'logout', description: '用户退出系统', time: '2024-01-14 18:00:00' }
        ];
        
        return activities.map(activity => `
            <div class="activity-item">
                <div class="activity-icon ${activity.type}">
                    <i class="fas fa-${this.getActivityIcon(activity.type)}"></i>
                </div>
                <div class="activity-content">
                    <h6>${activity.description}</h6>
                    <p>${this.formatDate(activity.time)}</p>
                </div>
            </div>
        `).join('');
    }

    /**
     * 渲染用户权限
     */
    renderUserPermissions(user) {
        // 这里应该从API获取用户权限
        // 暂时显示示例数据
        const permissions = {
            '项目管理': {
                'project.view': { name: '查看项目', granted: true },
                'project.create': { name: '创建项目', granted: user.role === 'admin' || user.role === 'manager' },
                'project.edit': { name: '编辑项目', granted: user.role === 'admin' || user.role === 'manager' },
                'project.delete': { name: '删除项目', granted: user.role === 'admin' }
            },
            '任务管理': {
                'task.view': { name: '查看任务', granted: true },
                'task.create': { name: '创建任务', granted: user.role !== 'viewer' },
                'task.edit': { name: '编辑任务', granted: user.role !== 'viewer' },
                'task.delete': { name: '删除任务', granted: user.role === 'admin' || user.role === 'manager' }
            },
            '用户管理': {
                'user.view': { name: '查看用户', granted: user.role === 'admin' || user.role === 'manager' },
                'user.create': { name: '创建用户', granted: user.role === 'admin' },
                'user.edit': { name: '编辑用户', granted: user.role === 'admin' },
                'user.delete': { name: '删除用户', granted: user.role === 'admin' }
            }
        };
        
        return Object.entries(permissions).map(([group, perms]) => `
            <div class="permission-group">
                <h6>${group}</h6>
                ${Object.entries(perms).map(([key, perm]) => `
                    <div class="permission-item">
                        <span class="permission-name">${perm.name}</span>
                        <span class="permission-status ${perm.granted ? 'granted' : 'denied'}">
                            ${perm.granted ? '已授权' : '未授权'}
                        </span>
                    </div>
                `).join('')}
            </div>
        `).join('');
    }

    /**
     * 获取活动图标
     */
    getActivityIcon(type) {
        const icons = {
            login: 'sign-in-alt',
            logout: 'sign-out-alt',
            update: 'edit',
            create: 'plus',
            delete: 'trash'
        };
        return icons[type] || 'circle';
    }

    /**
     * 编辑用户
     */
    editUser(userId) {
        // 这里应该打开编辑用户的模态框
        // 暂时显示提示
        this.showMessage('编辑用户功能开发中...', 'info');
    }

    /**
     * 删除用户
     */
    async deleteUser(userId) {
        if (!confirm('确定要删除这个用户吗？此操作不可撤销。')) {
            return;
        }
        
        try {
            await this.userAPI.deleteUser(userId);
            await this.loadUsers();
            await this.loadStatistics();
            this.showMessage('用户删除成功', 'success');
        } catch (error) {
            console.error('删除用户失败:', error);
            this.showMessage('删除用户失败: ' + error.message, 'error');
        }
    }

    /**
     * 重置用户密码
     */
    async resetUserPassword(userId) {
        if (!confirm('确定要重置这个用户的密码吗？')) {
            return;
        }
        
        try {
            await this.userAPI.resetUserPassword(userId);
            this.showMessage('密码重置成功，新密码已发送到用户邮箱', 'success');
        } catch (error) {
            console.error('重置密码失败:', error);
            this.showMessage('重置密码失败: ' + error.message, 'error');
        }
    }

    /**
     * 切换用户状态
     */
    async toggleUserStatus(userId) {
        try {
            await this.userAPI.toggleUserStatus(userId);
            await this.loadUsers();
            await this.loadStatistics();
            this.showMessage('用户状态更新成功', 'success');
        } catch (error) {
            console.error('更新用户状态失败:', error);
            this.showMessage('更新用户状态失败: ' + error.message, 'error');
        }
    }

    /**
     * 导出用户
     */
    async exportUsers() {
        try {
            const blob = await this.userAPI.exportUsers(this.currentFilters);
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `users_${new Date().toISOString().split('T')[0]}.xlsx`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
            
            this.showMessage('用户数据导出成功', 'success');
        } catch (error) {
            console.error('导出用户失败:', error);
            this.showMessage('导出用户失败: ' + error.message, 'error');
        }
    }

    /**
     * 渲染分页
     */
    renderPagination() {
        const pagination = document.getElementById('pagination');
        if (!pagination) return;
        
        if (this.totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }
        
        let html = '';
        
        // 上一页
        html += `
            <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="userManager.goToPage(${this.currentPage - 1})">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
        `;
        
        // 页码
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(this.totalPages, this.currentPage + 2);
        
        if (startPage > 1) {
            html += `<li class="page-item"><a class="page-link" href="#" onclick="userManager.goToPage(1)">1</a></li>`;
            if (startPage > 2) {
                html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }
        
        for (let i = startPage; i <= endPage; i++) {
            html += `
                <li class="page-item ${i === this.currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="userManager.goToPage(${i})">${i}</a>
                </li>
            `;
        }
        
        if (endPage < this.totalPages) {
            if (endPage < this.totalPages - 1) {
                html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
            html += `<li class="page-item"><a class="page-link" href="#" onclick="userManager.goToPage(${this.totalPages})">${this.totalPages}</a></li>`;
        }
        
        // 下一页
        html += `
            <li class="page-item ${this.currentPage === this.totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="userManager.goToPage(${this.currentPage + 1})">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
        `;
        
        pagination.innerHTML = html;
    }

    /**
     * 跳转到指定页面
     */
    goToPage(page) {
        if (page < 1 || page > this.totalPages || page === this.currentPage) {
            return;
        }
        
        this.currentPage = page;
        this.loadUsers();
    }

    /**
     * 更新页面信息
     */
    updatePageInfo() {
        const start = (this.currentPage - 1) * this.pageSize + 1;
        const end = Math.min(this.currentPage * this.pageSize, this.totalCount);
        
        document.getElementById('pageStart').textContent = this.totalCount > 0 ? start : 0;
        document.getElementById('pageEnd').textContent = end;
        document.getElementById('totalCount').textContent = this.totalCount;
    }

    /**
     * 显示/隐藏加载状态
     */
    showLoading(show) {
        const loadingState = document.getElementById('loadingState');
        const listContainer = document.getElementById('listViewContainer');
        const cardContainer = document.getElementById('cardViewContainer');
        
        if (show) {
            loadingState.style.display = 'block';
            listContainer.style.display = 'none';
            cardContainer.style.display = 'none';
            this.hideEmptyState();
        } else {
            loadingState.style.display = 'none';
        }
    }

    /**
     * 显示空状态
     */
    showEmptyState() {
        const emptyState = document.getElementById('emptyState');
        const listContainer = document.getElementById('listViewContainer');
        const cardContainer = document.getElementById('cardViewContainer');
        
        emptyState.style.display = 'block';
        listContainer.style.display = 'none';
        cardContainer.style.display = 'none';
    }

    /**
     * 隐藏空状态
     */
    hideEmptyState() {
        const emptyState = document.getElementById('emptyState');
        emptyState.style.display = 'none';
    }

    /**
     * 数字动画效果
     */
    animateNumbers() {
        const counters = document.querySelectorAll('.stat-card h5');
        counters.forEach(counter => {
            const target = parseInt(counter.textContent);
            const increment = target / 20;
            let current = 0;
            
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    counter.textContent = target;
                    clearInterval(timer);
                } else {
                    counter.textContent = Math.floor(current);
                }
            }, 50);
        });
    }

    /**
     * 获取状态文本
     */
    getStatusText(status) {
        const statusMap = {
            active: '活跃',
            inactive: '非活跃',
            pending: '待审核',
            suspended: '已暂停'
        };
        return statusMap[status] || status;
    }

    /**
     * 获取角色文本
     */
    getRoleText(role) {
        const roleMap = {
            admin: '管理员',
            manager: '项目经理',
            member: '成员',
            viewer: '查看者'
        };
        return roleMap[role] || role;
    }

    /**
     * 格式化日期
     */
    formatDate(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    /**
     * HTML转义
     */
    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 显示消息
     */
    showMessage(message, type = 'info') {
        // 使用全局的showToast函数
        if (typeof showToast === 'function') {
            showToast(message, type);
        } else {
            alert(message);
        }
    }
}

// 用户API类占位符
class UserAPI {
    constructor() {
        this.baseURL = APP_CONFIG.API_BASE_URL;
    }

    async getUsers(params = {}) {
        // 模拟API调用
        return {
            data: [
                {
                    id: 1,
                    username: 'admin',
                    email: '<EMAIL>',
                    full_name: '系统管理员',
                    phone: '13800138000',
                    role: 'admin',
                    status: 'active',
                    department: { id: 1, name: '技术部' },
                    avatar: null,
                    last_login_at: '2024-01-15T09:30:00Z',
                    created_at: '2024-01-01T00:00:00Z',
                    notes: '系统管理员账户'
                },
                {
                    id: 2,
                    username: 'manager',
                    email: '<EMAIL>',
                    full_name: '项目经理',
                    phone: '13800138001',
                    role: 'manager',
                    status: 'active',
                    department: { id: 2, name: '产品部' },
                    avatar: null,
                    last_login_at: '2024-01-14T16:45:00Z',
                    created_at: '2024-01-02T00:00:00Z',
                    notes: '项目经理账户'
                }
            ],
            total: 2,
            page: 1,
            page_size: 20
        };
    }

    async getUser(userId) {
        // 模拟API调用
        return {
            id: userId,
            username: 'admin',
            email: '<EMAIL>',
            full_name: '系统管理员',
            phone: '13800138000',
            role: 'admin',
            status: 'active',
            department: { id: 1, name: '技术部' },
            avatar: null,
            last_login_at: '2024-01-15T09:30:00Z',
            created_at: '2024-01-01T00:00:00Z',
            notes: '系统管理员账户'
        };
    }

    async getUserStatistics() {
        // 模拟API调用
        return {
            total_users: 25,
            active_users: 20,
            pending_users: 3,
            new_users_today: 2
        };
    }

    async getDepartments() {
        // 模拟API调用
        return [
            { id: 1, name: '技术部' },
            { id: 2, name: '产品部' },
            { id: 3, name: '运营部' },
            { id: 4, name: '市场部' }
        ];
    }

    async createUser(userData) {
        // 模拟API调用
        console.log('创建用户:', userData);
        return { id: Date.now(), ...userData };
    }

    async deleteUser(userId) {
        // 模拟API调用
        console.log('删除用户:', userId);
        return { success: true };
    }

    async resetUserPassword(userId) {
        // 模拟API调用
        console.log('重置密码:', userId);
        return { success: true };
    }

    async toggleUserStatus(userId) {
        // 模拟API调用
        console.log('切换用户状态:', userId);
        return { success: true };
    }

    async exportUsers(filters) {
        // 模拟API调用
        console.log('导出用户:', filters);
        return new Blob(['用户数据'], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    }
}

// 全局变量
let userManager;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    userManager = new UserManager();
});