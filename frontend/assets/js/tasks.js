/**
 * 任务管理页面逻辑
 */

class TaskManager {
    constructor() {
        this.currentPage = 1;
        this.pageSize = 10;
        this.totalCount = 0;
        this.currentView = 'list';
        this.filters = {
            project_id: '',
            status: '',
            priority: '',
            assignee_id: '',
            search: ''
        };
        this.sortBy = 'created_at';
        this.sortOrder = 'desc';
        this.selectedTasks = new Set();
        this.taskAPI = new TaskAPI();
        this.projectAPI = new ProjectAPI();
        this.authAPI = new AuthAPI();
        
        this.init();
    }

    async init() {
        try {
            // 检查认证状态
            if (!await this.authAPI.checkAuth()) {
                window.location.href = 'login.html';
                return;
            }

            // 设置事件监听器
            this.setupEventListeners();
            
            // 加载初始数据
            await this.loadProjects();
            await this.loadUsers();
            await this.loadTasks();
            await this.loadStatistics();
            
        } catch (error) {
            console.error('初始化失败:', error);
            this.showMessage('初始化失败，请刷新页面重试', 'error');
        }
    }

    setupEventListeners() {
        // 视图切换
        document.querySelectorAll('input[name="viewMode"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                this.currentView = e.target.value;
                this.switchView();
            });
        });

        // 筛选器
        document.getElementById('projectFilter').addEventListener('change', (e) => {
            this.filters.project_id = e.target.value;
            this.currentPage = 1;
            this.loadTasks();
        });

        document.getElementById('statusFilter').addEventListener('change', (e) => {
            this.filters.status = e.target.value;
            this.currentPage = 1;
            this.loadTasks();
        });

        document.getElementById('priorityFilter').addEventListener('change', (e) => {
            this.filters.priority = e.target.value;
            this.currentPage = 1;
            this.loadTasks();
        });

        document.getElementById('assigneeFilter').addEventListener('change', (e) => {
            this.filters.assignee_id = e.target.value;
            this.currentPage = 1;
            this.loadTasks();
        });

        // 搜索
        const searchInput = document.getElementById('searchInput');
        searchInput.addEventListener('input', this.debounce((e) => {
            this.filters.search = e.target.value;
            this.currentPage = 1;
            this.loadTasks();
        }, 500));

        // 排序
        document.getElementById('sortBy').addEventListener('change', (e) => {
            this.sortBy = e.target.value;
            this.loadTasks();
        });

        document.getElementById('sortOrder').addEventListener('change', (e) => {
            this.sortOrder = e.target.value;
            this.loadTasks();
        });

        // 全选
        document.getElementById('selectAll').addEventListener('change', (e) => {
            this.toggleSelectAll(e.target.checked);
        });

        // 创建任务表单
        document.getElementById('createTaskForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.createTask();
        });

        // 批量操作
        document.getElementById('batchAction').addEventListener('change', (e) => {
            this.showBatchActionOptions(e.target.value);
        });

        document.getElementById('executeBatchAction').addEventListener('click', () => {
            this.executeBatchAction();
        });
    }

    async loadProjects() {
        try {
            const response = await this.projectAPI.getProjects({ page: 1, size: 100 });
            const projects = response.data || [];
            
            // 填充项目筛选器
            const projectFilter = document.getElementById('projectFilter');
            const createProjectSelect = document.querySelector('#createTaskForm select[name="project_id"]');
            
            projects.forEach(project => {
                const option = new Option(project.name, project.id);
                projectFilter.appendChild(option.cloneNode(true));
                createProjectSelect.appendChild(option);
            });
            
        } catch (error) {
            console.error('加载项目列表失败:', error);
        }
    }

    async loadUsers() {
        try {
            // 这里应该调用用户API获取用户列表
            // 暂时使用模拟数据
            const users = [
                { id: 1, name: '张三' },
                { id: 2, name: '李四' },
                { id: 3, name: '王五' }
            ];
            
            const assigneeFilter = document.getElementById('assigneeFilter');
            const createAssigneeSelect = document.querySelector('#createTaskForm select[name="assignee_id"]');
            
            users.forEach(user => {
                const option = new Option(user.name, user.id);
                assigneeFilter.appendChild(option.cloneNode(true));
                createAssigneeSelect.appendChild(option);
            });
            
        } catch (error) {
            console.error('加载用户列表失败:', error);
        }
    }

    async loadTasks() {
        try {
            this.showLoading(true);
            
            const params = {
                page: this.currentPage,
                size: this.pageSize,
                sort_by: this.sortBy,
                sort_order: this.sortOrder,
                ...this.filters
            };
            
            // 移除空值
            Object.keys(params).forEach(key => {
                if (params[key] === '' || params[key] === null || params[key] === undefined) {
                    delete params[key];
                }
            });
            
            const response = await this.taskAPI.getTasks(params);
            const tasks = response.data || [];
            this.totalCount = response.total || 0;
            
            if (this.currentView === 'list') {
                this.renderTaskList(tasks);
            } else {
                this.renderKanbanView(tasks);
            }
            
            this.renderPagination();
            
        } catch (error) {
            console.error('加载任务列表失败:', error);
            this.showMessage('加载任务列表失败', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    async loadStatistics() {
        try {
            const stats = await this.taskAPI.getTaskStatistics();
            
            document.getElementById('totalTasks').textContent = stats.total || 0;
            document.getElementById('pendingTasks').textContent = stats.todo || 0;
            document.getElementById('inProgressTasks').textContent = stats.in_progress || 0;
            document.getElementById('completedTasks').textContent = stats.completed || 0;
            
        } catch (error) {
            console.error('加载统计数据失败:', error);
        }
    }

    renderTaskList(tasks) {
        const tbody = document.getElementById('taskTableBody');
        
        if (tasks.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center py-4">
                        <div class="empty-state">
                            <i class="fas fa-tasks empty-state-icon"></i>
                            <h5 class="empty-state-title">暂无任务</h5>
                            <p class="empty-state-description">还没有任务，点击"创建任务"开始吧</p>
                        </div>
                    </td>
                </tr>
            `;
            return;
        }
        
        tbody.innerHTML = tasks.map(task => `
            <tr>
                <td>
                    <input type="checkbox" class="form-check-input task-checkbox" 
                           value="${task.id}" ${this.selectedTasks.has(task.id) ? 'checked' : ''}>
                </td>
                <td>
                    <div class="d-flex flex-column">
                        <span class="fw-semibold text-truncate" style="max-width: 200px;" 
                              title="${this.escapeHtml(task.title)}">
                            ${this.escapeHtml(task.title)}
                        </span>
                        ${task.description ? `
                            <small class="text-muted text-truncate" style="max-width: 200px;" 
                                   title="${this.escapeHtml(task.description)}">
                                ${this.escapeHtml(task.description)}
                            </small>
                        ` : ''}
                        ${task.tags ? `
                            <div class="task-tags mt-1">
                                ${task.tags.split(',').map(tag => `
                                    <span class="task-tag">${this.escapeHtml(tag.trim())}</span>
                                `).join('')}
                            </div>
                        ` : ''}
                    </div>
                </td>
                <td>
                    <span class="text-primary fw-semibold">${this.escapeHtml(task.project_name || '')}</span>
                </td>
                <td>
                    ${task.assignee_name ? `
                        <div class="d-flex align-items-center">
                            <div class="user-avatar size-sm me-2">
                                ${task.assignee_name.charAt(0)}
                            </div>
                            <span class="text-truncate" style="max-width: 80px;" 
                                  title="${this.escapeHtml(task.assignee_name)}">
                                ${this.escapeHtml(task.assignee_name)}
                            </span>
                        </div>
                    ` : '<span class="text-muted">未分配</span>'}
                </td>
                <td>
                    <span class="task-status ${this.getStatusClass(task.status)}">
                        ${this.getStatusText(task.status)}
                    </span>
                </td>
                <td>
                    <span class="task-priority ${this.getPriorityClass(task.priority)}">
                        <i class="${this.getPriorityIcon(task.priority)}"></i>
                        ${this.getPriorityText(task.priority)}
                    </span>
                </td>
                <td>
                    ${task.due_date ? `
                        <span class="${this.getDueDateClass(task.due_date)}" 
                              title="${this.formatDateTime(task.due_date)}">
                            ${this.formatDate(task.due_date)}
                        </span>
                    ` : '<span class="text-muted">无</span>'}
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn btn-view" onclick="viewTask(${task.id})" 
                                title="查看详情">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn btn-edit" onclick="editTask(${task.id})" 
                                title="编辑任务">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn btn-delete" onclick="deleteTask(${task.id})" 
                                title="删除任务">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
        
        // 重新绑定复选框事件
        this.bindCheckboxEvents();
    }

    renderKanbanView(tasks) {
        const columns = {
            TODO: document.getElementById('todoTasks'),
            IN_PROGRESS: document.getElementById('inProgressTasks'),
            REVIEW: document.getElementById('reviewTasks'),
            COMPLETED: document.getElementById('completedTasks')
        };
        
        // 清空所有列
        Object.values(columns).forEach(column => {
            column.innerHTML = '';
        });
        
        // 统计各状态任务数量
        const counts = { TODO: 0, IN_PROGRESS: 0, REVIEW: 0, COMPLETED: 0 };
        
        tasks.forEach(task => {
            const status = task.status || 'TODO';
            counts[status]++;
            
            if (columns[status]) {
                const taskCard = this.createKanbanTaskCard(task);
                columns[status].appendChild(taskCard);
            }
        });
        
        // 更新计数
        document.getElementById('todoCount').textContent = counts.TODO;
        document.getElementById('inProgressCount').textContent = counts.IN_PROGRESS;
        document.getElementById('reviewCount').textContent = counts.REVIEW;
        document.getElementById('completedCount').textContent = counts.COMPLETED;
        
        // 如果某列为空，显示空状态
        Object.entries(columns).forEach(([status, column]) => {
            if (counts[status] === 0) {
                column.innerHTML = `
                    <div class="text-center text-muted py-3">
                        <i class="fas fa-inbox fa-2x mb-2"></i>
                        <p class="mb-0">暂无任务</p>
                    </div>
                `;
            }
        });
    }

    createKanbanTaskCard(task) {
        const card = document.createElement('div');
        card.className = `kanban-task-card priority-${task.priority?.toLowerCase() || 'medium'}`;
        card.draggable = true;
        card.dataset.taskId = task.id;
        
        card.innerHTML = `
            <div class="kanban-task-title">${this.escapeHtml(task.title)}</div>
            <div class="kanban-task-project">${this.escapeHtml(task.project_name || '')}</div>
            ${task.description ? `
                <div class="text-muted text-truncate-2" style="font-size: 0.8rem;">
                    ${this.escapeHtml(task.description)}
                </div>
            ` : ''}
            ${task.tags ? `
                <div class="task-tags">
                    ${task.tags.split(',').map(tag => `
                        <span class="task-tag">${this.escapeHtml(tag.trim())}</span>
                    `).join('')}
                </div>
            ` : ''}
            <div class="kanban-task-meta">
                <div class="kanban-task-assignee">
                    ${task.assignee_name ? `
                        <div class="user-avatar size-sm">
                            ${task.assignee_name.charAt(0)}
                        </div>
                        <span>${this.escapeHtml(task.assignee_name)}</span>
                    ` : '<span class="text-muted">未分配</span>'}
                </div>
                <div class="kanban-task-due">
                    ${task.due_date ? `
                        <i class="fas fa-clock"></i>
                        <span class="${this.getDueDateClass(task.due_date)}">
                            ${this.formatDate(task.due_date)}
                        </span>
                    ` : ''}
                </div>
            </div>
        `;
        
        // 添加点击事件
        card.addEventListener('click', () => {
            viewTask(task.id);
        });
        
        // 添加拖拽事件
        this.addDragEvents(card);
        
        return card;
    }

    addDragEvents(card) {
        card.addEventListener('dragstart', (e) => {
            e.dataTransfer.setData('text/plain', card.dataset.taskId);
            card.classList.add('dragging');
        });
        
        card.addEventListener('dragend', () => {
            card.classList.remove('dragging');
        });
    }

    switchView() {
        const listContainer = document.getElementById('listViewContainer');
        const kanbanContainer = document.getElementById('kanbanViewContainer');
        
        if (this.currentView === 'list') {
            listContainer.style.display = 'block';
            kanbanContainer.style.display = 'none';
        } else {
            listContainer.style.display = 'none';
            kanbanContainer.style.display = 'block';
        }
        
        this.loadTasks();
    }

    bindCheckboxEvents() {
        document.querySelectorAll('.task-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                const taskId = parseInt(e.target.value);
                if (e.target.checked) {
                    this.selectedTasks.add(taskId);
                } else {
                    this.selectedTasks.delete(taskId);
                }
                this.updateBatchActions();
            });
        });
    }

    toggleSelectAll(checked) {
        document.querySelectorAll('.task-checkbox').forEach(checkbox => {
            checkbox.checked = checked;
            const taskId = parseInt(checkbox.value);
            if (checked) {
                this.selectedTasks.add(taskId);
            } else {
                this.selectedTasks.delete(taskId);
            }
        });
        this.updateBatchActions();
    }

    updateBatchActions() {
        const batchActions = document.querySelector('.batch-actions');
        const selectedCount = this.selectedTasks.size;
        
        if (selectedCount > 0) {
            if (!batchActions) {
                this.createBatchActionsBar();
            } else {
                batchActions.classList.add('show');
                batchActions.querySelector('.batch-actions-info').textContent = 
                    `已选择 ${selectedCount} 个任务`;
            }
        } else if (batchActions) {
            batchActions.classList.remove('show');
        }
    }

    createBatchActionsBar() {
        const container = document.querySelector('.main-content .container-fluid');
        const batchActions = document.createElement('div');
        batchActions.className = 'batch-actions show';
        batchActions.innerHTML = `
            <div class="batch-actions-info">已选择 ${this.selectedTasks.size} 个任务</div>
            <div class="batch-actions-buttons">
                <button class="btn btn-sm btn-outline-light" data-bs-toggle="modal" data-bs-target="#batchActionModal">
                    <i class="fas fa-cogs me-1"></i>批量操作
                </button>
                <button class="btn btn-sm btn-outline-light" onclick="taskManager.clearSelection()">
                    <i class="fas fa-times me-1"></i>取消选择
                </button>
            </div>
        `;
        
        // 插入到统计卡片后面
        const statsRow = document.getElementById('taskStats');
        statsRow.parentNode.insertBefore(batchActions, statsRow.nextSibling);
    }

    clearSelection() {
        this.selectedTasks.clear();
        document.querySelectorAll('.task-checkbox').forEach(checkbox => {
            checkbox.checked = false;
        });
        document.getElementById('selectAll').checked = false;
        this.updateBatchActions();
    }

    async createTask() {
        try {
            const form = document.getElementById('createTaskForm');
            const formData = new FormData(form);
            const taskData = Object.fromEntries(formData.entries());
            
            // 处理标签
            if (taskData.tags) {
                taskData.tags = taskData.tags.split(',').map(tag => tag.trim()).filter(tag => tag);
            }
            
            // 处理数值字段
            if (taskData.estimated_hours) {
                taskData.estimated_hours = parseFloat(taskData.estimated_hours);
            }
            
            this.showLoading(true);
            await this.taskAPI.createTask(taskData);
            
            this.showMessage('任务创建成功', 'success');
            
            // 关闭模态框并重置表单
            const modal = bootstrap.Modal.getInstance(document.getElementById('createTaskModal'));
            modal.hide();
            form.reset();
            
            // 刷新任务列表
            await this.loadTasks();
            await this.loadStatistics();
            
        } catch (error) {
            console.error('创建任务失败:', error);
            this.showMessage('创建任务失败', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    showBatchActionOptions(action) {
        const optionsContainer = document.getElementById('batchActionOptions');
        
        if (!action) {
            optionsContainer.style.display = 'none';
            return;
        }
        
        let optionsHtml = '';
        
        switch (action) {
            case 'status':
                optionsHtml = `
                    <label class="form-label">新状态</label>
                    <select class="form-select" id="batchStatusValue">
                        <option value="TODO">待办</option>
                        <option value="IN_PROGRESS">进行中</option>
                        <option value="REVIEW">待审核</option>
                        <option value="COMPLETED">已完成</option>
                        <option value="CANCELLED">已取消</option>
                    </select>
                `;
                break;
            case 'priority':
                optionsHtml = `
                    <label class="form-label">新优先级</label>
                    <select class="form-select" id="batchPriorityValue">
                        <option value="HIGH">高</option>
                        <option value="MEDIUM">中</option>
                        <option value="LOW">低</option>
                    </select>
                `;
                break;
            case 'assignee':
                optionsHtml = `
                    <label class="form-label">新负责人</label>
                    <select class="form-select" id="batchAssigneeValue">
                        <option value="">未分配</option>
                        <!-- 这里应该动态加载用户列表 -->
                    </select>
                `;
                break;
            case 'delete':
                optionsHtml = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        确定要删除选中的 ${this.selectedTasks.size} 个任务吗？此操作不可撤销。
                    </div>
                `;
                break;
        }
        
        optionsContainer.innerHTML = optionsHtml;
        optionsContainer.style.display = 'block';
    }

    async executeBatchAction() {
        const action = document.getElementById('batchAction').value;
        const taskIds = Array.from(this.selectedTasks);
        
        if (!action || taskIds.length === 0) {
            this.showMessage('请选择操作和任务', 'warning');
            return;
        }
        
        try {
            this.showLoading(true);
            
            switch (action) {
                case 'status':
                    const status = document.getElementById('batchStatusValue').value;
                    await this.taskAPI.batchUpdateTasks(taskIds, { status });
                    break;
                case 'priority':
                    const priority = document.getElementById('batchPriorityValue').value;
                    await this.taskAPI.batchUpdateTasks(taskIds, { priority });
                    break;
                case 'assignee':
                    const assignee_id = document.getElementById('batchAssigneeValue').value;
                    await this.taskAPI.batchUpdateTasks(taskIds, { assignee_id });
                    break;
                case 'delete':
                    await this.taskAPI.batchDeleteTasks(taskIds);
                    break;
            }
            
            this.showMessage('批量操作执行成功', 'success');
            
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('batchActionModal'));
            modal.hide();
            
            // 清除选择并刷新列表
            this.clearSelection();
            await this.loadTasks();
            await this.loadStatistics();
            
        } catch (error) {
            console.error('批量操作失败:', error);
            this.showMessage('批量操作失败', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    renderPagination() {
        const pagination = document.getElementById('pagination');
        const totalPages = Math.ceil(this.totalCount / this.pageSize);
        
        if (totalPages <= 1) {
            pagination.innerHTML = '';
            this.updatePageInfo();
            return;
        }
        
        let paginationHtml = '';
        
        // 上一页
        paginationHtml += `
            <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="taskManager.goToPage(${this.currentPage - 1})">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
        `;
        
        // 页码
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(totalPages, this.currentPage + 2);
        
        if (startPage > 1) {
            paginationHtml += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="taskManager.goToPage(1)">1</a>
                </li>
            `;
            if (startPage > 2) {
                paginationHtml += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }
        
        for (let i = startPage; i <= endPage; i++) {
            paginationHtml += `
                <li class="page-item ${i === this.currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="taskManager.goToPage(${i})">${i}</a>
                </li>
            `;
        }
        
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                paginationHtml += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
            paginationHtml += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="taskManager.goToPage(${totalPages})">${totalPages}</a>
                </li>
            `;
        }
        
        // 下一页
        paginationHtml += `
            <li class="page-item ${this.currentPage === totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="taskManager.goToPage(${this.currentPage + 1})">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
        `;
        
        pagination.innerHTML = paginationHtml;
        this.updatePageInfo();
    }

    updatePageInfo() {
        const start = (this.currentPage - 1) * this.pageSize + 1;
        const end = Math.min(this.currentPage * this.pageSize, this.totalCount);
        
        document.getElementById('pageStart').textContent = this.totalCount > 0 ? start : 0;
        document.getElementById('pageEnd').textContent = end;
        document.getElementById('totalCount').textContent = this.totalCount;
    }

    goToPage(page) {
        if (page < 1 || page > Math.ceil(this.totalCount / this.pageSize)) {
            return;
        }
        this.currentPage = page;
        this.loadTasks();
    }

    // 工具方法
    getStatusClass(status) {
        const statusMap = {
            'TODO': 'status-todo',
            'IN_PROGRESS': 'status-in-progress',
            'REVIEW': 'status-review',
            'COMPLETED': 'status-completed',
            'CANCELLED': 'status-cancelled'
        };
        return statusMap[status] || 'status-todo';
    }

    getStatusText(status) {
        const statusMap = {
            'TODO': '待办',
            'IN_PROGRESS': '进行中',
            'REVIEW': '待审核',
            'COMPLETED': '已完成',
            'CANCELLED': '已取消'
        };
        return statusMap[status] || '待办';
    }

    getPriorityClass(priority) {
        const priorityMap = {
            'HIGH': 'priority-high',
            'MEDIUM': 'priority-medium',
            'LOW': 'priority-low'
        };
        return priorityMap[priority] || 'priority-medium';
    }

    getPriorityText(priority) {
        const priorityMap = {
            'HIGH': '高',
            'MEDIUM': '中',
            'LOW': '低'
        };
        return priorityMap[priority] || '中';
    }

    getPriorityIcon(priority) {
        const iconMap = {
            'HIGH': 'fas fa-arrow-up',
            'MEDIUM': 'fas fa-minus',
            'LOW': 'fas fa-arrow-down'
        };
        return iconMap[priority] || 'fas fa-minus';
    }

    getDueDateClass(dueDate) {
        const now = new Date();
        const due = new Date(dueDate);
        const diffDays = Math.ceil((due - now) / (1000 * 60 * 60 * 24));
        
        if (diffDays < 0) {
            return 'text-danger fw-bold'; // 已过期
        } else if (diffDays <= 3) {
            return 'text-warning fw-bold'; // 即将到期
        }
        return 'text-muted';
    }

    formatDate(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN');
    }

    formatDateTime(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN');
    }

    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    showLoading(show) {
        const existingSpinner = document.querySelector('.loading-spinner');
        
        if (show && !existingSpinner) {
            const spinner = document.createElement('div');
            spinner.className = 'loading-spinner';
            spinner.innerHTML = '<div class="spinner"></div>';
            
            const container = this.currentView === 'list' 
                ? document.getElementById('taskTableBody')
                : document.querySelector('.kanban-board');
            
            if (container) {
                container.appendChild(spinner);
            }
        } else if (!show && existingSpinner) {
            existingSpinner.remove();
        }
    }

    showMessage(message, type = 'info') {
        // 使用全局的 showToast 函数
        if (typeof showToast === 'function') {
            showToast(message, type);
        } else {
            alert(message);
        }
    }
}

// 全局函数
function viewTask(taskId) {
    console.log('查看任务:', taskId);
    // TODO: 实现任务详情查看
}

function editTask(taskId) {
    console.log('编辑任务:', taskId);
    // TODO: 实现任务编辑
}

function deleteTask(taskId) {
    if (confirm('确定要删除这个任务吗？')) {
        console.log('删除任务:', taskId);
        // TODO: 实现任务删除
    }
}

function refreshTasks() {
    if (window.taskManager) {
        window.taskManager.loadTasks();
        window.taskManager.loadStatistics();
    }
}

function searchTasks() {
    if (window.taskManager) {
        const searchInput = document.getElementById('searchInput');
        window.taskManager.filters.search = searchInput.value;
        window.taskManager.currentPage = 1;
        window.taskManager.loadTasks();
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    window.taskManager = new TaskManager();
});