/**
 * 项目管理页面JavaScript逻辑
 * 处理项目列表、创建、编辑、删除等功能
 */

class ProjectManager {
    constructor() {
        this.projects = [];
        this.filteredProjects = [];
        this.currentPage = 1;
        this.pageSize = 12;
        this.totalPages = 0;
        this.selectedMembers = [];
        this.viewMode = 'grid';
        this.filters = {
            search: '',
            status: '',
            priority: '',
            sortBy: 'createTime'
        };
        
        this.init();
    }

    /**
     * 初始化项目管理器
     */
    async init() {
        this.setupEventListeners();
        this.checkAuthentication();
        await this.loadProjects();
        this.loadStatistics();
    }

    /**
     * 检查用户认证状态
     */
    checkAuthentication() {
        const token = localStorage.getItem(APP_CONFIG.TOKEN_KEY);
        const user = JSON.parse(localStorage.getItem(APP_CONFIG.USER_KEY) || '{}');
        
        if (!token || !user.id) {
            window.location.href = 'login.html';
            return;
        }
        
        // 更新导航栏用户信息
        const userNameElement = document.getElementById('currentUserName');
        if (userNameElement) {
            userNameElement.textContent = user.realName || user.username;
        }
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 搜索输入
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', debounce((e) => {
                this.filters.search = e.target.value;
                this.applyFilters();
            }, 300));
        }

        // 筛选器
        ['statusFilter', 'priorityFilter', 'sortBy'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', (e) => {
                    this.filters[id.replace('Filter', '').replace('sortBy', 'sortBy')] = e.target.value;
                    this.applyFilters();
                });
            }
        });

        // 视图模式切换
        document.querySelectorAll('input[name="viewMode"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                this.viewMode = e.target.value;
                this.renderProjects();
            });
        });

        // 创建项目表单
        const createForm = document.getElementById('createProjectForm');
        if (createForm) {
            createForm.addEventListener('submit', (e) => this.handleCreateProject(e));
        }

        // 成员搜索
        const memberSearch = document.getElementById('memberSearch');
        if (memberSearch) {
            memberSearch.addEventListener('input', debounce((e) => {
                if (e.target.value.length >= 2) {
                    this.searchUsers(e.target.value);
                } else {
                    this.clearUserSearchResults();
                }
            }, 300));
        }

        // 项目名称自动生成编码
        const projectName = document.getElementById('projectName');
        if (projectName) {
            projectName.addEventListener('input', (e) => {
                this.generateProjectCode(e.target.value);
            });
        }
    }

    /**
     * 加载项目列表
     */
    async loadProjects() {
        try {
            this.showLoading(true);
            
            const response = await fetch(`${APP_CONFIG.API_BASE_URL}/projects`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem(APP_CONFIG.TOKEN_KEY)}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    this.projects = data.data || [];
                    this.applyFilters();
                } else {
                    throw new Error(data.message || '加载项目失败');
                }
            } else {
                throw new Error('网络请求失败');
            }
        } catch (error) {
            console.error('加载项目失败:', error);
            showToast('加载项目失败: ' + error.message, 'error');
            this.projects = [];
            this.renderProjects();
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * 加载统计数据
     */
    async loadStatistics() {
        try {
            const response = await fetch(`${APP_CONFIG.API_BASE_URL}/statistics/dashboard`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem(APP_CONFIG.TOKEN_KEY)}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    this.updateStatistics(data.data);
                }
            }
        } catch (error) {
            console.error('加载统计数据失败:', error);
        }
    }

    /**
     * 更新统计数据显示
     */
    updateStatistics(stats) {
        const elements = {
            totalProjects: document.getElementById('totalProjects'),
            activeProjects: document.getElementById('activeProjects'),
            completedProjects: document.getElementById('completedProjects'),
            myProjects: document.getElementById('myProjects')
        };

        if (elements.totalProjects) {
            this.animateNumber(elements.totalProjects, stats.totalProjects || this.projects.length);
        }
        if (elements.activeProjects) {
            this.animateNumber(elements.activeProjects, stats.activeProjects || 
                this.projects.filter(p => p.status === 'IN_PROGRESS').length);
        }
        if (elements.completedProjects) {
            this.animateNumber(elements.completedProjects, stats.completedProjects || 
                this.projects.filter(p => p.status === 'COMPLETED').length);
        }
        if (elements.myProjects) {
            this.animateNumber(elements.myProjects, stats.myProjects || this.projects.length);
        }
    }

    /**
     * 数字动画效果
     */
    animateNumber(element, target) {
        const start = parseInt(element.textContent) || 0;
        const duration = 1000;
        const startTime = Date.now();

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            const current = Math.floor(start + (target - start) * progress);
            
            element.textContent = current;
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };

        animate();
    }

    /**
     * 应用筛选器
     */
    applyFilters() {
        let filtered = [...this.projects];

        // 搜索筛选
        if (this.filters.search) {
            const search = this.filters.search.toLowerCase();
            filtered = filtered.filter(project => 
                project.name.toLowerCase().includes(search) ||
                (project.description && project.description.toLowerCase().includes(search)) ||
                (project.code && project.code.toLowerCase().includes(search))
            );
        }

        // 状态筛选
        if (this.filters.status) {
            filtered = filtered.filter(project => project.status === this.filters.status);
        }

        // 优先级筛选
        if (this.filters.priority) {
            filtered = filtered.filter(project => project.priority === this.filters.priority);
        }

        // 排序
        filtered.sort((a, b) => {
            const field = this.filters.sortBy;
            if (field === 'name') {
                return a.name.localeCompare(b.name);
            } else if (field === 'priority') {
                const priorityOrder = { 'HIGH': 3, 'MEDIUM': 2, 'LOW': 1 };
                return priorityOrder[b.priority] - priorityOrder[a.priority];
            } else {
                return new Date(b[field]) - new Date(a[field]);
            }
        });

        this.filteredProjects = filtered;
        this.currentPage = 1;
        this.calculatePagination();
        this.renderProjects();
        this.renderPagination();
    }

    /**
     * 计算分页
     */
    calculatePagination() {
        this.totalPages = Math.ceil(this.filteredProjects.length / this.pageSize);
    }

    /**
     * 渲染项目列表
     */
    renderProjects() {
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        const pageProjects = this.filteredProjects.slice(startIndex, endIndex);

        const gridContainer = document.getElementById('projectsGrid');
        const listContainer = document.getElementById('projectsList');
        const emptyState = document.getElementById('emptyState');

        // 隐藏所有容器
        [gridContainer, listContainer, emptyState].forEach(el => {
            if (el) el.style.display = 'none';
        });

        if (pageProjects.length === 0) {
            if (emptyState) emptyState.style.display = 'block';
            return;
        }

        if (this.viewMode === 'grid') {
            this.renderGridView(pageProjects, gridContainer);
        } else {
            this.renderListView(pageProjects, listContainer);
        }
    }

    /**
     * 渲染网格视图
     */
    renderGridView(projects, container) {
        if (!container) return;

        container.innerHTML = projects.map(project => `
            <div class="col-md-6 col-lg-4">
                <div class="card project-card fade-in-up">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="card-title mb-1">${this.escapeHtml(project.name)}</h6>
                                <small class="text-muted">${project.code || ''}</small>
                            </div>
                            <div class="d-flex gap-1">
                                <span class="status-badge status-${project.status.toLowerCase().replace('_', '-')}">
                                    ${this.getStatusText(project.status)}
                                </span>
                                <span class="priority-badge priority-${project.priority.toLowerCase()}">
                                    ${this.getPriorityText(project.priority)}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <p class="card-text text-muted small mb-3">
                            ${this.escapeHtml(project.description || '暂无描述').substring(0, 100)}${project.description && project.description.length > 100 ? '...' : ''}
                        </p>
                        
                        <div class="progress-container">
                            <div class="progress">
                                <div class="progress-bar bg-success" style="width: ${project.progress || 0}%"></div>
                            </div>
                            <div class="progress-text">进度: ${project.progress || 0}%</div>
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div class="member-avatars">
                                ${this.renderMemberAvatars(project.members || [])}
                            </div>
                            <small class="text-muted">
                                ${formatDate(project.createTime)}
                            </small>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="project-actions">
                            <button class="btn btn-outline-primary action-btn" onclick="viewProject(${project.id})" title="查看详情">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-success action-btn" onclick="editProject(${project.id})" title="编辑项目">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-info action-btn" onclick="manageMembers(${project.id})" title="成员管理">
                                <i class="fas fa-users"></i>
                            </button>
                            <button class="btn btn-outline-danger action-btn" onclick="deleteProject(${project.id})" title="删除项目">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');

        container.style.display = 'block';
    }

    /**
     * 渲染列表视图
     */
    renderListView(projects, container) {
        if (!container) return;

        const tbody = document.getElementById('projectsTableBody');
        if (!tbody) return;

        tbody.innerHTML = projects.map(project => `
            <tr>
                <td>
                    <div>
                        <strong>${this.escapeHtml(project.name)}</strong>
                        <br>
                        <small class="text-muted">${project.code || ''}</small>
                    </div>
                </td>
                <td>
                    <span class="status-badge status-${project.status.toLowerCase().replace('_', '-')}">
                        ${this.getStatusText(project.status)}
                    </span>
                </td>
                <td>
                    <span class="priority-badge priority-${project.priority.toLowerCase()}">
                        ${this.getPriorityText(project.priority)}
                    </span>
                </td>
                <td>
                    <div class="member-avatars">
                        ${this.renderMemberAvatars(project.members || [])}
                    </div>
                </td>
                <td>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar bg-success" style="width: ${project.progress || 0}%"></div>
                    </div>
                    <small class="text-muted">${project.progress || 0}%</small>
                </td>
                <td>
                    <small>${formatDate(project.createTime)}</small>
                </td>
                <td>
                    <div class="project-actions">
                        <button class="btn btn-outline-primary action-btn" onclick="viewProject(${project.id})" title="查看详情">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-success action-btn" onclick="editProject(${project.id})" title="编辑项目">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-danger action-btn" onclick="deleteProject(${project.id})" title="删除项目">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');

        container.style.display = 'block';
    }

    /**
     * 渲染成员头像
     */
    renderMemberAvatars(members) {
        if (!members || members.length === 0) {
            return '<small class="text-muted">暂无成员</small>';
        }

        const maxShow = 3;
        const showMembers = members.slice(0, maxShow);
        const remainingCount = members.length - maxShow;

        let html = showMembers.map(member => {
            const initial = (member.realName || member.username || '?').charAt(0).toUpperCase();
            return `<div class="member-avatar" title="${this.escapeHtml(member.realName || member.username)}">${initial}</div>`;
        }).join('');

        if (remainingCount > 0) {
            html += `<div class="member-avatar member-more" title="还有${remainingCount}位成员">+${remainingCount}</div>`;
        }

        return html;
    }

    /**
     * 渲染分页
     */
    renderPagination() {
        const pagination = document.getElementById('pagination');
        if (!pagination || this.totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }

        let html = '';
        
        // 上一页
        html += `
            <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="projectManager.goToPage(${this.currentPage - 1})">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
        `;

        // 页码
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(this.totalPages, this.currentPage + 2);

        for (let i = startPage; i <= endPage; i++) {
            html += `
                <li class="page-item ${i === this.currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="projectManager.goToPage(${i})">${i}</a>
                </li>
            `;
        }

        // 下一页
        html += `
            <li class="page-item ${this.currentPage === this.totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="projectManager.goToPage(${this.currentPage + 1})">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
        `;

        pagination.innerHTML = html;
    }

    /**
     * 跳转到指定页面
     */
    goToPage(page) {
        if (page < 1 || page > this.totalPages || page === this.currentPage) {
            return;
        }
        
        this.currentPage = page;
        this.renderProjects();
        this.renderPagination();
    }

    /**
     * 显示/隐藏加载状态
     */
    showLoading(show) {
        const loading = document.getElementById('projectsLoading');
        if (loading) {
            loading.style.display = show ? 'block' : 'none';
        }
    }

    /**
     * 处理创建项目
     */
    async handleCreateProject(event) {
        event.preventDefault();
        
        const form = event.target;
        if (!form.checkValidity()) {
            form.classList.add('was-validated');
            return;
        }

        const formData = {
            name: document.getElementById('projectName').value.trim(),
            code: document.getElementById('projectCode').value.trim(),
            description: document.getElementById('projectDescription').value.trim(),
            priority: document.getElementById('projectPriority').value,
            startDate: document.getElementById('projectStartDate').value,
            endDate: document.getElementById('projectEndDate').value,
            memberIds: this.selectedMembers.map(m => m.id)
        };

        try {
            this.setCreateLoading(true);
            
            const response = await fetch(`${APP_CONFIG.API_BASE_URL}/projects`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem(APP_CONFIG.TOKEN_KEY)}`
                },
                body: JSON.stringify(formData)
            });

            const data = await response.json();

            if (response.ok && data.success) {
                showToast('项目创建成功！', 'success');
                
                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('createProjectModal'));
                modal.hide();
                
                // 重置表单
                this.resetCreateForm();
                
                // 重新加载项目列表
                await this.loadProjects();
                this.loadStatistics();
            } else {
                throw new Error(data.message || '创建项目失败');
            }
        } catch (error) {
            console.error('创建项目失败:', error);
            showToast('创建项目失败: ' + error.message, 'error');
        } finally {
            this.setCreateLoading(false);
        }
    }

    /**
     * 设置创建按钮加载状态
     */
    setCreateLoading(loading) {
        const form = document.getElementById('createProjectForm');
        const submitBtn = form.querySelector('button[type="submit"]');
        const btnText = submitBtn.querySelector('.btn-text');
        const btnLoading = submitBtn.querySelector('.btn-loading');

        if (loading) {
            submitBtn.disabled = true;
            btnText.classList.add('d-none');
            btnLoading.classList.remove('d-none');
        } else {
            submitBtn.disabled = false;
            btnText.classList.remove('d-none');
            btnLoading.classList.add('d-none');
        }
    }

    /**
     * 重置创建表单
     */
    resetCreateForm() {
        const form = document.getElementById('createProjectForm');
        form.reset();
        form.classList.remove('was-validated');
        this.selectedMembers = [];
        this.updateSelectedMembers();
        this.clearUserSearchResults();
    }

    /**
     * 生成项目编码
     */
    generateProjectCode(name) {
        if (!name) return;
        
        // 简单的编码生成逻辑：取名称首字母 + 时间戳后4位
        const initials = name.split(' ').map(word => 
            word.charAt(0).toUpperCase()
        ).join('').substring(0, 3);
        
        const timestamp = Date.now().toString().slice(-4);
        const code = `${initials}${timestamp}`;
        
        const codeInput = document.getElementById('projectCode');
        if (codeInput && !codeInput.value) {
            codeInput.value = code;
        }
    }

    /**
     * 搜索用户
     */
    async searchUsers(query) {
        try {
            const response = await fetch(`${APP_CONFIG.API_BASE_URL}/users/search?q=${encodeURIComponent(query)}`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem(APP_CONFIG.TOKEN_KEY)}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    this.renderUserSearchResults(data.data || []);
                }
            }
        } catch (error) {
            console.error('搜索用户失败:', error);
        }
    }

    /**
     * 渲染用户搜索结果
     */
    renderUserSearchResults(users) {
        const container = document.getElementById('userSearchResults');
        if (!container) return;

        if (users.length === 0) {
            container.innerHTML = '<small class="text-muted">未找到匹配的用户</small>';
            return;
        }

        container.innerHTML = users.map(user => `
            <div class="user-search-item" onclick="projectManager.addMember(${JSON.stringify(user).replace(/"/g, '&quot;')})">
                <div class="d-flex align-items-center">
                    <div class="member-avatar me-2">
                        ${(user.realName || user.username).charAt(0).toUpperCase()}
                    </div>
                    <div>
                        <div class="fw-medium">${this.escapeHtml(user.realName || user.username)}</div>
                        <small class="text-muted">${this.escapeHtml(user.email || '')}</small>
                    </div>
                </div>
            </div>
        `).join('');
    }

    /**
     * 清除用户搜索结果
     */
    clearUserSearchResults() {
        const container = document.getElementById('userSearchResults');
        if (container) {
            container.innerHTML = '';
        }
    }

    /**
     * 添加成员
     */
    addMember(user) {
        // 检查是否已经添加
        if (this.selectedMembers.find(m => m.id === user.id)) {
            showToast('该用户已经是项目成员', 'warning');
            return;
        }

        this.selectedMembers.push(user);
        this.updateSelectedMembers();
        this.clearUserSearchResults();
        
        // 清空搜索框
        const searchInput = document.getElementById('memberSearch');
        if (searchInput) {
            searchInput.value = '';
        }
    }

    /**
     * 移除成员
     */
    removeMember(userId) {
        this.selectedMembers = this.selectedMembers.filter(m => m.id !== userId);
        this.updateSelectedMembers();
    }

    /**
     * 更新已选成员显示
     */
    updateSelectedMembers() {
        const container = document.getElementById('selectedMembers');
        if (!container) return;

        if (this.selectedMembers.length === 0) {
            container.innerHTML = '<small class="text-muted">暂未选择成员</small>';
            return;
        }

        container.innerHTML = this.selectedMembers.map(member => `
            <span class="selected-member">
                ${this.escapeHtml(member.realName || member.username)}
                <button type="button" class="remove-member" onclick="projectManager.removeMember(${member.id})">
                    <i class="fas fa-times"></i>
                </button>
            </span>
        `).join('');
    }

    /**
     * 获取状态文本
     */
    getStatusText(status) {
        const statusMap = {
            'PLANNING': '规划中',
            'IN_PROGRESS': '进行中',
            'COMPLETED': '已完成',
            'ON_HOLD': '暂停',
            'CANCELLED': '已取消'
        };
        return statusMap[status] || status;
    }

    /**
     * 获取优先级文本
     */
    getPriorityText(priority) {
        const priorityMap = {
            'HIGH': '高',
            'MEDIUM': '中',
            'LOW': '低'
        };
        return priorityMap[priority] || priority;
    }

    /**
     * HTML转义
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// 全局函数
/**
 * 刷新项目列表
 */
function refreshProjects() {
    if (window.projectManager) {
        window.projectManager.loadProjects();
        window.projectManager.loadStatistics();
    }
}

/**
 * 查看项目详情
 */
function viewProject(projectId) {
    // TODO: 实现项目详情查看
    console.log('查看项目:', projectId);
    showToast('项目详情功能开发中...', 'info');
}

/**
 * 编辑项目
 */
function editProject(projectId) {
    // TODO: 实现项目编辑
    console.log('编辑项目:', projectId);
    showToast('项目编辑功能开发中...', 'info');
}

/**
 * 管理项目成员
 */
function manageMembers(projectId) {
    // TODO: 实现成员管理
    console.log('管理成员:', projectId);
    showToast('成员管理功能开发中...', 'info');
}

/**
 * 删除项目
 */
function deleteProject(projectId) {
    if (confirm('确定要删除这个项目吗？此操作不可撤销。')) {
        // TODO: 实现项目删除
        console.log('删除项目:', projectId);
        showToast('项目删除功能开发中...', 'info');
    }
}

/**
 * 搜索用户
 */
function searchUsers() {
    const query = document.getElementById('memberSearch').value.trim();
    if (query && window.projectManager) {
        window.projectManager.searchUsers(query);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.projectManager = new ProjectManager();
});

// 导出到全局
window.refreshProjects = refreshProjects;
window.viewProject = viewProject;
window.editProject = editProject;
window.manageMembers = manageMembers;
window.deleteProject = deleteProject;
window.searchUsers = searchUsers;