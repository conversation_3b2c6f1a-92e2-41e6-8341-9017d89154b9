/**
 * 仪表板组件
 * 显示项目统计信息、最近项目和任务概览
 */

class Dashboard {
    constructor() {
        this.data = {
            statistics: {
                totalProjects: 0,
                activeTasks: 0,
                pendingApprovals: 0,
                teamMembers: 0
            },
            recentProjects: [],
            recentTasks: []
        };
    }

    /**
     * 渲染仪表板
     */
    async render() {
        try {
            // 显示加载状态
            this.showLoading();
            
            // 加载数据
            await this.loadData();
            
            // 渲染内容
            this.renderContent();
            
            // 更新统计数据
            this.updateStatistics();
            
            // 渲染最近项目
            this.renderRecentProjects();
            
        } catch (error) {
            console.error('仪表板渲染失败:', error);
            this.showError('仪表板加载失败，请稍后重试');
        }
    }

    /**
     * 显示加载状态
     */
    showLoading() {
        const contentContainer = document.getElementById('app-content');
        contentContainer.innerHTML = `
            <div class="d-flex justify-content-center align-items-center" style="height: 300px;">
                <div class="text-center">
                    <div class="loading mb-3"></div>
                    <p class="text-muted">加载仪表板数据...</p>
                </div>
            </div>
        `;
    }

    /**
     * 显示错误信息
     * @param {string} message - 错误消息
     */
    showError(message) {
        const contentContainer = document.getElementById('app-content');
        contentContainer.innerHTML = `
            <div class="alert alert-danger" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                ${message}
                <button class="btn btn-outline-danger btn-sm ms-3" onclick="Dashboard.render()">
                    重试
                </button>
            </div>
        `;
    }

    /**
     * 加载仪表板数据
     */
    async loadData() {
        try {
            // 并行加载所有数据
            const [statisticsResponse, projectsResponse] = await Promise.all([
                this.loadStatistics(),
                this.loadRecentProjects()
            ]);

            this.data.statistics = statisticsResponse.data || this.getDefaultStatistics();
            this.data.recentProjects = projectsResponse.data || [];
            
        } catch (error) {
            console.error('加载仪表板数据失败:', error);
            // 使用默认数据
            this.data.statistics = this.getDefaultStatistics();
            this.data.recentProjects = this.getDefaultProjects();
        }
    }

    /**
     * 加载统计数据
     * @returns {Promise<Object>} 统计数据
     */
    async loadStatistics() {
        try {
            return await authAPI.makeAuthenticatedRequest('/dashboard/statistics');
        } catch (error) {
            console.error('加载统计数据失败:', error);
            return { data: this.getDefaultStatistics() };
        }
    }

    /**
     * 加载最近项目
     * @returns {Promise<Object>} 最近项目数据
     */
    async loadRecentProjects() {
        try {
            return await authAPI.makeAuthenticatedRequest('/projects/recent?limit=5');
        } catch (error) {
            console.error('加载最近项目失败:', error);
            return { data: this.getDefaultProjects() };
        }
    }

    /**
     * 获取默认统计数据
     * @returns {Object} 默认统计数据
     */
    getDefaultStatistics() {
        return {
            totalProjects: 12,
            activeTasks: 28,
            pendingApprovals: 5,
            teamMembers: 15
        };
    }

    /**
     * 获取默认项目数据
     * @returns {Array} 默认项目列表
     */
    getDefaultProjects() {
        return [
            {
                id: 1,
                name: '电商平台重构',
                status: 'IN_PROGRESS',
                progress: 75,
                dueDate: '2024-02-15',
                memberCount: 8
            },
            {
                id: 2,
                name: '移动端APP开发',
                status: 'IN_PROGRESS',
                progress: 45,
                dueDate: '2024-03-01',
                memberCount: 5
            },
            {
                id: 3,
                name: '数据分析系统',
                status: 'PLANNING',
                progress: 20,
                dueDate: '2024-04-10',
                memberCount: 3
            }
        ];
    }

    /**
     * 渲染仪表板内容
     */
    renderContent() {
        const contentContainer = document.getElementById('app-content');
        contentContainer.innerHTML = `
            <div id="dashboard-content" class="fade-in">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h2">仪表板</h1>
                    <div class="text-muted">
                        <i class="fas fa-clock me-1"></i>
                        最后更新: ${Utils.formatDate(new Date(), 'YYYY-MM-DD HH:mm')}
                    </div>
                </div>
                
                <!-- 统计卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card text-white bg-primary h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title mb-1">总项目数</h6>
                                        <h2 class="mb-0" id="total-projects">0</h2>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-folder fa-2x opacity-75"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card text-white bg-success h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title mb-1">进行中任务</h6>
                                        <h2 class="mb-0" id="active-tasks">0</h2>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-tasks fa-2x opacity-75"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card text-white bg-warning h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title mb-1">待审核</h6>
                                        <h2 class="mb-0" id="pending-approvals">0</h2>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-clock fa-2x opacity-75"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card text-white bg-info h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title mb-1">团队成员</h6>
                                        <h2 class="mb-0" id="team-members">0</h2>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-users fa-2x opacity-75"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 最近项目和快速操作 -->
                <div class="row">
                    <!-- 最近项目 -->
                    <div class="col-lg-8 mb-4">
                        <div class="card h-100">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-folder-open me-2"></i>
                                    最近项目
                                </h5>
                                <a href="#projects" class="btn btn-outline-primary btn-sm">
                                    查看全部
                                </a>
                            </div>
                            <div class="card-body p-0">
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead class="table-light">
                                            <tr>
                                                <th>项目名称</th>
                                                <th>状态</th>
                                                <th>进度</th>
                                                <th>截止日期</th>
                                                <th>成员</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="recent-projects">
                                            <!-- 动态加载项目数据 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 快速操作 -->
                    <div class="col-lg-4 mb-4">
                        <div class="card h-100">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-bolt me-2"></i>
                                    快速操作
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <button class="btn btn-primary" onclick="createProject()">
                                        <i class="fas fa-plus me-2"></i>
                                        新建项目
                                    </button>
                                    <button class="btn btn-outline-primary" onclick="createTask()">
                                        <i class="fas fa-tasks me-2"></i>
                                        新建任务
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="appState.navigateTo('approvals')">
                                        <i class="fas fa-check-circle me-2"></i>
                                        审核管理
                                    </button>
                                    <button class="btn btn-outline-info" onclick="showReports()">
                                        <i class="fas fa-chart-bar me-2"></i>
                                        查看报表
                                    </button>
                                </div>
                                
                                <hr class="my-3">
                                
                                <h6 class="text-muted mb-2">最近活动</h6>
                                <div id="recent-activities">
                                    <div class="d-flex align-items-center mb-2">
                                        <div class="status-indicator active me-2"></div>
                                        <small class="text-muted">张三完成了任务"用户界面设计"</small>
                                    </div>
                                    <div class="d-flex align-items-center mb-2">
                                        <div class="status-indicator pending me-2"></div>
                                        <small class="text-muted">李四提交了项目审核申请</small>
                                    </div>
                                    <div class="d-flex align-items-center mb-2">
                                        <div class="status-indicator completed me-2"></div>
                                        <small class="text-muted">王五创建了新项目"移动端开发"</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 更新统计数据显示
     */
    updateStatistics() {
        const stats = this.data.statistics;
        
        // 使用动画效果更新数字
        this.animateNumber('total-projects', stats.totalProjects);
        this.animateNumber('active-tasks', stats.activeTasks);
        this.animateNumber('pending-approvals', stats.pendingApprovals);
        this.animateNumber('team-members', stats.teamMembers);
    }

    /**
     * 数字动画效果
     * @param {string} elementId - 元素ID
     * @param {number} targetValue - 目标值
     */
    animateNumber(elementId, targetValue) {
        const element = document.getElementById(elementId);
        if (!element) return;
        
        const startValue = 0;
        const duration = 1000; // 1秒
        const startTime = Date.now();
        
        const updateNumber = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // 使用缓动函数
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const currentValue = Math.floor(startValue + (targetValue - startValue) * easeOutQuart);
            
            element.textContent = currentValue;
            
            if (progress < 1) {
                requestAnimationFrame(updateNumber);
            } else {
                element.textContent = targetValue;
            }
        };
        
        updateNumber();
    }

    /**
     * 渲染最近项目列表
     */
    renderRecentProjects() {
        const tbody = document.getElementById('recent-projects');
        if (!tbody) return;
        
        if (this.data.recentProjects.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center text-muted py-4">
                        <i class="fas fa-folder-open fa-2x mb-2 d-block"></i>
                        暂无项目数据
                    </td>
                </tr>
            `;
            return;
        }
        
        tbody.innerHTML = this.data.recentProjects.map(project => `
            <tr>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="status-indicator ${this.getStatusClass(project.status)} me-2"></div>
                        <span class="fw-medium">${project.name}</span>
                    </div>
                </td>
                <td>
                    <span class="badge bg-${this.getStatusBadgeClass(project.status)}">
                        ${this.getStatusText(project.status)}
                    </span>
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="progress me-2" style="width: 60px; height: 6px;">
                            <div class="progress-bar bg-${this.getProgressBarClass(project.progress)}" 
                                 style="width: ${project.progress}%"></div>
                        </div>
                        <small class="text-muted">${project.progress}%</small>
                    </div>
                </td>
                <td>
                    <small class="text-muted">${Utils.formatDate(project.dueDate)}</small>
                </td>
                <td>
                    <span class="badge bg-light text-dark">
                        <i class="fas fa-users me-1"></i>
                        ${project.memberCount}
                    </span>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary btn-sm" 
                                onclick="viewProject(${project.id})" 
                                title="查看详情">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" 
                                onclick="editProject(${project.id})" 
                                title="编辑项目">
                            <i class="fas fa-edit"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    /**
     * 获取状态样式类
     * @param {string} status - 状态
     * @returns {string} 样式类
     */
    getStatusClass(status) {
        const statusMap = {
            'PLANNING': 'pending',
            'IN_PROGRESS': 'active',
            'COMPLETED': 'completed',
            'CANCELLED': 'cancelled'
        };
        return statusMap[status] || 'pending';
    }

    /**
     * 获取状态徽章样式类
     * @param {string} status - 状态
     * @returns {string} 徽章样式类
     */
    getStatusBadgeClass(status) {
        const statusMap = {
            'PLANNING': 'warning',
            'IN_PROGRESS': 'primary',
            'COMPLETED': 'success',
            'CANCELLED': 'danger'
        };
        return statusMap[status] || 'secondary';
    }

    /**
     * 获取状态文本
     * @param {string} status - 状态
     * @returns {string} 状态文本
     */
    getStatusText(status) {
        const statusMap = {
            'PLANNING': '规划中',
            'IN_PROGRESS': '进行中',
            'COMPLETED': '已完成',
            'CANCELLED': '已取消'
        };
        return statusMap[status] || '未知';
    }

    /**
     * 获取进度条样式类
     * @param {number} progress - 进度百分比
     * @returns {string} 进度条样式类
     */
    getProgressBarClass(progress) {
        if (progress >= 80) return 'success';
        if (progress >= 50) return 'primary';
        if (progress >= 25) return 'warning';
        return 'danger';
    }
}

// 全局函数
/**
 * 查看项目详情
 * @param {number} projectId - 项目ID
 */
function viewProject(projectId) {
    Utils.showToast(`查看项目 ${projectId} 详情功能开发中...`, 'info');
}

/**
 * 编辑项目
 * @param {number} projectId - 项目ID
 */
function editProject(projectId) {
    Utils.showToast(`编辑项目 ${projectId} 功能开发中...`, 'info');
}

/**
 * 显示报表
 */
function showReports() {
    Utils.showToast('报表功能开发中...', 'info');
}

// 创建全局实例
const dashboardInstance = new Dashboard();

// 导出到全局
window.Dashboard = dashboardInstance;
window.DashboardClass = Dashboard;