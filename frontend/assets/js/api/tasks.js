/**
 * 任务管理API接口
 */

class TaskAPI {
    constructor() {
        this.baseURL = APP_CONFIG.API_BASE_URL;
        this.timeout = APP_CONFIG.API_TIMEOUT;
    }

    /**
     * 获取认证头
     */
    getAuthHeaders() {
        const token = localStorage.getItem(APP_CONFIG.TOKEN_KEY);
        return {
            'Content-Type': 'application/json',
            'Authorization': token ? `Bearer ${token}` : ''
        };
    }

    /**
     * 处理API响应
     */
    async handleResponse(response) {
        if (response.status === 401) {
            // Token过期，尝试刷新
            const authAPI = new AuthAPI();
            const refreshed = await authAPI.refreshToken();
            if (!refreshed) {
                window.location.href = 'login.html';
                throw new Error('认证失败');
            }
            // 重新发起请求
            throw new Error('TOKEN_REFRESHED');
        }

        if (!response.ok) {
            const error = await response.json().catch(() => ({ message: '请求失败' }));
            throw new Error(error.message || `HTTP ${response.status}`);
        }

        return await response.json();
    }

    /**
     * 发送请求
     */
    async request(url, options = {}) {
        const config = {
            headers: this.getAuthHeaders(),
            ...options
        };

        try {
            const response = await fetch(`${this.baseURL}${url}`, config);
            return await this.handleResponse(response);
        } catch (error) {
            if (error.message === 'TOKEN_REFRESHED') {
                // Token已刷新，重新发起请求
                config.headers = this.getAuthHeaders();
                const response = await fetch(`${this.baseURL}${url}`, config);
                return await this.handleResponse(response);
            }
            throw error;
        }
    }

    /**
     * 获取任务列表
     */
    async getTasks(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = `/tasks${queryString ? `?${queryString}` : ''}`;
        return await this.request(url);
    }

    /**
     * 获取任务详情
     */
    async getTask(taskId) {
        return await this.request(`/tasks/${taskId}`);
    }

    /**
     * 创建任务
     */
    async createTask(taskData) {
        return await this.request('/tasks', {
            method: 'POST',
            body: JSON.stringify(taskData)
        });
    }

    /**
     * 更新任务
     */
    async updateTask(taskId, taskData) {
        return await this.request(`/tasks/${taskId}`, {
            method: 'PUT',
            body: JSON.stringify(taskData)
        });
    }

    /**
     * 删除任务
     */
    async deleteTask(taskId) {
        return await this.request(`/tasks/${taskId}`, {
            method: 'DELETE'
        });
    }

    /**
     * 更新任务状态
     */
    async updateTaskStatus(taskId, status) {
        return await this.request(`/tasks/${taskId}/status`, {
            method: 'PATCH',
            body: JSON.stringify({ status })
        });
    }

    /**
     * 分配任务
     */
    async assignTask(taskId, assigneeId) {
        return await this.request(`/tasks/${taskId}/assign`, {
            method: 'PATCH',
            body: JSON.stringify({ assignee_id: assigneeId })
        });
    }

    /**
     * 更新任务优先级
     */
    async updateTaskPriority(taskId, priority) {
        return await this.request(`/tasks/${taskId}/priority`, {
            method: 'PATCH',
            body: JSON.stringify({ priority })
        });
    }

    /**
     * 获取任务统计信息
     */
    async getTaskStatistics(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = `/tasks/statistics${queryString ? `?${queryString}` : ''}`;
        return await this.request(url);
    }

    /**
     * 获取我的任务
     */
    async getMyTasks(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = `/tasks/my${queryString ? `?${queryString}` : ''}`;
        return await this.request(url);
    }

    /**
     * 获取我创建的任务
     */
    async getCreatedTasks(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = `/tasks/created${queryString ? `?${queryString}` : ''}`;
        return await this.request(url);
    }

    /**
     * 搜索任务
     */
    async searchTasks(query, params = {}) {
        const searchParams = { q: query, ...params };
        const queryString = new URLSearchParams(searchParams).toString();
        return await this.request(`/tasks/search?${queryString}`);
    }

    /**
     * 批量更新任务
     */
    async batchUpdateTasks(taskIds, updateData) {
        return await this.request('/tasks/batch', {
            method: 'PATCH',
            body: JSON.stringify({
                task_ids: taskIds,
                update_data: updateData
            })
        });
    }

    /**
     * 批量删除任务
     */
    async batchDeleteTasks(taskIds) {
        return await this.request('/tasks/batch', {
            method: 'DELETE',
            body: JSON.stringify({ task_ids: taskIds })
        });
    }

    /**
     * 复制任务
     */
    async duplicateTask(taskId, newData = {}) {
        return await this.request(`/tasks/${taskId}/duplicate`, {
            method: 'POST',
            body: JSON.stringify(newData)
        });
    }

    /**
     * 获取任务评论
     */
    async getTaskComments(taskId, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = `/tasks/${taskId}/comments${queryString ? `?${queryString}` : ''}`;
        return await this.request(url);
    }

    /**
     * 添加任务评论
     */
    async addTaskComment(taskId, content) {
        return await this.request(`/tasks/${taskId}/comments`, {
            method: 'POST',
            body: JSON.stringify({ content })
        });
    }

    /**
     * 删除任务评论
     */
    async deleteTaskComment(taskId, commentId) {
        return await this.request(`/tasks/${taskId}/comments/${commentId}`, {
            method: 'DELETE'
        });
    }

    /**
     * 获取任务附件
     */
    async getTaskAttachments(taskId) {
        return await this.request(`/tasks/${taskId}/attachments`);
    }

    /**
     * 上传任务附件
     */
    async uploadTaskAttachment(taskId, file, description = '') {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('description', description);

        const token = localStorage.getItem(APP_CONFIG.TOKEN_KEY);
        const headers = {
            'Authorization': token ? `Bearer ${token}` : ''
        };

        try {
            const response = await fetch(`${this.baseURL}/tasks/${taskId}/attachments`, {
                method: 'POST',
                headers,
                body: formData
            });
            return await this.handleResponse(response);
        } catch (error) {
            if (error.message === 'TOKEN_REFRESHED') {
                const newToken = localStorage.getItem(APP_CONFIG.TOKEN_KEY);
                headers.Authorization = `Bearer ${newToken}`;
                const response = await fetch(`${this.baseURL}/tasks/${taskId}/attachments`, {
                    method: 'POST',
                    headers,
                    body: formData
                });
                return await this.handleResponse(response);
            }
            throw error;
        }
    }

    /**
     * 删除任务附件
     */
    async deleteTaskAttachment(taskId, attachmentId) {
        return await this.request(`/tasks/${taskId}/attachments/${attachmentId}`, {
            method: 'DELETE'
        });
    }

    /**
     * 获取任务时间记录
     */
    async getTaskTimeEntries(taskId, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = `/tasks/${taskId}/time-entries${queryString ? `?${queryString}` : ''}`;
        return await this.request(url);
    }

    /**
     * 添加任务时间记录
     */
    async addTaskTimeEntry(taskId, timeData) {
        return await this.request(`/tasks/${taskId}/time-entries`, {
            method: 'POST',
            body: JSON.stringify(timeData)
        });
    }

    /**
     * 更新任务时间记录
     */
    async updateTaskTimeEntry(taskId, entryId, timeData) {
        return await this.request(`/tasks/${taskId}/time-entries/${entryId}`, {
            method: 'PUT',
            body: JSON.stringify(timeData)
        });
    }

    /**
     * 删除任务时间记录
     */
    async deleteTaskTimeEntry(taskId, entryId) {
        return await this.request(`/tasks/${taskId}/time-entries/${entryId}`, {
            method: 'DELETE'
        });
    }

    /**
     * 开始任务计时
     */
    async startTaskTimer(taskId, description = '') {
        return await this.request(`/tasks/${taskId}/timer/start`, {
            method: 'POST',
            body: JSON.stringify({ description })
        });
    }

    /**
     * 停止任务计时
     */
    async stopTaskTimer(taskId) {
        return await this.request(`/tasks/${taskId}/timer/stop`, {
            method: 'POST'
        });
    }

    /**
     * 获取任务依赖关系
     */
    async getTaskDependencies(taskId) {
        return await this.request(`/tasks/${taskId}/dependencies`);
    }

    /**
     * 添加任务依赖
     */
    async addTaskDependency(taskId, dependsOnTaskId, type = 'finish_to_start') {
        return await this.request(`/tasks/${taskId}/dependencies`, {
            method: 'POST',
            body: JSON.stringify({
                depends_on_task_id: dependsOnTaskId,
                dependency_type: type
            })
        });
    }

    /**
     * 删除任务依赖
     */
    async removeTaskDependency(taskId, dependencyId) {
        return await this.request(`/tasks/${taskId}/dependencies/${dependencyId}`, {
            method: 'DELETE'
        });
    }

    /**
     * 获取任务子任务
     */
    async getSubTasks(taskId, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = `/tasks/${taskId}/subtasks${queryString ? `?${queryString}` : ''}`;
        return await this.request(url);
    }

    /**
     * 创建子任务
     */
    async createSubTask(parentTaskId, taskData) {
        return await this.request(`/tasks/${parentTaskId}/subtasks`, {
            method: 'POST',
            body: JSON.stringify(taskData)
        });
    }

    /**
     * 获取任务历史记录
     */
    async getTaskHistory(taskId, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = `/tasks/${taskId}/history${queryString ? `?${queryString}` : ''}`;
        return await this.request(url);
    }

    /**
     * 获取任务甘特图数据
     */
    async getTaskGanttData(projectId, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = `/projects/${projectId}/tasks/gantt${queryString ? `?${queryString}` : ''}`;
        return await this.request(url);
    }

    /**
     * 获取任务看板数据
     */
    async getTaskKanbanData(projectId, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = `/projects/${projectId}/tasks/kanban${queryString ? `?${queryString}` : ''}`;
        return await this.request(url);
    }

    /**
     * 更新任务看板位置
     */
    async updateTaskKanbanPosition(taskId, status, position) {
        return await this.request(`/tasks/${taskId}/kanban-position`, {
            method: 'PATCH',
            body: JSON.stringify({ status, position })
        });
    }

    /**
     * 获取任务模板
     */
    async getTaskTemplates(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = `/tasks/templates${queryString ? `?${queryString}` : ''}`;
        return await this.request(url);
    }

    /**
     * 创建任务模板
     */
    async createTaskTemplate(templateData) {
        return await this.request('/tasks/templates', {
            method: 'POST',
            body: JSON.stringify(templateData)
        });
    }

    /**
     * 从模板创建任务
     */
    async createTaskFromTemplate(templateId, taskData = {}) {
        return await this.request(`/tasks/templates/${templateId}/create`, {
            method: 'POST',
            body: JSON.stringify(taskData)
        });
    }

    /**
     * 导出任务数据
     */
    async exportTasks(params = {}, format = 'excel') {
        const queryString = new URLSearchParams({ ...params, format }).toString();
        const url = `/tasks/export?${queryString}`;
        
        const token = localStorage.getItem(APP_CONFIG.TOKEN_KEY);
        const response = await fetch(`${this.baseURL}${url}`, {
            headers: {
                'Authorization': token ? `Bearer ${token}` : ''
            }
        });
        
        if (!response.ok) {
            throw new Error('导出失败');
        }
        
        return response.blob();
    }

    /**
     * 导入任务数据
     */
    async importTasks(file, projectId) {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('project_id', projectId);

        const token = localStorage.getItem(APP_CONFIG.TOKEN_KEY);
        const headers = {
            'Authorization': token ? `Bearer ${token}` : ''
        };

        try {
            const response = await fetch(`${this.baseURL}/tasks/import`, {
                method: 'POST',
                headers,
                body: formData
            });
            return await this.handleResponse(response);
        } catch (error) {
            if (error.message === 'TOKEN_REFRESHED') {
                const newToken = localStorage.getItem(APP_CONFIG.TOKEN_KEY);
                headers.Authorization = `Bearer ${newToken}`;
                const response = await fetch(`${this.baseURL}/tasks/import`, {
                    method: 'POST',
                    headers,
                    body: formData
                });
                return await this.handleResponse(response);
            }
            throw error;
        }
    }

    /**
     * 获取任务报告
     */
    async getTaskReport(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = `/tasks/report${queryString ? `?${queryString}` : ''}`;
        return await this.request(url);
    }

    /**
     * 获取任务工作量统计
     */
    async getTaskWorkloadStats(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = `/tasks/workload${queryString ? `?${queryString}` : ''}`;
        return await this.request(url);
    }

    /**
     * 获取任务完成趋势
     */
    async getTaskCompletionTrend(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = `/tasks/completion-trend${queryString ? `?${queryString}` : ''}`;
        return await this.request(url);
    }

    /**
     * 获取任务延期统计
     */
    async getTaskDelayStats(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = `/tasks/delay-stats${queryString ? `?${queryString}` : ''}`;
        return await this.request(url);
    }

    /**
     * 获取用户任务负载
     */
    async getUserTaskLoad(userId, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = `/users/${userId}/task-load${queryString ? `?${queryString}` : ''}`;
        return await this.request(url);
    }

    /**
     * 获取任务提醒设置
     */
    async getTaskReminders(taskId) {
        return await this.request(`/tasks/${taskId}/reminders`);
    }

    /**
     * 设置任务提醒
     */
    async setTaskReminder(taskId, reminderData) {
        return await this.request(`/tasks/${taskId}/reminders`, {
            method: 'POST',
            body: JSON.stringify(reminderData)
        });
    }

    /**
     * 删除任务提醒
     */
    async deleteTaskReminder(taskId, reminderId) {
        return await this.request(`/tasks/${taskId}/reminders/${reminderId}`, {
            method: 'DELETE'
        });
    }

    /**
     * 获取任务标签
     */
    async getTaskTags(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = `/tasks/tags${queryString ? `?${queryString}` : ''}`;
        return await this.request(url);
    }

    /**
     * 创建任务标签
     */
    async createTaskTag(tagData) {
        return await this.request('/tasks/tags', {
            method: 'POST',
            body: JSON.stringify(tagData)
        });
    }

    /**
     * 为任务添加标签
     */
    async addTaskTag(taskId, tagId) {
        return await this.request(`/tasks/${taskId}/tags`, {
            method: 'POST',
            body: JSON.stringify({ tag_id: tagId })
        });
    }

    /**
     * 从任务移除标签
     */
    async removeTaskTag(taskId, tagId) {
        return await this.request(`/tasks/${taskId}/tags/${tagId}`, {
            method: 'DELETE'
        });
    }
}

// 导出到全局
window.TaskAPI = TaskAPI;

// 兼容性导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TaskAPI;
}