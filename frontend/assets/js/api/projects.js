/**
 * 项目管理API接口
 * 处理项目相关的数据交互
 */

class ProjectAPI {
    constructor() {
        this.baseURL = APP_CONFIG.API_BASE_URL;
    }

    /**
     * 获取认证头
     */
    getAuthHeaders() {
        const token = localStorage.getItem(APP_CONFIG.TOKEN_KEY);
        return {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        };
    }

    /**
     * 处理API响应
     */
    async handleResponse(response) {
        if (!response.ok) {
            if (response.status === 401) {
                // Token过期，尝试刷新
                const refreshed = await this.refreshToken();
                if (!refreshed) {
                    window.location.href = 'login.html';
                    throw new Error('认证失败，请重新登录');
                }
                throw new Error('TOKEN_EXPIRED');
            }
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        if (!data.success) {
            throw new Error(data.message || '请求失败');
        }

        return data;
    }

    /**
     * 刷新Token
     */
    async refreshToken() {
        try {
            const refreshToken = localStorage.getItem(APP_CONFIG.REFRESH_TOKEN_KEY);
            if (!refreshToken) {
                return false;
            }

            const response = await fetch(`${this.baseURL}/auth/refresh`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ refreshToken })
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    localStorage.setItem(APP_CONFIG.TOKEN_KEY, data.data.token);
                    if (data.data.refreshToken) {
                        localStorage.setItem(APP_CONFIG.REFRESH_TOKEN_KEY, data.data.refreshToken);
                    }
                    return true;
                }
            }
        } catch (error) {
            console.error('刷新Token失败:', error);
        }
        return false;
    }

    /**
     * 获取项目列表
     * @param {Object} params - 查询参数
     */
    async getProjects(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = `${this.baseURL}/projects${queryString ? '?' + queryString : ''}`;
        
        const response = await fetch(url, {
            method: 'GET',
            headers: this.getAuthHeaders()
        });

        return this.handleResponse(response);
    }

    /**
     * 获取项目详情
     * @param {number} projectId - 项目ID
     */
    async getProject(projectId) {
        const response = await fetch(`${this.baseURL}/projects/${projectId}`, {
            method: 'GET',
            headers: this.getAuthHeaders()
        });

        return this.handleResponse(response);
    }

    /**
     * 创建项目
     * @param {Object} projectData - 项目数据
     */
    async createProject(projectData) {
        const response = await fetch(`${this.baseURL}/projects`, {
            method: 'POST',
            headers: this.getAuthHeaders(),
            body: JSON.stringify(projectData)
        });

        return this.handleResponse(response);
    }

    /**
     * 更新项目
     * @param {number} projectId - 项目ID
     * @param {Object} projectData - 项目数据
     */
    async updateProject(projectId, projectData) {
        const response = await fetch(`${this.baseURL}/projects/${projectId}`, {
            method: 'PUT',
            headers: this.getAuthHeaders(),
            body: JSON.stringify(projectData)
        });

        return this.handleResponse(response);
    }

    /**
     * 删除项目
     * @param {number} projectId - 项目ID
     */
    async deleteProject(projectId) {
        const response = await fetch(`${this.baseURL}/projects/${projectId}`, {
            method: 'DELETE',
            headers: this.getAuthHeaders()
        });

        return this.handleResponse(response);
    }

    /**
     * 获取项目成员
     * @param {number} projectId - 项目ID
     */
    async getProjectMembers(projectId) {
        const response = await fetch(`${this.baseURL}/projects/${projectId}/members`, {
            method: 'GET',
            headers: this.getAuthHeaders()
        });

        return this.handleResponse(response);
    }

    /**
     * 添加项目成员
     * @param {number} projectId - 项目ID
     * @param {Array} userIds - 用户ID数组
     */
    async addProjectMembers(projectId, userIds) {
        const response = await fetch(`${this.baseURL}/projects/${projectId}/members`, {
            method: 'POST',
            headers: this.getAuthHeaders(),
            body: JSON.stringify({ userIds })
        });

        return this.handleResponse(response);
    }

    /**
     * 移除项目成员
     * @param {number} projectId - 项目ID
     * @param {number} userId - 用户ID
     */
    async removeProjectMember(projectId, userId) {
        const response = await fetch(`${this.baseURL}/projects/${projectId}/members/${userId}`, {
            method: 'DELETE',
            headers: this.getAuthHeaders()
        });

        return this.handleResponse(response);
    }

    /**
     * 更新项目成员角色
     * @param {number} projectId - 项目ID
     * @param {number} userId - 用户ID
     * @param {string} role - 角色
     */
    async updateMemberRole(projectId, userId, role) {
        const response = await fetch(`${this.baseURL}/projects/${projectId}/members/${userId}`, {
            method: 'PUT',
            headers: this.getAuthHeaders(),
            body: JSON.stringify({ role })
        });

        return this.handleResponse(response);
    }

    /**
     * 获取项目任务
     * @param {number} projectId - 项目ID
     * @param {Object} params - 查询参数
     */
    async getProjectTasks(projectId, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = `${this.baseURL}/projects/${projectId}/tasks${queryString ? '?' + queryString : ''}`;
        
        const response = await fetch(url, {
            method: 'GET',
            headers: this.getAuthHeaders()
        });

        return this.handleResponse(response);
    }

    /**
     * 获取项目统计信息
     * @param {number} projectId - 项目ID
     */
    async getProjectStatistics(projectId) {
        const response = await fetch(`${this.baseURL}/projects/${projectId}/statistics`, {
            method: 'GET',
            headers: this.getAuthHeaders()
        });

        return this.handleResponse(response);
    }

    /**
     * 获取项目进度报告
     * @param {number} projectId - 项目ID
     * @param {string} startDate - 开始日期
     * @param {string} endDate - 结束日期
     */
    async getProjectProgressReport(projectId, startDate, endDate) {
        const params = new URLSearchParams({
            startDate,
            endDate
        });
        
        const response = await fetch(`${this.baseURL}/projects/${projectId}/progress-report?${params}`, {
            method: 'GET',
            headers: this.getAuthHeaders()
        });

        return this.handleResponse(response);
    }

    /**
     * 更新项目状态
     * @param {number} projectId - 项目ID
     * @param {string} status - 新状态
     */
    async updateProjectStatus(projectId, status) {
        const response = await fetch(`${this.baseURL}/projects/${projectId}/status`, {
            method: 'PUT',
            headers: this.getAuthHeaders(),
            body: JSON.stringify({ status })
        });

        return this.handleResponse(response);
    }

    /**
     * 归档项目
     * @param {number} projectId - 项目ID
     */
    async archiveProject(projectId) {
        const response = await fetch(`${this.baseURL}/projects/${projectId}/archive`, {
            method: 'POST',
            headers: this.getAuthHeaders()
        });

        return this.handleResponse(response);
    }

    /**
     * 恢复项目
     * @param {number} projectId - 项目ID
     */
    async restoreProject(projectId) {
        const response = await fetch(`${this.baseURL}/projects/${projectId}/restore`, {
            method: 'POST',
            headers: this.getAuthHeaders()
        });

        return this.handleResponse(response);
    }

    /**
     * 复制项目
     * @param {number} projectId - 项目ID
     * @param {Object} options - 复制选项
     */
    async duplicateProject(projectId, options = {}) {
        const response = await fetch(`${this.baseURL}/projects/${projectId}/duplicate`, {
            method: 'POST',
            headers: this.getAuthHeaders(),
            body: JSON.stringify(options)
        });

        return this.handleResponse(response);
    }

    /**
     * 导出项目数据
     * @param {number} projectId - 项目ID
     * @param {string} format - 导出格式 (excel, pdf, csv)
     */
    async exportProject(projectId, format = 'excel') {
        const response = await fetch(`${this.baseURL}/projects/${projectId}/export?format=${format}`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem(APP_CONFIG.TOKEN_KEY)}`
            }
        });

        if (!response.ok) {
            throw new Error('导出失败');
        }

        return response.blob();
    }

    /**
     * 搜索项目
     * @param {string} query - 搜索关键词
     * @param {Object} filters - 筛选条件
     */
    async searchProjects(query, filters = {}) {
        const params = new URLSearchParams({
            q: query,
            ...filters
        });
        
        const response = await fetch(`${this.baseURL}/projects/search?${params}`, {
            method: 'GET',
            headers: this.getAuthHeaders()
        });

        return this.handleResponse(response);
    }

    /**
     * 获取我参与的项目
     */
    async getMyProjects() {
        const response = await fetch(`${this.baseURL}/projects/my`, {
            method: 'GET',
            headers: this.getAuthHeaders()
        });

        return this.handleResponse(response);
    }

    /**
     * 获取我创建的项目
     */
    async getMyCreatedProjects() {
        const response = await fetch(`${this.baseURL}/projects/created`, {
            method: 'GET',
            headers: this.getAuthHeaders()
        });

        return this.handleResponse(response);
    }

    /**
     * 获取项目模板
     */
    async getProjectTemplates() {
        const response = await fetch(`${this.baseURL}/projects/templates`, {
            method: 'GET',
            headers: this.getAuthHeaders()
        });

        return this.handleResponse(response);
    }

    /**
     * 从模板创建项目
     * @param {number} templateId - 模板ID
     * @param {Object} projectData - 项目数据
     */
    async createProjectFromTemplate(templateId, projectData) {
        const response = await fetch(`${this.baseURL}/projects/templates/${templateId}/create`, {
            method: 'POST',
            headers: this.getAuthHeaders(),
            body: JSON.stringify(projectData)
        });

        return this.handleResponse(response);
    }

    /**
     * 保存项目为模板
     * @param {number} projectId - 项目ID
     * @param {Object} templateData - 模板数据
     */
    async saveAsTemplate(projectId, templateData) {
        const response = await fetch(`${this.baseURL}/projects/${projectId}/save-as-template`, {
            method: 'POST',
            headers: this.getAuthHeaders(),
            body: JSON.stringify(templateData)
        });

        return this.handleResponse(response);
    }

    /**
     * 获取项目活动日志
     * @param {number} projectId - 项目ID
     * @param {Object} params - 查询参数
     */
    async getProjectActivities(projectId, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = `${this.baseURL}/projects/${projectId}/activities${queryString ? '?' + queryString : ''}`;
        
        const response = await fetch(url, {
            method: 'GET',
            headers: this.getAuthHeaders()
        });

        return this.handleResponse(response);
    }

    /**
     * 批量操作项目
     * @param {Array} projectIds - 项目ID数组
     * @param {string} action - 操作类型
     * @param {Object} data - 操作数据
     */
    async batchOperation(projectIds, action, data = {}) {
        const response = await fetch(`${this.baseURL}/projects/batch`, {
            method: 'POST',
            headers: this.getAuthHeaders(),
            body: JSON.stringify({
                projectIds,
                action,
                data
            })
        });

        return this.handleResponse(response);
    }

    /**
     * 获取项目甘特图数据
     * @param {number} projectId - 项目ID
     */
    async getGanttData(projectId) {
        const response = await fetch(`${this.baseURL}/projects/${projectId}/gantt`, {
            method: 'GET',
            headers: this.getAuthHeaders()
        });

        return this.handleResponse(response);
    }

    /**
     * 获取项目看板数据
     * @param {number} projectId - 项目ID
     */
    async getKanbanData(projectId) {
        const response = await fetch(`${this.baseURL}/projects/${projectId}/kanban`, {
            method: 'GET',
            headers: this.getAuthHeaders()
        });

        return this.handleResponse(response);
    }
}

// 创建全局实例
const projectAPI = new ProjectAPI();

// 导出到全局
window.ProjectAPI = ProjectAPI;
window.projectAPI = projectAPI;

// 兼容性导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ProjectAPI;
}