/**
 * 审核管理API接口
 * 处理审核相关的数据交互
 */

class ApprovalsAPI {
    constructor() {
        this.baseURL = APP_CONFIG.API_BASE_URL;
    }

    /**
     * 获取认证头
     */
    getAuthHeaders() {
        const token = localStorage.getItem(APP_CONFIG.TOKEN_KEY);
        return {
            'Content-Type': 'application/json',
            'Authorization': `Bear<PERSON> ${token}`
        };
    }

    /**
     * 处理API响应
     */
    async handleResponse(response) {
        if (!response.ok) {
            const error = await response.json().catch(() => ({ message: '请求失败' }));
            throw new Error(error.message || `HTTP ${response.status}`);
        }

        return await response.json();
    }

    /**
     * 获取审核列表
     * @param {Object} params 查询参数
     * @returns {Promise<Object>} 审核列表数据
     */
    async getApprovals(params = {}) {
        try {
            const queryParams = new URLSearchParams();
            
            // 添加分页参数
            if (params.page !== undefined) queryParams.append('page', params.page);
            if (params.size !== undefined) queryParams.append('size', params.size);
            
            // 添加筛选参数
            if (params.type) queryParams.append('type', params.type);
            if (params.status) queryParams.append('status', params.status);
            if (params.requesterId) queryParams.append('requesterId', params.requesterId);
            if (params.startDate) queryParams.append('startDate', params.startDate);
            if (params.endDate) queryParams.append('endDate', params.endDate);
            if (params.search) queryParams.append('search', params.search);
            
            const url = `/api/approvals?${queryParams.toString()}`;
            const response = await fetch(`${this.baseURL}${url}`, {
                method: 'GET',
                headers: this.getAuthHeaders()
            });
            
            return await this.handleResponse(response);
        } catch (error) {
            console.error('获取审核列表失败:', error);
            throw error;
        }
    }

    /**
     * 获取审核详情
     * @param {number} id 审核ID
     * @returns {Promise<Object>} 审核详情数据
     */
    async getApprovalDetail(id) {
        try {
            const url = `/api/approvals/${id}`;
            const response = await fetch(`${this.baseURL}${url}`, {
                method: 'GET',
                headers: this.getAuthHeaders()
            });
            
            return await this.handleResponse(response);
        } catch (error) {
            console.error(`获取审核详情失败 (ID: ${id}):`, error);
            throw error;
        }
    }
    
    /**
     * 批准审核
     * @param {number} id 审核ID
     * @param {string} comment 审批意见
     * @returns {Promise<Object>} 审批结果
     */
    async approveApproval(id, comment = '') {
        try {
            const url = `/api/approvals/${id}/approve`;
            const response = await fetch(`${this.baseURL}${url}`, {
                method: 'POST',
                headers: this.getAuthHeaders(),
                body: JSON.stringify({ comment })
            });
            
            return await this.handleResponse(response);
        } catch (error) {
            console.error(`批准审核失败 (ID: ${id}):`, error);
            throw error;
        }
    }
    
    /**
     * 拒绝审核
     * @param {number} id 审核ID
     * @param {string} reason 拒绝理由
     * @returns {Promise<Object>} 拒绝结果
     */
    async rejectApproval(id, reason) {
        try {
            const url = `/api/approvals/${id}/reject`;
            const response = await fetch(`${this.baseURL}${url}`, {
                method: 'POST',
                headers: this.getAuthHeaders(),
                body: JSON.stringify({ reason })
            });
            
            return await this.handleResponse(response);
        } catch (error) {
            console.error(`拒绝审核失败 (ID: ${id}):`, error);
            throw error;
        }
    }
    
    /**
     * 批量审批
     * @param {Array<number>} ids 审核ID数组
     * @param {string} action 操作类型：approve/reject
     * @param {string} comment 审批意见或拒绝理由
     * @returns {Promise<Object>} 批量操作结果
     */
    async batchProcess(ids, action, comment = '') {
        try {
            const url = `/api/approvals/batch/${action}`;
            const response = await fetch(`${this.baseURL}${url}`, {
                method: 'POST',
                headers: this.getAuthHeaders(),
                body: JSON.stringify({ ids, comment })
            });
            
            return await this.handleResponse(response);
        } catch (error) {
            console.error(`批量${action === 'approve' ? '审批' : '拒绝'}失败:`, error);
            throw error;
        }
    }
    
    /**
     * 获取审核统计数据
     * @returns {Promise<Object>} 统计数据
     */
    async getApprovalStats() {
        try {
            const url = '/api/approvals/stats';
            const response = await fetch(`${this.baseURL}${url}`, {
                method: 'GET',
                headers: this.getAuthHeaders()
            });
            
            return await this.handleResponse(response);
        } catch (error) {
            console.error('获取审核统计数据失败:', error);
            throw error;
        }
    }
    
    /**
     * 提交审核申请
     * @param {Object} data 审核申请数据
     * @returns {Promise<Object>} 提交结果
     */
    async submitApproval(data) {
        try {
            const url = '/api/approvals';
            const response = await fetch(`${this.baseURL}${url}`, {
                method: 'POST',
                headers: this.getAuthHeaders(),
                body: JSON.stringify(data)
            });
            
            return await this.handleResponse(response);
        } catch (error) {
            console.error('提交审核申请失败:', error);
            throw error;
        }
    }
}

// 创建API实例
const approvalsAPI = new ApprovalsAPI();

// 导出API实例
if (typeof module !== 'undefined' && module.exports) {
    module.exports = approvalsAPI;
} else {
    window.approvalsAPI = approvalsAPI;
}