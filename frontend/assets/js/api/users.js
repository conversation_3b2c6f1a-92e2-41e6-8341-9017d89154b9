/**
 * 用户管理API接口
 */

class UserAPI {
    constructor() {
        this.baseURL = APP_CONFIG.API_BASE_URL;
        this.timeout = APP_CONFIG.API_TIMEOUT;
    }

    /**
     * 获取认证头
     */
    getAuthHeaders() {
        const token = localStorage.getItem(APP_CONFIG.TOKEN_KEY);
        return {
            'Content-Type': 'application/json',
            'Authorization': token ? `Bearer ${token}` : ''
        };
    }

    /**
     * 处理API响应
     */
    async handleResponse(response) {
        if (response.status === 401) {
            // Token过期，尝试刷新
            const authAPI = new AuthAPI();
            const refreshed = await authAPI.refreshToken();
            if (!refreshed) {
                window.location.href = 'login.html';
                throw new Error('认证失败');
            }
            // 重新发起请求
            throw new Error('TOKEN_REFRESHED');
        }

        if (!response.ok) {
            const error = await response.json().catch(() => ({ message: '请求失败' }));
            throw new Error(error.message || `HTTP ${response.status}`);
        }

        return await response.json();
    }

    /**
     * 发送请求
     */
    async request(url, options = {}) {
        const config = {
            headers: this.getAuthHeaders(),
            ...options
        };

        try {
            const response = await fetch(`${this.baseURL}${url}`, config);
            return await this.handleResponse(response);
        } catch (error) {
            if (error.message === 'TOKEN_REFRESHED') {
                // Token已刷新，重新发起请求
                config.headers = this.getAuthHeaders();
                const response = await fetch(`${this.baseURL}${url}`, config);
                return await this.handleResponse(response);
            }
            throw error;
        }
    }

    /**
     * 获取用户列表
     */
    async getUsers(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = `/users${queryString ? `?${queryString}` : ''}`;
        return await this.request(url);
    }

    /**
     * 获取用户详情
     */
    async getUser(userId) {
        return await this.request(`/users/${userId}`);
    }

    /**
     * 创建用户
     */
    async createUser(userData) {
        return await this.request('/users', {
            method: 'POST',
            body: JSON.stringify(userData)
        });
    }

    /**
     * 更新用户
     */
    async updateUser(userId, userData) {
        return await this.request(`/users/${userId}`, {
            method: 'PUT',
            body: JSON.stringify(userData)
        });
    }

    /**
     * 删除用户
     */
    async deleteUser(userId) {
        return await this.request(`/users/${userId}`, {
            method: 'DELETE'
        });
    }

    /**
     * 批量删除用户
     */
    async batchDeleteUsers(userIds) {
        return await this.request('/users/batch', {
            method: 'DELETE',
            body: JSON.stringify({ user_ids: userIds })
        });
    }

    /**
     * 更新用户状态
     */
    async updateUserStatus(userId, status) {
        return await this.request(`/users/${userId}/status`, {
            method: 'PATCH',
            body: JSON.stringify({ status })
        });
    }

    /**
     * 切换用户状态
     */
    async toggleUserStatus(userId) {
        return await this.request(`/users/${userId}/toggle-status`, {
            method: 'PATCH'
        });
    }

    /**
     * 重置用户密码
     */
    async resetUserPassword(userId, newPassword = null) {
        const body = newPassword ? { new_password: newPassword } : {};
        return await this.request(`/users/${userId}/reset-password`, {
            method: 'POST',
            body: JSON.stringify(body)
        });
    }

    /**
     * 修改用户密码
     */
    async changeUserPassword(userId, oldPassword, newPassword) {
        return await this.request(`/users/${userId}/change-password`, {
            method: 'POST',
            body: JSON.stringify({
                old_password: oldPassword,
                new_password: newPassword
            })
        });
    }

    /**
     * 更新用户角色
     */
    async updateUserRole(userId, role) {
        return await this.request(`/users/${userId}/role`, {
            method: 'PATCH',
            body: JSON.stringify({ role })
        });
    }

    /**
     * 批量更新用户角色
     */
    async batchUpdateUserRole(userIds, role) {
        return await this.request('/users/batch/role', {
            method: 'PATCH',
            body: JSON.stringify({
                user_ids: userIds,
                role: role
            })
        });
    }

    /**
     * 获取用户统计信息
     */
    async getUserStatistics(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = `/users/statistics${queryString ? `?${queryString}` : ''}`;
        return await this.request(url);
    }

    /**
     * 搜索用户
     */
    async searchUsers(query, params = {}) {
        const searchParams = { q: query, ...params };
        const queryString = new URLSearchParams(searchParams).toString();
        return await this.request(`/users/search?${queryString}`);
    }

    /**
     * 获取用户活动日志
     */
    async getUserActivityLog(userId, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = `/users/${userId}/activity${queryString ? `?${queryString}` : ''}`;
        return await this.request(url);
    }

    /**
     * 获取用户权限
     */
    async getUserPermissions(userId) {
        return await this.request(`/users/${userId}/permissions`);
    }

    /**
     * 更新用户权限
     */
    async updateUserPermissions(userId, permissions) {
        return await this.request(`/users/${userId}/permissions`, {
            method: 'PUT',
            body: JSON.stringify({ permissions })
        });
    }

    /**
     * 获取用户所属项目
     */
    async getUserProjects(userId, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = `/users/${userId}/projects${queryString ? `?${queryString}` : ''}`;
        return await this.request(url);
    }

    /**
     * 获取用户任务
     */
    async getUserTasks(userId, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = `/users/${userId}/tasks${queryString ? `?${queryString}` : ''}`;
        return await this.request(url);
    }

    /**
     * 获取用户工作量统计
     */
    async getUserWorkload(userId, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = `/users/${userId}/workload${queryString ? `?${queryString}` : ''}`;
        return await this.request(url);
    }

    /**
     * 上传用户头像
     */
    async uploadUserAvatar(userId, file) {
        const formData = new FormData();
        formData.append('avatar', file);

        const token = localStorage.getItem(APP_CONFIG.TOKEN_KEY);
        const headers = {
            'Authorization': token ? `Bearer ${token}` : ''
        };

        try {
            const response = await fetch(`${this.baseURL}/users/${userId}/avatar`, {
                method: 'POST',
                headers,
                body: formData
            });
            return await this.handleResponse(response);
        } catch (error) {
            if (error.message === 'TOKEN_REFRESHED') {
                const newToken = localStorage.getItem(APP_CONFIG.TOKEN_KEY);
                headers.Authorization = `Bearer ${newToken}`;
                const response = await fetch(`${this.baseURL}/users/${userId}/avatar`, {
                    method: 'POST',
                    headers,
                    body: formData
                });
                return await this.handleResponse(response);
            }
            throw error;
        }
    }

    /**
     * 删除用户头像
     */
    async deleteUserAvatar(userId) {
        return await this.request(`/users/${userId}/avatar`, {
            method: 'DELETE'
        });
    }

    /**
     * 获取部门列表
     */
    async getDepartments(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = `/departments${queryString ? `?${queryString}` : ''}`;
        return await this.request(url);
    }

    /**
     * 创建部门
     */
    async createDepartment(departmentData) {
        return await this.request('/departments', {
            method: 'POST',
            body: JSON.stringify(departmentData)
        });
    }

    /**
     * 更新部门
     */
    async updateDepartment(departmentId, departmentData) {
        return await this.request(`/departments/${departmentId}`, {
            method: 'PUT',
            body: JSON.stringify(departmentData)
        });
    }

    /**
     * 删除部门
     */
    async deleteDepartment(departmentId) {
        return await this.request(`/departments/${departmentId}`, {
            method: 'DELETE'
        });
    }

    /**
     * 获取部门用户
     */
    async getDepartmentUsers(departmentId, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = `/departments/${departmentId}/users${queryString ? `?${queryString}` : ''}`;
        return await this.request(url);
    }

    /**
     * 将用户分配到部门
     */
    async assignUserToDepartment(userId, departmentId) {
        return await this.request(`/users/${userId}/department`, {
            method: 'PATCH',
            body: JSON.stringify({ department_id: departmentId })
        });
    }

    /**
     * 批量分配用户到部门
     */
    async batchAssignUsersToDepartment(userIds, departmentId) {
        return await this.request('/users/batch/department', {
            method: 'PATCH',
            body: JSON.stringify({
                user_ids: userIds,
                department_id: departmentId
            })
        });
    }

    /**
     * 从部门移除用户
     */
    async removeUserFromDepartment(userId) {
        return await this.request(`/users/${userId}/department`, {
            method: 'DELETE'
        });
    }

    /**
     * 获取角色列表
     */
    async getRoles() {
        return await this.request('/roles');
    }

    /**
     * 获取权限列表
     */
    async getPermissions() {
        return await this.request('/permissions');
    }

    /**
     * 获取用户登录历史
     */
    async getUserLoginHistory(userId, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = `/users/${userId}/login-history${queryString ? `?${queryString}` : ''}`;
        return await this.request(url);
    }

    /**
     * 获取在线用户
     */
    async getOnlineUsers(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = `/users/online${queryString ? `?${queryString}` : ''}`;
        return await this.request(url);
    }

    /**
     * 强制用户下线
     */
    async forceUserOffline(userId) {
        return await this.request(`/users/${userId}/force-offline`, {
            method: 'POST'
        });
    }

    /**
     * 锁定用户
     */
    async lockUser(userId, reason = '') {
        return await this.request(`/users/${userId}/lock`, {
            method: 'POST',
            body: JSON.stringify({ reason })
        });
    }

    /**
     * 解锁用户
     */
    async unlockUser(userId) {
        return await this.request(`/users/${userId}/unlock`, {
            method: 'POST'
        });
    }

    /**
     * 发送用户通知
     */
    async sendUserNotification(userId, notification) {
        return await this.request(`/users/${userId}/notifications`, {
            method: 'POST',
            body: JSON.stringify(notification)
        });
    }

    /**
     * 批量发送通知
     */
    async batchSendNotification(userIds, notification) {
        return await this.request('/users/batch/notifications', {
            method: 'POST',
            body: JSON.stringify({
                user_ids: userIds,
                notification: notification
            })
        });
    }

    /**
     * 获取用户设置
     */
    async getUserSettings(userId) {
        return await this.request(`/users/${userId}/settings`);
    }

    /**
     * 更新用户设置
     */
    async updateUserSettings(userId, settings) {
        return await this.request(`/users/${userId}/settings`, {
            method: 'PUT',
            body: JSON.stringify(settings)
        });
    }

    /**
     * 获取用户偏好设置
     */
    async getUserPreferences(userId) {
        return await this.request(`/users/${userId}/preferences`);
    }

    /**
     * 更新用户偏好设置
     */
    async updateUserPreferences(userId, preferences) {
        return await this.request(`/users/${userId}/preferences`, {
            method: 'PUT',
            body: JSON.stringify(preferences)
        });
    }

    /**
     * 验证用户邮箱
     */
    async verifyUserEmail(userId, verificationCode) {
        return await this.request(`/users/${userId}/verify-email`, {
            method: 'POST',
            body: JSON.stringify({ verification_code: verificationCode })
        });
    }

    /**
     * 重新发送邮箱验证
     */
    async resendEmailVerification(userId) {
        return await this.request(`/users/${userId}/resend-verification`, {
            method: 'POST'
        });
    }

    /**
     * 验证用户手机号
     */
    async verifyUserPhone(userId, verificationCode) {
        return await this.request(`/users/${userId}/verify-phone`, {
            method: 'POST',
            body: JSON.stringify({ verification_code: verificationCode })
        });
    }

    /**
     * 发送手机验证码
     */
    async sendPhoneVerification(userId) {
        return await this.request(`/users/${userId}/send-phone-verification`, {
            method: 'POST'
        });
    }

    /**
     * 启用/禁用用户双因素认证
     */
    async toggleUserTwoFactor(userId, enabled) {
        return await this.request(`/users/${userId}/two-factor`, {
            method: 'PATCH',
            body: JSON.stringify({ enabled })
        });
    }

    /**
     * 获取用户双因素认证设置
     */
    async getUserTwoFactorSettings(userId) {
        return await this.request(`/users/${userId}/two-factor`);
    }

    /**
     * 导出用户数据
     */
    async exportUsers(params = {}, format = 'excel') {
        const queryString = new URLSearchParams({ ...params, format }).toString();
        const url = `/users/export?${queryString}`;
        
        const token = localStorage.getItem(APP_CONFIG.TOKEN_KEY);
        const response = await fetch(`${this.baseURL}${url}`, {
            headers: {
                'Authorization': token ? `Bearer ${token}` : ''
            }
        });
        
        if (!response.ok) {
            throw new Error('导出失败');
        }
        
        return response.blob();
    }

    /**
     * 导入用户数据
     */
    async importUsers(file, options = {}) {
        const formData = new FormData();
        formData.append('file', file);
        
        // 添加导入选项
        Object.keys(options).forEach(key => {
            formData.append(key, options[key]);
        });

        const token = localStorage.getItem(APP_CONFIG.TOKEN_KEY);
        const headers = {
            'Authorization': token ? `Bearer ${token}` : ''
        };

        try {
            const response = await fetch(`${this.baseURL}/users/import`, {
                method: 'POST',
                headers,
                body: formData
            });
            return await this.handleResponse(response);
        } catch (error) {
            if (error.message === 'TOKEN_REFRESHED') {
                const newToken = localStorage.getItem(APP_CONFIG.TOKEN_KEY);
                headers.Authorization = `Bearer ${newToken}`;
                const response = await fetch(`${this.baseURL}/users/import`, {
                    method: 'POST',
                    headers,
                    body: formData
                });
                return await this.handleResponse(response);
            }
            throw error;
        }
    }

    /**
     * 获取用户导入模板
     */
    async getUserImportTemplate(format = 'excel') {
        const url = `/users/import-template?format=${format}`;
        
        const token = localStorage.getItem(APP_CONFIG.TOKEN_KEY);
        const response = await fetch(`${this.baseURL}${url}`, {
            headers: {
                'Authorization': token ? `Bearer ${token}` : ''
            }
        });
        
        if (!response.ok) {
            throw new Error('获取模板失败');
        }
        
        return response.blob();
    }

    /**
     * 获取用户报告
     */
    async getUserReport(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = `/users/report${queryString ? `?${queryString}` : ''}`;
        return await this.request(url);
    }

    /**
     * 获取用户绩效统计
     */
    async getUserPerformanceStats(userId, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = `/users/${userId}/performance${queryString ? `?${queryString}` : ''}`;
        return await this.request(url);
    }

    /**
     * 获取团队统计
     */
    async getTeamStats(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = `/users/team-stats${queryString ? `?${queryString}` : ''}`;
        return await this.request(url);
    }

    /**
     * 获取用户时间线
     */
    async getUserTimeline(userId, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = `/users/${userId}/timeline${queryString ? `?${queryString}` : ''}`;
        return await this.request(url);
    }

    /**
     * 获取用户标签
     */
    async getUserTags(userId) {
        return await this.request(`/users/${userId}/tags`);
    }

    /**
     * 添加用户标签
     */
    async addUserTag(userId, tag) {
        return await this.request(`/users/${userId}/tags`, {
            method: 'POST',
            body: JSON.stringify({ tag })
        });
    }

    /**
     * 删除用户标签
     */
    async removeUserTag(userId, tagId) {
        return await this.request(`/users/${userId}/tags/${tagId}`, {
            method: 'DELETE'
        });
    }

    /**
     * 获取用户备注
     */
    async getUserNotes(userId) {
        return await this.request(`/users/${userId}/notes`);
    }

    /**
     * 添加用户备注
     */
    async addUserNote(userId, note) {
        return await this.request(`/users/${userId}/notes`, {
            method: 'POST',
            body: JSON.stringify({ note })
        });
    }

    /**
     * 更新用户备注
     */
    async updateUserNote(userId, noteId, note) {
        return await this.request(`/users/${userId}/notes/${noteId}`, {
            method: 'PUT',
            body: JSON.stringify({ note })
        });
    }

    /**
     * 删除用户备注
     */
    async deleteUserNote(userId, noteId) {
        return await this.request(`/users/${userId}/notes/${noteId}`, {
            method: 'DELETE'
        });
    }

    /**
     * 获取用户关注列表
     */
    async getUserFollowing(userId, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = `/users/${userId}/following${queryString ? `?${queryString}` : ''}`;
        return await this.request(url);
    }

    /**
     * 获取用户粉丝列表
     */
    async getUserFollowers(userId, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = `/users/${userId}/followers${queryString ? `?${queryString}` : ''}`;
        return await this.request(url);
    }

    /**
     * 关注用户
     */
    async followUser(userId) {
        return await this.request(`/users/${userId}/follow`, {
            method: 'POST'
        });
    }

    /**
     * 取消关注用户
     */
    async unfollowUser(userId) {
        return await this.request(`/users/${userId}/unfollow`, {
            method: 'POST'
        });
    }

    /**
     * 检查是否关注用户
     */
    async checkUserFollowing(userId) {
        return await this.request(`/users/${userId}/is-following`);
    }
}

// 导出到全局
window.UserAPI = UserAPI;

// 兼容性导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UserAPI;
}