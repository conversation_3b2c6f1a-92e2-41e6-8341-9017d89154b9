/**
 * 认证API接口
 */

class AuthAPI {
    constructor() {
        this.baseURL = APP_CONFIG.API_BASE_URL;
        this.timeout = APP_CONFIG.API_TIMEOUT;
    }

    /**
     * 获取基础请求头
     */
    getBaseHeaders() {
        return {
            'Content-Type': 'application/json'
        };
    }

    /**
     * 获取认证头
     */
    getAuthHeaders() {
        const token = localStorage.getItem(APP_CONFIG.TOKEN_KEY);
        return {
            ...this.getBaseHeaders(),
            'Authorization': token ? `Bearer ${token}` : ''
        };
    }

    /**
     * 处理API响应
     */
    async handleResponse(response) {
        if (!response.ok) {
            const error = await response.json().catch(() => ({ message: '请求失败' }));
            throw new Error(error.message || `HTTP ${response.status}`);
        }

        return await response.json();
    }

    /**
     * 发送请求
     */
    async request(url, options = {}) {
        const config = {
            headers: this.getBaseHeaders(),
            ...options
        };

        const response = await fetch(`${this.baseURL}${url}`, config);
        return await this.handleResponse(response);
    }

    /**
     * 发送认证请求
     */
    async authRequest(url, options = {}) {
        const config = {
            headers: this.getAuthHeaders(),
            ...options
        };

        const response = await fetch(`${this.baseURL}${url}`, config);
        return await this.handleResponse(response);
    }

    /**
     * 用户登录
     */
    async login(credentials) {
        const response = await this.request('/auth/login', {
            method: 'POST',
            body: JSON.stringify(credentials)
        });

        if (response.success && response.data) {
            // 保存认证信息
            localStorage.setItem(APP_CONFIG.TOKEN_KEY, response.data.access_token);
            if (response.data.refresh_token) {
                localStorage.setItem(APP_CONFIG.REFRESH_TOKEN_KEY, response.data.refresh_token);
            }
            if (response.data.user) {
                localStorage.setItem(APP_CONFIG.USER_KEY, JSON.stringify(response.data.user));
            }
        }

        return response;
    }

    /**
     * 用户注册
     */
    async register(userData) {
        return await this.request('/auth/register', {
            method: 'POST',
            body: JSON.stringify(userData)
        });
    }

    /**
     * 用户登出
     */
    async logout() {
        try {
            await this.authRequest('/auth/logout', {
                method: 'POST'
            });
        } catch (error) {
            console.warn('登出请求失败:', error.message);
        } finally {
            // 清除本地存储
            this.clearAuthData();
        }
    }

    /**
     * 刷新Token
     */
    async refreshToken() {
        const refreshToken = localStorage.getItem(APP_CONFIG.REFRESH_TOKEN_KEY);
        if (!refreshToken) {
            this.clearAuthData();
            return false;
        }

        try {
            const response = await this.request('/auth/refresh', {
                method: 'POST',
                body: JSON.stringify({ refresh_token: refreshToken })
            });

            if (response.success && response.data) {
                localStorage.setItem(APP_CONFIG.TOKEN_KEY, response.data.access_token);
                if (response.data.refresh_token) {
                    localStorage.setItem(APP_CONFIG.REFRESH_TOKEN_KEY, response.data.refresh_token);
                }
                return true;
            }
        } catch (error) {
            console.error('刷新Token失败:', error.message);
        }

        this.clearAuthData();
        return false;
    }

    /**
     * 验证Token
     */
    async verifyToken() {
        const token = localStorage.getItem(APP_CONFIG.TOKEN_KEY);
        if (!token) {
            return false;
        }

        try {
            const response = await this.authRequest('/auth/verify');
            return response.success;
        } catch (error) {
            console.error('Token验证失败:', error.message);
            // 尝试刷新Token
            return await this.refreshToken();
        }
    }

    /**
     * 获取当前用户信息
     */
    async getCurrentUser() {
        try {
            const response = await this.authRequest('/auth/me');
            if (response.success && response.data) {
                localStorage.setItem(APP_CONFIG.USER_KEY, JSON.stringify(response.data));
                return response.data;
            }
        } catch (error) {
            console.error('获取用户信息失败:', error.message);
            throw error;
        }
    }

    /**
     * 更新用户资料
     */
    async updateProfile(profileData) {
        const response = await this.authRequest('/auth/profile', {
            method: 'PUT',
            body: JSON.stringify(profileData)
        });

        if (response.success && response.data) {
            localStorage.setItem(APP_CONFIG.USER_KEY, JSON.stringify(response.data));
        }

        return response;
    }

    /**
     * 修改密码
     */
    async changePassword(passwordData) {
        return await this.authRequest('/auth/change-password', {
            method: 'POST',
            body: JSON.stringify(passwordData)
        });
    }

    /**
     * 忘记密码
     */
    async forgotPassword(email) {
        return await this.request('/auth/forgot-password', {
            method: 'POST',
            body: JSON.stringify({ email })
        });
    }

    /**
     * 重置密码
     */
    async resetPassword(resetData) {
        return await this.request('/auth/reset-password', {
            method: 'POST',
            body: JSON.stringify(resetData)
        });
    }

    /**
     * 上传头像
     */
    async uploadAvatar(file) {
        const formData = new FormData();
        formData.append('avatar', file);

        const token = localStorage.getItem(APP_CONFIG.TOKEN_KEY);
        const headers = {
            'Authorization': token ? `Bearer ${token}` : ''
        };

        const response = await fetch(`${this.baseURL}/auth/avatar`, {
            method: 'POST',
            headers,
            body: formData
        });

        const result = await this.handleResponse(response);
        if (result.success && result.data) {
            localStorage.setItem(APP_CONFIG.USER_KEY, JSON.stringify(result.data));
        }

        return result;
    }

    /**
     * 清除认证数据
     */
    clearAuthData() {
        localStorage.removeItem(APP_CONFIG.TOKEN_KEY);
        localStorage.removeItem(APP_CONFIG.REFRESH_TOKEN_KEY);
        localStorage.removeItem(APP_CONFIG.USER_KEY);
    }

    /**
     * 检查是否已登录
     */
    isAuthenticated() {
        return !!localStorage.getItem(APP_CONFIG.TOKEN_KEY);
    }

    /**
     * 获取当前用户（从本地存储）
     */
    getCurrentUserFromStorage() {
        const userStr = localStorage.getItem(APP_CONFIG.USER_KEY);
        return userStr ? JSON.parse(userStr) : null;
    }

    /**
     * 获取Token（从本地存储）
     */
    getTokenFromStorage() {
        return localStorage.getItem(APP_CONFIG.TOKEN_KEY);
    }

    /**
     * 获取刷新Token（从本地存储）
     */
    getRefreshTokenFromStorage() {
        return localStorage.getItem(APP_CONFIG.REFRESH_TOKEN_KEY);
    }
}

// 导出到全局
window.AuthAPI = AuthAPI;

// 兼容性导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AuthAPI;
}