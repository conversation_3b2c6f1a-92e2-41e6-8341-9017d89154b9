/**
 * 审核管理页面脚本
 */

class ApprovalManager {
    constructor() {
        this.approvals = [];
        this.filteredApprovals = [];
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.totalPages = 1;
        this.selectedApprovals = new Set();
        this.filters = {
            search: '',
            type: 'all',
            status: 'all',
            applicant: 'all',
            dateRange: 'all'
        };
        this.dateRangeStart = null;
        this.dateRangeEnd = null;
        this.stats = {
            pending: 0,
            approved: 0,
            rejected: 0,
            todayProcessed: 0
        };
    }

    /**
     * 初始化审核管理页面
     */
    async init() {
        // 检查用户是否已认证
        if (!appState.checkAuth()) {
            appState.redirectToLogin();
            return;
        }

        // 检查用户权限
        if (!this.checkPermission()) {
            this.showPermissionDenied();
            return;
        }

        // 设置事件监听器
        this.setupEventListeners();

        // 加载审核数据
        await this.loadApprovals();

        // 更新统计数据
        this.updateStats();

        // 渲染审核列表
        this.renderApprovals();

        // 初始化日期选择器
        this.initDatePickers();
    }

    /**
     * 检查用户是否有权限访问审核管理页面
     * @returns {boolean} 是否有权限
     */
    checkPermission() {
        const user = appState.getUser();
        // 只有管理员和有审核权限的用户可以访问
        return user && (user.role === 'admin' || user.role === 'manager' || user.permissions?.includes('approval_manage'));
    }

    /**
     * 显示权限不足提示
     */
    showPermissionDenied() {
        const container = document.getElementById('approvals-container');
        container.innerHTML = `
            <div class="alert alert-danger mt-4" role="alert">
                <h4 class="alert-heading">权限不足</h4>
                <p>您没有访问审核管理页面的权限。如需访问，请联系系统管理员。</p>
            </div>
        `;
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 搜索框
        document.getElementById('approval-search').addEventListener('input', (e) => {
            this.filters.search = e.target.value.trim();
            this.currentPage = 1;
            this.applyFilters();
        });

        // 审核类型筛选
        document.getElementById('filter-type').addEventListener('change', (e) => {
            this.filters.type = e.target.value;
            this.currentPage = 1;
            this.applyFilters();
        });

        // 状态筛选
        document.getElementById('filter-status').addEventListener('change', (e) => {
            this.filters.status = e.target.value;
            this.currentPage = 1;
            this.applyFilters();
        });

        // 申请人筛选
        document.getElementById('filter-applicant').addEventListener('change', (e) => {
            this.filters.applicant = e.target.value;
            this.currentPage = 1;
            this.applyFilters();
        });

        // 日期范围筛选
        document.getElementById('filter-date-range').addEventListener('change', (e) => {
            this.filters.dateRange = e.target.value;
            this.currentPage = 1;
            this.handleDateRangeChange();
        });

        // 自定义日期范围变化
        document.getElementById('date-range-start').addEventListener('change', (e) => {
            this.dateRangeStart = e.target.value ? new Date(e.target.value) : null;
            if (this.filters.dateRange === 'custom') {
                this.currentPage = 1;
                this.applyFilters();
            }
        });

        document.getElementById('date-range-end').addEventListener('change', (e) => {
            this.dateRangeEnd = e.target.value ? new Date(e.target.value) : null;
            if (this.filters.dateRange === 'custom') {
                this.currentPage = 1;
                this.applyFilters();
            }
        });

        // 重置筛选
        document.getElementById('reset-filters').addEventListener('click', () => {
            this.resetFilters();
        });

        // 全选/取消全选
        document.getElementById('select-all').addEventListener('change', (e) => {
            const isChecked = e.target.checked;
            const checkboxes = document.querySelectorAll('.approval-checkbox');
            
            checkboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
                const approvalId = checkbox.getAttribute('data-id');
                
                if (isChecked) {
                    this.selectedApprovals.add(approvalId);
                } else {
                    this.selectedApprovals.delete(approvalId);
                }
            });
            
            this.updateBulkActionButton();
        });

        // 批量审核按钮
        document.getElementById('bulk-approve').addEventListener('click', () => {
            if (this.selectedApprovals.size > 0) {
                this.showBulkApproveModal();
            }
        });

        // 批量拒绝按钮
        document.getElementById('bulk-reject').addEventListener('click', () => {
            if (this.selectedApprovals.size > 0) {
                this.showBulkRejectModal();
            }
        });

        // 批量审核确认
        document.getElementById('confirm-bulk-approve').addEventListener('click', () => {
            this.processBulkApprove();
        });

        // 批量拒绝确认
        document.getElementById('confirm-bulk-reject').addEventListener('click', () => {
            const reason = document.getElementById('bulk-reject-reason').value.trim();
            this.processBulkReject(reason);
        });

        // 分页事件
        document.getElementById('pagination').addEventListener('click', (e) => {
            if (e.target.classList.contains('page-link')) {
                e.preventDefault();
                const page = e.target.getAttribute('data-page');
                if (page) {
                    this.goToPage(parseInt(page));
                }
            }
        });
    }

    /**
     * 初始化日期选择器
     */
    initDatePickers() {
        // 设置日期选择器的最大值为今天
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('date-range-start').setAttribute('max', today);
        document.getElementById('date-range-end').setAttribute('max', today);
        
        // 默认隐藏自定义日期范围选择器
        document.getElementById('custom-date-range').style.display = 'none';
    }

    /**
     * 处理日期范围变化
     */
    handleDateRangeChange() {
        const customDateRange = document.getElementById('custom-date-range');
        
        if (this.filters.dateRange === 'custom') {
            customDateRange.style.display = 'flex';
        } else {
            customDateRange.style.display = 'none';
            this.applyFilters();
        }
    }

    /**
     * 重置所有筛选条件
     */
    resetFilters() {
        // 重置筛选条件
        this.filters = {
            search: '',
            type: 'all',
            status: 'all',
            applicant: 'all',
            dateRange: 'all'
        };
        
        // 重置表单元素
        document.getElementById('approval-search').value = '';
        document.getElementById('filter-type').value = 'all';
        document.getElementById('filter-status').value = 'all';
        document.getElementById('filter-applicant').value = 'all';
        document.getElementById('filter-date-range').value = 'all';
        
        // 隐藏自定义日期范围
        document.getElementById('custom-date-range').style.display = 'none';
        document.getElementById('date-range-start').value = '';
        document.getElementById('date-range-end').value = '';
        
        this.dateRangeStart = null;
        this.dateRangeEnd = null;
        
        // 重置页码
        this.currentPage = 1;
        
        // 应用筛选
        this.applyFilters();
    }

    /**
     * 加载审核数据
     */
    async loadApprovals() {
        try {
            this.showLoading();
            
            // 调用API获取审核数据
            const response = await fetch(`${APP_CONFIG.API_BASE_URL}/api/approvals`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${appState.getToken()}`,
                    'Content-Type': 'application/json'
                }
            });
            
            if (!response.ok) {
                throw new Error('获取审核数据失败');
            }
            
            const data = await response.json();
            this.approvals = data.approvals || [];
            this.filteredApprovals = [...this.approvals];
            
            // 加载申请人列表
            this.loadApplicants();
            
            this.hideLoading();
        } catch (error) {
            console.error('加载审核数据出错:', error);
            this.showMessage('加载审核数据失败，请稍后重试', 'danger');
            this.hideLoading();
        }
    }

    /**
     * 加载申请人列表
     */
    loadApplicants() {
        // 获取所有不重复的申请人
        const applicants = [...new Set(this.approvals.map(approval => approval.applicant.id))];
        
        // 获取申请人筛选下拉框
        const applicantSelect = document.getElementById('filter-applicant');
        
        // 清空现有选项（保留"全部"选项）
        while (applicantSelect.options.length > 1) {
            applicantSelect.remove(1);
        }
        
        // 添加申请人选项
        applicants.forEach(applicantId => {
            const approval = this.approvals.find(a => a.applicant.id === applicantId);
            if (approval) {
                const option = document.createElement('option');
                option.value = applicantId;
                option.textContent = approval.applicant.name;
                applicantSelect.appendChild(option);
            }
        });
    }

    /**
     * 应用筛选条件
     */
    applyFilters() {
        // 复制原始数据
        let filtered = [...this.approvals];
        
        // 应用搜索筛选
        if (this.filters.search) {
            const searchTerm = this.filters.search.toLowerCase();
            filtered = filtered.filter(approval => 
                approval.id.toLowerCase().includes(searchTerm) ||
                approval.applicant.name.toLowerCase().includes(searchTerm) ||
                approval.target.name.toLowerCase().includes(searchTerm) ||
                (approval.description && approval.description.toLowerCase().includes(searchTerm))
            );
        }
        
        // 应用类型筛选
        if (this.filters.type !== 'all') {
            filtered = filtered.filter(approval => approval.type === this.filters.type);
        }
        
        // 应用状态筛选
        if (this.filters.status !== 'all') {
            filtered = filtered.filter(approval => approval.status === this.filters.status);
        }
        
        // 应用申请人筛选
        if (this.filters.applicant !== 'all') {
            filtered = filtered.filter(approval => approval.applicant.id === this.filters.applicant);
        }
        
        // 应用日期范围筛选
        if (this.filters.dateRange !== 'all') {
            const now = new Date();
            let startDate;
            
            switch (this.filters.dateRange) {
                case 'today':
                    startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                    filtered = filtered.filter(approval => {
                        const approvalDate = new Date(approval.createdAt);
                        return approvalDate >= startDate;
                    });
                    break;
                    
                case 'yesterday':
                    startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1);
                    const endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                    filtered = filtered.filter(approval => {
                        const approvalDate = new Date(approval.createdAt);
                        return approvalDate >= startDate && approvalDate < endDate;
                    });
                    break;
                    
                case 'thisWeek':
                    const dayOfWeek = now.getDay() || 7; // 如果是0（周日），则设为7
                    startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - dayOfWeek + 1);
                    filtered = filtered.filter(approval => {
                        const approvalDate = new Date(approval.createdAt);
                        return approvalDate >= startDate;
                    });
                    break;
                    
                case 'thisMonth':
                    startDate = new Date(now.getFullYear(), now.getMonth(), 1);
                    filtered = filtered.filter(approval => {
                        const approvalDate = new Date(approval.createdAt);
                        return approvalDate >= startDate;
                    });
                    break;
                    
                case 'custom':
                    if (this.dateRangeStart || this.dateRangeEnd) {
                        filtered = filtered.filter(approval => {
                            const approvalDate = new Date(approval.createdAt);
                            
                            if (this.dateRangeStart && this.dateRangeEnd) {
                                // 设置结束日期为当天的23:59:59
                                const endDate = new Date(this.dateRangeEnd);
                                endDate.setHours(23, 59, 59, 999);
                                return approvalDate >= this.dateRangeStart && approvalDate <= endDate;
                            } else if (this.dateRangeStart) {
                                return approvalDate >= this.dateRangeStart;
                            } else if (this.dateRangeEnd) {
                                // 设置结束日期为当天的23:59:59
                                const endDate = new Date(this.dateRangeEnd);
                                endDate.setHours(23, 59, 59, 999);
                                return approvalDate <= endDate;
                            }
                            
                            return true;
                        });
                    }
                    break;
            }
        }
        
        // 更新筛选后的数据
        this.filteredApprovals = filtered;
        
        // 更新总页数
        this.totalPages = Math.ceil(this.filteredApprovals.length / this.itemsPerPage);
        if (this.totalPages === 0) this.totalPages = 1;
        
        // 确保当前页码有效
        if (this.currentPage > this.totalPages) {
            this.currentPage = this.totalPages;
        }
        
        // 渲染审核列表
        this.renderApprovals();
    }

    /**
     * 更新统计数据
     */
    updateStats() {
        // 重置统计数据
        this.stats = {
            pending: 0,
            approved: 0,
            rejected: 0,
            todayProcessed: 0
        };
        
        // 获取今天的日期（不包含时间）
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        // 统计各状态的数量
        this.approvals.forEach(approval => {
            // 统计状态数量
            if (approval.status === 'pending') {
                this.stats.pending++;
            } else if (approval.status === 'approved') {
                this.stats.approved++;
            } else if (approval.status === 'rejected') {
                this.stats.rejected++;
            }
            
            // 统计今日处理的数量
            if (approval.status !== 'pending') {
                const processedDate = new Date(approval.processedAt || approval.updatedAt);
                processedDate.setHours(0, 0, 0, 0);
                
                if (processedDate.getTime() === today.getTime()) {
                    this.stats.todayProcessed++;
                }
            }
        });
        
        // 更新统计卡片
        document.getElementById('pending-count').textContent = this.stats.pending;
        document.getElementById('approved-count').textContent = this.stats.approved;
        document.getElementById('rejected-count').textContent = this.stats.rejected;
        document.getElementById('today-processed-count').textContent = this.stats.todayProcessed;
    }

    /**
     * 渲染审核列表
     */
    renderApprovals() {
        const tableBody = document.getElementById('approvals-table-body');
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const pageItems = this.filteredApprovals.slice(startIndex, endIndex);
        
        // 清空表格内容
        tableBody.innerHTML = '';
        
        // 检查是否有数据
        if (pageItems.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center py-4">
                        <div class="empty-state">
                            <div class="empty-state-icon">
                                <i class="fas fa-clipboard-list"></i>
                            </div>
                            <p class="empty-state-text">暂无符合条件的审核记录</p>
                        </div>
                    </td>
                </tr>
            `;
            
            // 更新分页
            this.renderPagination();
            return;
        }
        
        // 渲染数据
        pageItems.forEach(approval => {
            const row = document.createElement('tr');
            
            // 创建选择框单元格
            const checkboxCell = document.createElement('td');
            checkboxCell.className = 'text-center';
            
            // 只有待审核的项目可以选择
            if (approval.status === 'pending') {
                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.className = 'form-check-input approval-checkbox';
                checkbox.setAttribute('data-id', approval.id);
                
                // 如果在已选列表中，设置为选中状态
                if (this.selectedApprovals.has(approval.id)) {
                    checkbox.checked = true;
                }
                
                // 添加选择事件
                checkbox.addEventListener('change', (e) => {
                    if (e.target.checked) {
                        this.selectedApprovals.add(approval.id);
                    } else {
                        this.selectedApprovals.delete(approval.id);
                    }
                    
                    this.updateBulkActionButton();
                });
                
                checkboxCell.appendChild(checkbox);
            }
            
            row.appendChild(checkboxCell);
            
            // 创建ID单元格
            const idCell = document.createElement('td');
            idCell.textContent = approval.id.substring(0, 8) + '...';
            idCell.title = approval.id;
            row.appendChild(idCell);
            
            // 创建类型单元格
            const typeCell = document.createElement('td');
            const typeSpan = document.createElement('span');
            typeSpan.className = `approval-type ${approval.type}`;
            typeSpan.textContent = this.getApprovalTypeName(approval.type);
            typeCell.appendChild(typeSpan);
            row.appendChild(typeCell);
            
            // 创建申请人单元格
            const applicantCell = document.createElement('td');
            const applicantDiv = document.createElement('div');
            applicantDiv.className = 'd-flex align-items-center';
            
            // 创建申请人头像或占位符
            if (approval.applicant.avatar) {
                const avatar = document.createElement('img');
                avatar.src = approval.applicant.avatar;
                avatar.alt = approval.applicant.name;
                avatar.className = 'user-avatar';
                applicantDiv.appendChild(avatar);
            } else {
                const avatarPlaceholder = document.createElement('div');
                avatarPlaceholder.className = 'user-avatar-placeholder';
                avatarPlaceholder.textContent = approval.applicant.name.charAt(0).toUpperCase();
                applicantDiv.appendChild(avatarPlaceholder);
            }
            
            // 创建申请人名称
            const applicantName = document.createElement('span');
            applicantName.textContent = approval.applicant.name;
            applicantDiv.appendChild(applicantName);
            
            applicantCell.appendChild(applicantDiv);
            row.appendChild(applicantCell);
            
            // 创建目标单元格
            const targetCell = document.createElement('td');
            targetCell.textContent = approval.target.name;
            row.appendChild(targetCell);
            
            // 创建申请时间单元格
            const createdAtCell = document.createElement('td');
            createdAtCell.textContent = this.formatDate(approval.createdAt);
            createdAtCell.title = new Date(approval.createdAt).toLocaleString();
            row.appendChild(createdAtCell);
            
            // 创建状态单元格
            const statusCell = document.createElement('td');
            const statusSpan = document.createElement('span');
            statusSpan.className = `approval-status ${approval.status}`;
            statusSpan.textContent = this.getStatusText(approval.status);
            statusCell.appendChild(statusSpan);
            row.appendChild(statusCell);
            
            // 创建操作单元格
            const actionCell = document.createElement('td');
            
            // 查看详情按钮
            const viewButton = document.createElement('button');
            viewButton.type = 'button';
            viewButton.className = 'btn btn-sm btn-outline-primary me-1';
            viewButton.innerHTML = '<i class="fas fa-eye"></i>';
            viewButton.title = '查看详情';
            viewButton.addEventListener('click', () => {
                this.showApprovalDetail(approval);
            });
            actionCell.appendChild(viewButton);
            
            // 如果是待审核状态，添加审批和拒绝按钮
            if (approval.status === 'pending') {
                // 审批按钮
                const approveButton = document.createElement('button');
                approveButton.type = 'button';
                approveButton.className = 'btn btn-sm btn-success me-1';
                approveButton.innerHTML = '<i class="fas fa-check"></i>';
                approveButton.title = '批准';
                approveButton.addEventListener('click', () => {
                    this.showApproveModal(approval);
                });
                actionCell.appendChild(approveButton);
                
                // 拒绝按钮
                const rejectButton = document.createElement('button');
                rejectButton.type = 'button';
                rejectButton.className = 'btn btn-sm btn-danger';
                rejectButton.innerHTML = '<i class="fas fa-times"></i>';
                rejectButton.title = '拒绝';
                rejectButton.addEventListener('click', () => {
                    this.showRejectModal(approval);
                });
                actionCell.appendChild(rejectButton);
            }
            
            row.appendChild(actionCell);
            
            // 将行添加到表格
            tableBody.appendChild(row);
        });
        
        // 更新分页
        this.renderPagination();
        
        // 更新批量操作按钮状态
        this.updateBulkActionButton();
    }

    /**
     * 渲染分页
     */
    renderPagination() {
        const pagination = document.getElementById('pagination');
        pagination.innerHTML = '';
        
        // 如果只有一页，不显示分页
        if (this.totalPages <= 1) {
            return;
        }
        
        // 创建分页列表
        const ul = document.createElement('ul');
        ul.className = 'pagination justify-content-center';
        
        // 上一页按钮
        const prevLi = document.createElement('li');
        prevLi.className = `page-item ${this.currentPage === 1 ? 'disabled' : ''}`;
        
        const prevLink = document.createElement('a');
        prevLink.className = 'page-link';
        prevLink.href = '#';
        prevLink.setAttribute('data-page', this.currentPage - 1);
        prevLink.innerHTML = '&laquo;';
        
        prevLi.appendChild(prevLink);
        ul.appendChild(prevLi);
        
        // 页码按钮
        let startPage = Math.max(1, this.currentPage - 2);
        let endPage = Math.min(this.totalPages, startPage + 4);
        
        // 调整起始页，确保显示5个页码（如果有足够的页数）
        if (endPage - startPage < 4 && this.totalPages > 5) {
            startPage = Math.max(1, endPage - 4);
        }
        
        for (let i = startPage; i <= endPage; i++) {
            const pageLi = document.createElement('li');
            pageLi.className = `page-item ${i === this.currentPage ? 'active' : ''}`;
            
            const pageLink = document.createElement('a');
            pageLink.className = 'page-link';
            pageLink.href = '#';
            pageLink.setAttribute('data-page', i);
            pageLink.textContent = i;
            
            pageLi.appendChild(pageLink);
            ul.appendChild(pageLi);
        }
        
        // 下一页按钮
        const nextLi = document.createElement('li');
        nextLi.className = `page-item ${this.currentPage === this.totalPages ? 'disabled' : ''}`;
        
        const nextLink = document.createElement('a');
        nextLink.className = 'page-link';
        nextLink.href = '#';
        nextLink.setAttribute('data-page', this.currentPage + 1);
        nextLink.innerHTML = '&raquo;';
        
        nextLi.appendChild(nextLink);
        ul.appendChild(nextLi);
        
        // 添加分页到容器
        pagination.appendChild(ul);
    }

    /**
     * 跳转到指定页码
     * @param {number} page 页码
     */
    goToPage(page) {
        if (page < 1 || page > this.totalPages) {
            return;
        }
        
        this.currentPage = page;
        this.renderApprovals();
        
        // 滚动到表格顶部
        document.getElementById('approvals-table').scrollIntoView({ behavior: 'smooth' });
    }

    /**
     * 更新批量操作按钮状态
     */
    updateBulkActionButton() {
        const bulkApproveBtn = document.getElementById('bulk-approve');
        const bulkRejectBtn = document.getElementById('bulk-reject');
        
        if (this.selectedApprovals.size > 0) {
            bulkApproveBtn.disabled = false;
            bulkRejectBtn.disabled = false;
            
            // 更新按钮文本
            bulkApproveBtn.textContent = `批准 (${this.selectedApprovals.size})`;
            bulkRejectBtn.textContent = `拒绝 (${this.selectedApprovals.size})`;
        } else {
            bulkApproveBtn.disabled = true;
            bulkRejectBtn.disabled = true;
            
            // 恢复按钮文本
            bulkApproveBtn.textContent = '批准';
            bulkRejectBtn.textContent = '拒绝';
        }
    }

    /**
     * 显示审核详情
     * @param {Object} approval 审核对象
     */
    showApprovalDetail(approval) {
        // 获取模态框元素
        const modal = document.getElementById('approval-detail-modal');
        const modalTitle = modal.querySelector('.modal-title');
        const modalBody = modal.querySelector('.modal-body');
        
        // 设置模态框标题
        modalTitle.textContent = `审核详情 - ${this.getApprovalTypeName(approval.type)}`;
        
        // 构建详情内容
        let detailContent = `
            <div class="approval-detail-header">
                <div class="row">
                    <div class="col-md-6">
                        <h5 class="mb-3">基本信息</h5>
                        <div class="approval-detail-item">
                            <span class="approval-detail-label">审核ID：</span>
                            <span class="approval-detail-value">${approval.id}</span>
                        </div>
                        <div class="approval-detail-item">
                            <span class="approval-detail-label">审核类型：</span>
                            <span class="approval-detail-value">
                                <span class="approval-type ${approval.type}">${this.getApprovalTypeName(approval.type)}</span>
                            </span>
                        </div>
                        <div class="approval-detail-item">
                            <span class="approval-detail-label">申请时间：</span>
                            <span class="approval-detail-value">${new Date(approval.createdAt).toLocaleString()}</span>
                        </div>
                        <div class="approval-detail-item">
                            <span class="approval-detail-label">当前状态：</span>
                            <span class="approval-detail-value">
                                <span class="approval-status ${approval.status}">${this.getStatusText(approval.status)}</span>
                            </span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h5 class="mb-3">申请人信息</h5>
                        <div class="approval-detail-item">
                            <span class="approval-detail-label">申请人：</span>
                            <span class="approval-detail-value">${approval.applicant.name}</span>
                        </div>
                        <div class="approval-detail-item">
                            <span class="approval-detail-label">部门：</span>
                            <span class="approval-detail-value">${approval.applicant.department || '未设置'}</span>
                        </div>
                        <div class="approval-detail-item">
                            <span class="approval-detail-label">职位：</span>
                            <span class="approval-detail-value">${approval.applicant.position || '未设置'}</span>
                        </div>
                        <div class="approval-detail-item">
                            <span class="approval-detail-label">邮箱：</span>
                            <span class="approval-detail-value">${approval.applicant.email || '未设置'}</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // 添加目标信息
        detailContent += `
            <div class="approval-detail-section">
                <h6>目标信息</h6>
        `;
        
        // 根据不同的审核类型显示不同的目标信息
        switch (approval.type) {
            case 'project-create':
            case 'project-update':
                detailContent += `
                    <div class="approval-detail-item">
                        <span class="approval-detail-label">项目名称：</span>
                        <span class="approval-detail-value">${approval.target.name}</span>
                    </div>
                    <div class="approval-detail-item">
                        <span class="approval-detail-label">项目描述：</span>
                        <span class="approval-detail-value">${approval.target.description || '无'}</span>
                    </div>
                    <div class="approval-detail-item">
                        <span class="approval-detail-label">开始日期：</span>
                        <span class="approval-detail-value">${approval.target.startDate ? this.formatDate(approval.target.startDate) : '未设置'}</span>
                    </div>
                    <div class="approval-detail-item">
                        <span class="approval-detail-label">截止日期：</span>
                        <span class="approval-detail-value">${approval.target.endDate ? this.formatDate(approval.target.endDate) : '未设置'}</span>
                    </div>
                `;
                break;
                
            case 'user-role':
                detailContent += `
                    <div class="approval-detail-item">
                        <span class="approval-detail-label">用户名称：</span>
                        <span class="approval-detail-value">${approval.target.name}</span>
                    </div>
                    <div class="approval-detail-item">
                        <span class="approval-detail-label">当前角色：</span>
                        <span class="approval-detail-value">${approval.target.currentRole || '普通用户'}</span>
                    </div>
                    <div class="approval-detail-item">
                        <span class="approval-detail-label">申请角色：</span>
                        <span class="approval-detail-value">${approval.target.requestedRole}</span>
                    </div>
                `;
                break;
                
            case 'project-join':
                detailContent += `
                    <div class="approval-detail-item">
                        <span class="approval-detail-label">项目名称：</span>
                        <span class="approval-detail-value">${approval.target.name}</span>
                    </div>
                    <div class="approval-detail-item">
                        <span class="approval-detail-label">项目负责人：</span>
                        <span class="approval-detail-value">${approval.target.owner || '未知'}</span>
                    </div>
                    <div class="approval-detail-item">
                        <span class="approval-detail-label">申请角色：</span>
                        <span class="approval-detail-value">${approval.target.requestedRole || '成员'}</span>
                    </div>
                `;
                break;
                
            default:
                detailContent += `
                    <div class="approval-detail-item">
                        <span class="approval-detail-label">名称：</span>
                        <span class="approval-detail-value">${approval.target.name}</span>
                    </div>
                `;
        }
        
        detailContent += `
            </div>
        `;
        
        // 添加申请理由
        if (approval.reason) {
            detailContent += `
                <div class="approval-detail-section">
                    <h6>申请理由</h6>
                    <div class="card">
                        <div class="card-body">
                            ${approval.reason}
                        </div>
                    </div>
                </div>
            `;
        }
        
        // 添加审核历史
        detailContent += `
            <div class="approval-detail-section">
                <h6>审核历史</h6>
                <div class="approval-timeline">
        `;
        
        // 添加创建记录
        detailContent += `
            <div class="approval-timeline-item">
                <div class="approval-timeline-date">${new Date(approval.createdAt).toLocaleString()}</div>
                <div class="approval-timeline-content">
                    <strong>${approval.applicant.name}</strong> 提交了审核申请
                </div>
            </div>
        `;
        
        // 如果已处理，添加处理记录
        if (approval.status !== 'pending') {
            const processedDate = approval.processedAt || approval.updatedAt;
            const statusText = approval.status === 'approved' ? '批准' : '拒绝';
            
            detailContent += `
                <div class="approval-timeline-item ${approval.status}">
                    <div class="approval-timeline-date">${new Date(processedDate).toLocaleString()}</div>
                    <div class="approval-timeline-content">
                        <strong>${approval.processor?.name || '系统'}</strong> ${statusText}了此申请
                        ${approval.rejectReason ? `<div class="mt-2"><strong>拒绝理由：</strong>${approval.rejectReason}</div>` : ''}
                    </div>
                </div>
            `;
        }
        
        detailContent += `
                </div>
            </div>
        `;
        
        // 设置模态框内容
        modalBody.innerHTML = detailContent;
        
        // 显示模态框
        const modalInstance = new bootstrap.Modal(modal);
        modalInstance.show();
    }

    /**
     * 显示批准模态框
     * @param {Object} approval 审核对象
     */
    showApproveModal(approval) {
        // 获取模态框元素
        const modal = document.getElementById('approve-modal');
        const modalTitle = modal.querySelector('.modal-title');
        const approvalIdInput = document.getElementById('approve-approval-id');
        const confirmBtn = document.getElementById('confirm-approve');
        
        // 设置模态框标题和内容
        modalTitle.textContent = `批准审核 - ${this.getApprovalTypeName(approval.type)}`;
        approvalIdInput.value = approval.id;
        
        // 设置确认按钮事件
        confirmBtn.onclick = () => {
            this.processApprove(approval.id);
        };
        
        // 显示模态框
        const modalInstance = new bootstrap.Modal(modal);
        modalInstance.show();
    }

    /**
     * 显示拒绝模态框
     * @param {Object} approval 审核对象
     */
    showRejectModal(approval) {
        // 获取模态框元素
        const modal = document.getElementById('reject-modal');
        const modalTitle = modal.querySelector('.modal-title');
        const approvalIdInput = document.getElementById('reject-approval-id');
        const reasonInput = document.getElementById('reject-reason');
        const confirmBtn = document.getElementById('confirm-reject');
        
        // 设置模态框标题和内容
        modalTitle.textContent = `拒绝审核 - ${this.getApprovalTypeName(approval.type)}`;
        approvalIdInput.value = approval.id;
        reasonInput.value = '';
        
        // 设置确认按钮事件
        confirmBtn.onclick = () => {
            const reason = reasonInput.value.trim();
            this.processReject(approval.id, reason);
        };
        
        // 显示模态框
        const modalInstance = new bootstrap.Modal(modal);
        modalInstance.show();
    }

    /**
     * 显示批量批准模态框
     */
    showBulkApproveModal() {
        // 获取模态框元素
        const modal = document.getElementById('bulk-approve-modal');
        const countSpan = document.getElementById('bulk-approve-count');
        
        // 设置选中数量
        countSpan.textContent = this.selectedApprovals.size;
        
        // 显示模态框
        const modalInstance = new bootstrap.Modal(modal);
        modalInstance.show();
    }

    /**
     * 显示批量拒绝模态框
     */
    showBulkRejectModal() {
        // 获取模态框元素
        const modal = document.getElementById('bulk-reject-modal');
        const countSpan = document.getElementById('bulk-reject-count');
        const reasonInput = document.getElementById('bulk-reject-reason');
        
        // 设置选中数量和清空理由
        countSpan.textContent = this.selectedApprovals.size;
        reasonInput.value = '';
        
        // 显示模态框
        const modalInstance = new bootstrap.Modal(modal);
        modalInstance.show();
    }

    /**
     * 处理批准操作
     * @param {string} approvalId 审核ID
     */
    async processApprove(approvalId) {
        try {
            this.showLoading();
            
            // 调用API批准审核
            const response = await fetch(`${APP_CONFIG.API_BASE_URL}/api/approvals/${approvalId}/approve`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${appState.getToken()}`,
                    'Content-Type': 'application/json'
                }
            });
            
            if (!response.ok) {
                throw new Error('批准审核失败');
            }
            
            // 关闭模态框
            const modal = document.getElementById('approve-modal');
            const modalInstance = bootstrap.Modal.getInstance(modal);
            if (modalInstance) {
                modalInstance.hide();
            }
            
            // 重新加载审核数据
            await this.loadApprovals();
            
            // 更新统计数据
            this.updateStats();
            
            // 应用筛选条件
            this.applyFilters();
            
            this.showMessage('审核已批准', 'success');
            this.hideLoading();
        } catch (error) {
            console.error('批准审核出错:', error);
            this.showMessage('批准审核失败，请稍后重试', 'danger');
            this.hideLoading();
        }
    }

    /**
     * 处理拒绝操作
     * @param {string} approvalId 审核ID
     * @param {string} reason 拒绝理由
     */
    async processReject(approvalId, reason) {
        try {
            this.showLoading();
            
            // 调用API拒绝审核
            const response = await fetch(`${APP_CONFIG.API_BASE_URL}/api/approvals/${approvalId}/reject`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${appState.getToken()}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ reason })
            });
            
            if (!response.ok) {
                throw new Error('拒绝审核失败');
            }
            
            // 关闭模态框
            const modal = document.getElementById('reject-modal');
            const modalInstance = bootstrap.Modal.getInstance(modal);
            if (modalInstance) {
                modalInstance.hide();
            }
            
            // 重新加载审核数据
            await this.loadApprovals();
            
            // 更新统计数据
            this.updateStats();
            
            // 应用筛选条件
            this.applyFilters();
            
            this.showMessage('审核已拒绝', 'success');
            this.hideLoading();
        } catch (error) {
            console.error('拒绝审核出错:', error);
            this.showMessage('拒绝审核失败，请稍后重试', 'danger');
            this.hideLoading();
        }
    }

    /**
     * 处理批量批准操作
     */
    async processBulkApprove() {
        try {
            this.showLoading();
            
            // 获取所有选中的审核ID
            const approvalIds = Array.from(this.selectedApprovals);
            
            // 调用API批量批准审核
            const response = await fetch(`${APP_CONFIG.API_BASE_URL}/api/approvals/bulk/approve`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${appState.getToken()}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ approvalIds })
            });
            
            if (!response.ok) {
                throw new Error('批量批准审核失败');
            }
            
            // 关闭模态框
            const modal = document.getElementById('bulk-approve-modal');
            const modalInstance = bootstrap.Modal.getInstance(modal);
            if (modalInstance) {
                modalInstance.hide();
            }
            
            // 清空选中列表
            this.selectedApprovals.clear();
            
            // 重新加载审核数据
            await this.loadApprovals();
            
            // 更新统计数据
            this.updateStats();
            
            // 应用筛选条件
            this.applyFilters();
            
            this.showMessage(`已批准 ${approvalIds.length} 条审核`, 'success');
            this.hideLoading();
        } catch (error) {
            console.error('批量批准审核出错:', error);
            this.showMessage('批量批准审核失败，请稍后重试', 'danger');
            this.hideLoading();
        }
    }

    /**
     * 处理批量拒绝操作
     * @param {string} reason 拒绝理由
     */
    async processBulkReject(reason) {
        try {
            this.showLoading();
            
            // 获取所有选中的审核ID
            const approvalIds = Array.from(this.selectedApprovals);
            
            // 调用API批量拒绝审核
            const response = await fetch(`${APP_CONFIG.API_BASE_URL}/api/approvals/bulk/reject`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${appState.getToken()}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ approvalIds, reason })
            });
            
            if (!response.ok) {
                throw new Error('批量拒绝审核失败');
            }
            
            // 关闭模态框
            const modal = document.getElementById('bulk-reject-modal');
            const modalInstance = bootstrap.Modal.getInstance(modal);
            if (modalInstance) {
                modalInstance.hide();
            }
            
            // 清空选中列表
            this.selectedApprovals.clear();
            
            // 重新加载审核数据
            await this.loadApprovals();
            
            // 更新统计数据
            this.updateStats();
            
            // 应用筛选条件
            this.applyFilters();
            
            this.showMessage(`已拒绝 ${approvalIds.length} 条审核`, 'success');
            this.hideLoading();
        } catch (error) {
            console.error('批量拒绝审核出错:', error);
            this.showMessage('批量拒绝审核失败，请稍后重试', 'danger');
            this.hideLoading();
        }
    }

    /**
     * 获取审核类型名称
     * @param {string} type 审核类型
     * @returns {string} 审核类型名称
     */
    getApprovalTypeName(type) {
        const typeMap = {
            'project-create': '创建项目',
            'project-update': '更新项目',
            'user-role': '角色变更',
            'project-join': '加入项目'
        };
        
        return typeMap[type] || type;
    }

    /**
     * 获取状态文本
     * @param {string} status 状态
     * @returns {string} 状态文本
     */
    getStatusText(status) {
        const statusMap = {
            'pending': '待审核',
            'approved': '已批准',
            'rejected': '已拒绝'
        };
        
        return statusMap[status] || status;
    }

    /**
     * 格式化日期
     * @param {string} dateString 日期字符串
     * @returns {string} 格式化后的日期
     */
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString();
    }

    /**
     * 显示加载动画
     */
    showLoading() {
        const loadingEl = document.getElementById('loading-overlay');
        if (loadingEl) {
            loadingEl.style.display = 'flex';
        }
    }

    /**
     * 隐藏加载动画
     */
    hideLoading() {
        const loadingEl = document.getElementById('loading-overlay');
        if (loadingEl) {
            loadingEl.style.display = 'none';
        }
    }

    /**
     * 显示消息提示
     * @param {string} message 消息内容
     * @param {string} type 消息类型（success, danger, warning, info）
     */
    showMessage(message, type = 'info') {
        const alertsContainer = document.getElementById('alerts-container');
        
        // 创建警告元素
        const alertEl = document.createElement('div');
        alertEl.className = `alert alert-${type} alert-dismissible fade show`;
        alertEl.role = 'alert';
        alertEl.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="关闭"></button>
        `;
        
        // 添加到容器
        alertsContainer.appendChild(alertEl);
        
        // 5秒后自动关闭
        setTimeout(() => {
            alertEl.classList.remove('show');
            setTimeout(() => {
                alertEl.remove();
            }, 150);
        }, 5000);
    }
}

// 初始化审核管理器
let approvalManager;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    approvalManager = new ApprovalManager();
    approvalManager.init();
});

// 刷新审核列表的全局函数
function refreshApprovals() {
    if (approvalManager) {
        approvalManager.loadApprovals().then(() => {
            approvalManager.updateStats();
            approvalManager.applyFilters();
        });
    }
}