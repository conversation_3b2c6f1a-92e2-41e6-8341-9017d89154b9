/**
 * 登录页面JavaScript逻辑
 * 处理用户登录、注册、忘记密码等功能
 */

class LoginManager {
    constructor() {
        this.init();
    }

    /**
     * 初始化登录管理器
     */
    init() {
        this.setupEventListeners();
        this.checkExistingAuth();
        this.setupFormValidation();
    }

    /**
     * 检查是否已经登录
     */
    checkExistingAuth() {
        const token = localStorage.getItem(APP_CONFIG.TOKEN_KEY);
        if (token) {
            // 验证token是否有效
            this.validateTokenAndRedirect(token);
        }
    }

    /**
     * 验证token并重定向
     * @param {string} token - 访问令牌
     */
    async validateTokenAndRedirect(token) {
        try {
            const response = await fetch(`${APP_CONFIG.API_BASE_URL}/auth/me`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (response.ok) {
                // Token有效，重定向到主页
                window.location.href = '../index.html';
            } else {
                // Token无效，清除本地存储
                this.clearAuthData();
            }
        } catch (error) {
            console.error('Token验证失败:', error);
            this.clearAuthData();
        }
    }

    /**
     * 清除认证数据
     */
    clearAuthData() {
        localStorage.removeItem(APP_CONFIG.TOKEN_KEY);
        localStorage.removeItem(APP_CONFIG.USER_KEY);
        localStorage.removeItem(APP_CONFIG.REFRESH_TOKEN_KEY);
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 登录表单提交
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }

        // 密码显示/隐藏切换
        const togglePassword = document.getElementById('togglePassword');
        if (togglePassword) {
            togglePassword.addEventListener('click', () => this.togglePasswordVisibility());
        }

        // 注册表单验证
        const registerForm = document.getElementById('registerForm');
        if (registerForm) {
            const confirmPassword = document.getElementById('regConfirmPassword');
            confirmPassword.addEventListener('input', () => this.validatePasswordMatch());
        }

        // 回车键快捷登录
        document.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !document.querySelector('.modal.show')) {
                const loginBtn = document.getElementById('loginBtn');
                if (loginBtn && !loginBtn.disabled) {
                    loginForm.dispatchEvent(new Event('submit'));
                }
            }
        });
    }

    /**
     * 设置表单验证
     */
    setupFormValidation() {
        // Bootstrap表单验证
        const forms = document.querySelectorAll('.needs-validation');
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                if (!form.checkValidity()) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                form.classList.add('was-validated');
            });
        });
    }

    /**
     * 处理用户登录
     * @param {Event} event - 表单提交事件
     */
    async handleLogin(event) {
        event.preventDefault();
        
        const form = event.target;
        if (!form.checkValidity()) {
            form.classList.add('was-validated');
            return;
        }

        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value;
        const rememberMe = document.getElementById('rememberMe').checked;

        if (!username || !password) {
            this.showError('请输入用户名和密码');
            return;
        }

        try {
            this.setLoginLoading(true);
            this.hideAlerts();

            const response = await fetch(`${APP_CONFIG.API_BASE_URL}/auth/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username,
                    password,
                    rememberMe
                })
            });

            const data = await response.json();

            if (response.ok && data.success) {
                // 登录成功
                this.handleLoginSuccess(data.data);
            } else {
                // 登录失败
                this.showError(data.message || '登录失败，请检查用户名和密码');
            }
        } catch (error) {
            console.error('登录错误:', error);
            this.showError('网络错误，请稍后重试');
        } finally {
            this.setLoginLoading(false);
        }
    }

    /**
     * 处理登录成功
     * @param {Object} authData - 认证数据
     */
    handleLoginSuccess(authData) {
        const { user, token, refreshToken } = authData;
        
        // 保存认证信息
        localStorage.setItem(APP_CONFIG.TOKEN_KEY, token);
        localStorage.setItem(APP_CONFIG.USER_KEY, JSON.stringify(user));
        
        if (refreshToken) {
            localStorage.setItem(APP_CONFIG.REFRESH_TOKEN_KEY, refreshToken);
        }

        // 显示成功消息
        this.showSuccess(`欢迎回来，${user.realName || user.username}！`);

        // 延迟跳转，让用户看到成功消息
        setTimeout(() => {
            window.location.href = '../index.html';
        }, 1000);
    }

    /**
     * 设置登录按钮加载状态
     * @param {boolean} loading - 是否加载中
     */
    setLoginLoading(loading) {
        const loginBtn = document.getElementById('loginBtn');
        const btnText = loginBtn.querySelector('.btn-text');
        const btnLoading = loginBtn.querySelector('.btn-loading');

        if (loading) {
            loginBtn.disabled = true;
            btnText.classList.add('d-none');
            btnLoading.classList.remove('d-none');
        } else {
            loginBtn.disabled = false;
            btnText.classList.remove('d-none');
            btnLoading.classList.add('d-none');
        }
    }

    /**
     * 切换密码可见性
     */
    togglePasswordVisibility() {
        const passwordInput = document.getElementById('password');
        const toggleIcon = document.querySelector('#togglePassword i');

        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.classList.remove('fa-eye');
            toggleIcon.classList.add('fa-eye-slash');
        } else {
            passwordInput.type = 'password';
            toggleIcon.classList.remove('fa-eye-slash');
            toggleIcon.classList.add('fa-eye');
        }
    }

    /**
     * 验证密码匹配
     */
    validatePasswordMatch() {
        const password = document.getElementById('regPassword').value;
        const confirmPassword = document.getElementById('regConfirmPassword');
        
        if (password !== confirmPassword.value) {
            confirmPassword.setCustomValidity('两次密码输入不一致');
        } else {
            confirmPassword.setCustomValidity('');
        }
    }

    /**
     * 显示错误消息
     * @param {string} message - 错误消息
     */
    showError(message) {
        const errorAlert = document.getElementById('errorAlert');
        const errorMessage = document.getElementById('errorMessage');
        
        errorMessage.textContent = message;
        errorAlert.classList.remove('d-none');
        
        // 自动隐藏错误消息
        setTimeout(() => {
            errorAlert.classList.add('d-none');
        }, 5000);
    }

    /**
     * 显示成功消息
     * @param {string} message - 成功消息
     */
    showSuccess(message) {
        const successAlert = document.getElementById('successAlert');
        const successMessage = document.getElementById('successMessage');
        
        successMessage.textContent = message;
        successAlert.classList.remove('d-none');
    }

    /**
     * 隐藏所有警告框
     */
    hideAlerts() {
        document.getElementById('errorAlert').classList.add('d-none');
        document.getElementById('successAlert').classList.add('d-none');
    }

    /**
     * 处理用户注册
     * @param {Object} userData - 用户注册数据
     */
    async handleRegister(userData) {
        try {
            const response = await fetch(`${APP_CONFIG.API_BASE_URL}/auth/register`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(userData)
            });

            const data = await response.json();

            if (response.ok && data.success) {
                // 注册成功
                this.showSuccess('注册成功！请使用新账户登录。');
                
                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('registerModal'));
                modal.hide();
                
                // 清空注册表单
                document.getElementById('registerForm').reset();
                document.getElementById('registerForm').classList.remove('was-validated');
                
                // 自动填充用户名
                document.getElementById('username').value = userData.username;
            } else {
                throw new Error(data.message || '注册失败');
            }
        } catch (error) {
            console.error('注册错误:', error);
            alert('注册失败：' + error.message);
        }
    }

    /**
     * 处理忘记密码
     * @param {string} email - 邮箱地址
     */
    async handleForgotPassword(email) {
        try {
            const response = await fetch(`${APP_CONFIG.API_BASE_URL}/auth/forgot-password`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ email })
            });

            const data = await response.json();

            if (response.ok && data.success) {
                alert('重置密码链接已发送到您的邮箱，请查收。');
                
                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('forgotPasswordModal'));
                modal.hide();
                
                // 清空表单
                document.getElementById('forgotPasswordForm').reset();
            } else {
                throw new Error(data.message || '发送重置链接失败');
            }
        } catch (error) {
            console.error('忘记密码错误:', error);
            alert('发送失败：' + error.message);
        }
    }
}

// 全局函数
/**
 * 显示注册模态框
 */
function showRegister() {
    const modal = new bootstrap.Modal(document.getElementById('registerModal'));
    modal.show();
}

/**
 * 显示忘记密码模态框
 */
function showForgotPassword() {
    const modal = new bootstrap.Modal(document.getElementById('forgotPasswordModal'));
    modal.show();
}

/**
 * 处理注册表单提交
 */
function handleRegister() {
    const form = document.getElementById('registerForm');
    
    if (!form.checkValidity()) {
        form.classList.add('was-validated');
        return;
    }

    const userData = {
        username: document.getElementById('regUsername').value.trim(),
        email: document.getElementById('regEmail').value.trim(),
        password: document.getElementById('regPassword').value,
        realName: document.getElementById('regRealName').value.trim()
    };

    // 验证密码匹配
    const confirmPassword = document.getElementById('regConfirmPassword').value;
    if (userData.password !== confirmPassword) {
        alert('两次密码输入不一致');
        return;
    }

    loginManager.handleRegister(userData);
}

/**
 * 处理忘记密码表单提交
 */
function handleForgotPassword() {
    const email = document.getElementById('resetEmail').value.trim();
    
    if (!email) {
        alert('请输入邮箱地址');
        return;
    }

    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        alert('请输入有效的邮箱地址');
        return;
    }

    loginManager.handleForgotPassword(email);
}

// 页面加载完成后初始化
let loginManager;

document.addEventListener('DOMContentLoaded', () => {
    loginManager = new LoginManager();
    
    // 添加一些用户体验增强
    
    // 自动聚焦到用户名输入框
    const usernameInput = document.getElementById('username');
    if (usernameInput) {
        setTimeout(() => usernameInput.focus(), 100);
    }
    
    // 添加输入框动画效果
    const inputs = document.querySelectorAll('.form-control');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            if (!this.value) {
                this.parentElement.classList.remove('focused');
            }
        });
    });
    
    // 记住用户名功能
    const savedUsername = localStorage.getItem('remembered_username');
    if (savedUsername) {
        document.getElementById('username').value = savedUsername;
        document.getElementById('rememberMe').checked = true;
    }
    
    // 保存用户名
    document.getElementById('rememberMe').addEventListener('change', function() {
        const username = document.getElementById('username').value.trim();
        if (this.checked && username) {
            localStorage.setItem('remembered_username', username);
        } else {
            localStorage.removeItem('remembered_username');
        }
    });
});

// 导出到全局
window.loginManager = loginManager;
window.showRegister = showRegister;
window.showForgotPassword = showForgotPassword;
window.handleRegister = handleRegister;
window.handleForgotPassword = handleForgotPassword;