<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>项目管理系统</title>
    <meta name="description" content="一个现代化的项目管理系统，支持任务管理、团队协作和进度跟踪">
    <meta name="keywords" content="项目管理,任务管理,团队协作,进度跟踪">
    <meta name="author" content="项目管理系统">
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#007bff">
    <meta name="msapplication-TileColor" content="#007bff">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="项目管理">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="application-name" content="项目管理系统">
    
    <!-- Favicon and Icons -->
    <link rel="icon" type="image/svg+xml" href="/assets/images/logo.svg">
    <link rel="icon" type="image/png" sizes="32x32" href="/assets/images/icons/icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/assets/images/icons/icon-16x16.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/images/icons/icon-180x180.png">
    <link rel="mask-icon" href="/assets/images/logo.svg" color="#007bff">
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json">
    
    <!-- Preconnect for performance -->
    <link rel="preconnect" href="https://cdn.jsdelivr.net">
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- 自定义样式 -->
    <link href="assets/css/main.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-project-diagram me-2"></i>
                项目管理系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#dashboard">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="pages/projects.html">
                            <i class="fas fa-folder me-1"></i>项目
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="pages/tasks.html">
                            <i class="fas fa-tasks me-1"></i>任务
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="pages/reports.html">
                            <i class="fas fa-chart-bar me-1"></i>报告
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="pages/approvals.html">
                            <i class="fas fa-check-circle me-1"></i>审核
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="pages/users.html">
                            <i class="fas fa-users me-1"></i>用户
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>
                            用户名
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#profile">个人资料</a></li>
                            <li><a class="dropdown-item" href="#settings">设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()">退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="container-fluid mt-4">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#dashboard">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#projects">
                                <i class="fas fa-folder me-2"></i>
                                我的项目
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#tasks">
                                <i class="fas fa-tasks me-2"></i>
                                我的任务
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#reports">
                                <i class="fas fa-chart-bar me-2"></i>
                                报告管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#approvals">
                                <i class="fas fa-check-circle me-2"></i>
                                待审核
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div id="app-content">
                    <!-- 动态内容将在这里加载 -->
                    <div id="dashboard-content">
                        <h1 class="h2">仪表板</h1>
                        <div class="row">
                            <div class="col-md-3 mb-4">
                                <div class="card text-white bg-primary">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h4 class="card-title">总项目数</h4>
                                                <h2 id="total-projects">0</h2>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="fas fa-folder fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-4">
                                <div class="card text-white bg-success">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h4 class="card-title">进行中任务</h4>
                                                <h2 id="active-tasks">0</h2>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="fas fa-tasks fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-4">
                                <div class="card text-white bg-warning">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h4 class="card-title">待审核</h4>
                                                <h2 id="pending-approvals">0</h2>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="fas fa-clock fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-4">
                                <div class="card text-white bg-info">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h4 class="card-title">团队成员</h4>
                                                <h2 id="team-members">0</h2>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="fas fa-users fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 最近项目 -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">最近项目</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>项目名称</th>
                                                        <th>状态</th>
                                                        <th>进度</th>
                                                        <th>截止日期</th>
                                                        <th>操作</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="recent-projects">
                                                    <!-- 动态加载项目数据 -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </main>

    <!-- PWA安装提示 -->
    <div id="pwa-install-banner" class="alert alert-info alert-dismissible fade" role="alert" style="position: fixed; top: 0; left: 0; right: 0; z-index: 1050; display: none;">
        <div class="container">
            <div class="d-flex align-items-center">
                <i class="fas fa-download me-2"></i>
                <span class="flex-grow-1">安装此应用到您的设备，获得更好的体验！</span>
                <button type="button" class="btn btn-sm btn-outline-primary me-2" id="pwa-install-btn">
                    <i class="fas fa-plus me-1"></i>安装
                </button>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    </div>

    <!-- 离线提示 -->
    <div id="offline-banner" class="alert alert-warning alert-dismissible fade" role="alert" style="position: fixed; bottom: 0; left: 0; right: 0; z-index: 1050; display: none;">
        <div class="container">
            <div class="d-flex align-items-center">
                <i class="fas fa-wifi me-2"></i>
                <span class="flex-grow-1">您当前处于离线模式，某些功能可能不可用</span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    </div>

    <!-- 更新提示 -->
    <div id="update-banner" class="alert alert-success alert-dismissible fade" role="alert" style="position: fixed; bottom: 0; left: 0; right: 0; z-index: 1050; display: none;">
        <div class="container">
            <div class="d-flex align-items-center">
                <i class="fas fa-sync-alt me-2"></i>
                <span class="flex-grow-1">应用有新版本可用</span>
                <button type="button" class="btn btn-sm btn-outline-success me-2" id="update-btn">
                    <i class="fas fa-refresh me-1"></i>更新
                </button>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Service Worker 注册 -->
    <script>
        // Service Worker 注册
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then(registration => {
                        console.log('Service Worker 注册成功:', registration.scope);
                        
                        // 检查更新
                        registration.addEventListener('updatefound', () => {
                            const newWorker = registration.installing;
                            newWorker.addEventListener('statechange', () => {
                                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                    // 显示更新提示
                                    showUpdateBanner();
                                }
                            });
                        });
                    })
                    .catch(error => {
                        console.error('Service Worker 注册失败:', error);
                    });
            });
        }

        // PWA 安装提示
        let deferredPrompt;
        window.addEventListener('beforeinstallprompt', (e) => {
            // 阻止默认的安装提示
            e.preventDefault();
            deferredPrompt = e;
            
            // 显示自定义安装提示
            showInstallBanner();
        });

        // 显示安装横幅
        function showInstallBanner() {
            const banner = document.getElementById('pwa-install-banner');
            const installBtn = document.getElementById('pwa-install-btn');
            
            if (banner && installBtn) {
                banner.style.display = 'block';
                banner.classList.add('show');
                
                installBtn.addEventListener('click', async () => {
                    if (deferredPrompt) {
                        deferredPrompt.prompt();
                        const { outcome } = await deferredPrompt.userChoice;
                        console.log('PWA 安装结果:', outcome);
                        deferredPrompt = null;
                        hideBanner('pwa-install-banner');
                    }
                });
            }
        }

        // 显示更新横幅
        function showUpdateBanner() {
            const banner = document.getElementById('update-banner');
            const updateBtn = document.getElementById('update-btn');
            
            if (banner && updateBtn) {
                banner.style.display = 'block';
                banner.classList.add('show');
                
                updateBtn.addEventListener('click', () => {
                    // 通知 Service Worker 跳过等待
                    if (navigator.serviceWorker.controller) {
                        navigator.serviceWorker.controller.postMessage({ type: 'SKIP_WAITING' });
                    }
                    window.location.reload();
                });
            }
        }

        // 隐藏横幅
        function hideBanner(bannerId) {
            const banner = document.getElementById(bannerId);
            if (banner) {
                banner.classList.remove('show');
                setTimeout(() => {
                    banner.style.display = 'none';
                }, 150);
            }
        }

        // 网络状态监听
        window.addEventListener('online', () => {
            hideBanner('offline-banner');
            console.log('网络已连接');
        });

        window.addEventListener('offline', () => {
            const banner = document.getElementById('offline-banner');
            if (banner) {
                banner.style.display = 'block';
                banner.classList.add('show');
            }
            console.log('网络已断开');
        });

        // PWA 安装成功监听
        window.addEventListener('appinstalled', () => {
            console.log('PWA 安装成功');
            hideBanner('pwa-install-banner');
            // 可以在这里显示感谢消息或引导用户
        });
    </script>
    
    <!-- 自定义JavaScript -->
     <script src="assets/js/main.js"></script>
     <script src="assets/js/api/auth.js"></script>
</body>
</html>